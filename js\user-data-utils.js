/**
 * Utility functions for consistent user data access
 * This file provides standardized methods to access user data fields
 * with proper fallbacks to ensure consistent display across the application
 */

/**
 * Standardizes user data access with proper fallbacks
 * @param {Object} userData - The raw user data from Firestore
 * @param {Object} authUser - Optional Firebase Auth user object for fallbacks
 * @returns {Object} Standardized user data object
 */
export function standardizeUserData(userData, authUser = null) {
    console.log('Standardizing user data:', userData ? 'User data exists' : 'User data is null/undefined');

    if (!userData) {
        console.warn('userData is null or undefined, returning default values');
        return {
            fullName: authUser?.displayName || 'User',
            email: authUser?.email || 'N/A',
            phoneNumber: 'N/A',
            referralCode: 'N/A',
            referredBy: null,
            wallet: {
                earning: 0,
                bonus: 0,
                balance: 0
            },
            plan: {
                name: 'Trial',
                dailyLimit: 100,
                rate: 0.1, // Updated to match the Trial plan rate in plan-manager.js
                duration: 2,
                earningsPerBatch: 10
            },
            status: 'active',
            createdAt: null,
            stats: {
                totalTranslations: 0,
                todayTranslations: 0,
                totalEarnings: 0,
                totalReferrals: 0
            }
        };
    }

    // Standardize wallet data
    const wallet = {
        earning: 0,
        bonus: 0,
        balance: 0
    };

    // Handle different wallet structures
    if (userData.wallet) {
        wallet.earning = userData.wallet.earning || 0;
        wallet.bonus = userData.wallet.bonus || 0;

        // Handle inconsistent naming (balance vs main)
        wallet.balance = userData.wallet.balance || userData.wallet.main || 0;
    } else {
        // Handle flat wallet fields
        wallet.balance = userData.walletBalance || userData.balance || 0;
    }

    // Standardize plan data
    let plan = {
        name: 'Trial',
        dailyLimit: 100,
        rate: 0.1, // Updated to match the Trial plan rate in plan-manager.js
        duration: 2,
        earningsPerBatch: 10
    };

    // Handle different plan structures
    if (userData.plan) {
        console.log('Plan data found:', typeof userData.plan, userData.plan);
        if (typeof userData.plan === 'string') {
            plan.name = userData.plan;
            console.log('Plan is a string:', plan.name);
        } else if (typeof userData.plan === 'object') {
            plan.name = userData.plan.name || 'Trial';
            plan.dailyLimit = userData.plan.dailyLimit || 100;
            plan.rate = userData.plan.rate || 0.1; // Updated to match the Trial plan rate in plan-manager.js
            plan.duration = userData.plan.duration || 30;
            plan.earningsPerBatch = userData.plan.earningsPerBatch || 10;
            console.log('Plan is an object:', plan);

            // Copy any additional plan properties
            Object.assign(plan, userData.plan);
        } else {
            console.warn('Unexpected plan type:', typeof userData.plan);
        }
    } else {
        console.log('No plan data found, using defaults');
    }

    // Standardize stats data
    const stats = {
        totalVideosCompleted: 0,
        totalTranslationsCompleted: 0, // Keep for backward compatibility
        totalTranslations: 0, // Keep for backward compatibility
        todayVideos: 0,
        todayTranslations: 0, // Keep for backward compatibility
        totalEarnings: 0,
        totalReferrals: 0
    };

    // Handle different stats structures
    if (userData.stats) {
        // For total videos:
        // 1. First try stats.totalVideosCompleted (primary field)
        // 2. Then try stats.totalTranslationsCompleted (backward compatibility)
        // 3. Then try videosCount or translationsCount (legacy field at root level)
        // 4. Fall back to 0 if none exists
        stats.totalVideosCompleted = userData.stats.totalVideosCompleted ||
                                    userData.stats.totalTranslationsCompleted ||
                                    userData.videosCount ||
                                    userData.translationsCount || 0;

        // Set backward compatibility fields
        stats.totalTranslationsCompleted = stats.totalVideosCompleted;
        stats.totalTranslations = stats.totalVideosCompleted;

        // For today's videos:
        // 1. First try stats.todayVideos
        // 2. Then try stats.dailyVideos.count
        // 3. Check if dailyVideos is at the root level (date-based structure)
        // 4. Then try stats.todayTranslations (backward compatibility)
        // 5. Then try stats.dailyTranslations.count (backward compatibility)
        // 6. Fall back to 0 if none exists

        // First check the standard locations
        stats.todayVideos = userData.stats.todayVideos ||
                           userData.stats.dailyVideos?.count ||
                           userData.stats.todayTranslations ||
                           userData.stats.dailyTranslations?.count || 0;

        // If we still don't have a value, check if dailyVideos is at the root level
        if (stats.todayVideos === 0 && userData.dailyVideos && typeof userData.dailyVideos === 'object') {
            // Find the most recent date entry
            const dates = Object.keys(userData.dailyVideos);
            if (dates.length > 0) {
                // Sort dates in descending order (newest first)
                dates.sort((a, b) => new Date(b) - new Date(a));
                // Get the count from the most recent date
                stats.todayVideos = userData.dailyVideos[dates[0]] || 0;
                console.log(`Found today's videos in root dailyVideos object for date ${dates[0]}: ${stats.todayVideos}`);
            }
        }

        // Set backward compatibility field
        stats.todayTranslations = stats.todayVideos;

        // Other stats
        stats.totalEarnings = userData.stats.totalEarnings || 0;
        stats.totalReferrals = userData.stats.totalReferrals || userData.referralCount || 0;
    } else {
        // If stats object is missing, try to get values from root level fields
        stats.totalVideosCompleted = userData.videosCount || userData.translationsCount || 0;
        stats.totalTranslationsCompleted = stats.totalVideosCompleted; // For backward compatibility
        stats.totalTranslations = stats.totalVideosCompleted; // For backward compatibility

        stats.todayVideos = 0; // No equivalent at root level
        stats.todayTranslations = 0; // For backward compatibility

        stats.totalEarnings = userData.totalEarnings || 0;
        stats.totalReferrals = userData.referralCount || 0;
    }

    // Return standardized user data
    return {
        // Basic user info
        fullName: userData.fullName || userData.fullname || userData.name ||
                 userData.displayName || authUser?.displayName || 'User',
        email: userData.email || authUser?.email || 'N/A',
        phoneNumber: userData.phoneNumber || userData.mobile || userData.phone || 'N/A',
        city: userData.city || 'N/A',

        // Referral info
        referralCode: userData.referralCode || 'N/A',
        // Fixed handling of referredBy field with detailed logging
        referredBy: (() => {
            console.log('Processing referredBy in standardizeUserData');

            // Check if referredBy exists and has a value
            if ('referredBy' in userData && userData.referredBy !== null && userData.referredBy !== undefined) {
                console.log('referredBy exists in userData with value:', userData.referredBy);
                console.log('referredBy type:', typeof userData.referredBy);

                // Check for invalid values (0, "0", "ZERO", empty string)
                if (userData.referredBy === 0 ||
                    userData.referredBy === '0' ||
                    userData.referredBy === '' ||
                    (typeof userData.referredBy === 'string' && userData.referredBy.trim().toUpperCase() === 'ZERO')) {
                    console.log('referredBy is invalid (0, "0", "ZERO", or empty), returning empty string');
                    return '';
                }

                // Return the valid value (ensure it's a string)
                const referredByValue = String(userData.referredBy).trim();
                console.log('referredBy is valid, returning:', referredByValue);
                return referredByValue;
            } else {
                console.log('referredBy does not exist or is null/undefined in userData, returning empty string');
                return '';
            }
        })(),

        // Wallet, plan, and stats
        wallet,
        plan,
        stats,

        // Status and dates
        status: userData.status || 'active',
        createdAt: userData.createdAt || null,

        // Bank details
        bankDetails: userData.bankDetails || {
            bank: '',
            account: '',
            ifsc: '',
            holder: ''
        },

        // Copy any additional fields from the original data
        ...userData,

        // Override with standardized fields
        _standardized: true
    };
}

/**
 * Formats currency values consistently
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency symbol (default: ₹)
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount, currency = '₹') {
    return `${currency}${parseFloat(amount || 0).toFixed(2)}`;
}

/**
 * Formats date values consistently
 * @param {Object} timestamp - Firestore timestamp or Date object
 * @returns {string} Formatted date string or 'N/A' if invalid
 */
export function formatDate(timestamp) {
    if (!timestamp) return 'N/A';

    try {
        // Handle Firestore Timestamp
        const date = timestamp.toDate ? timestamp.toDate() :
                    (timestamp instanceof Date ? timestamp : new Date(timestamp));

        return date.toLocaleDateString('en-IN', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        });
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'N/A';
    }
}

/**
 * Standardizes transaction data with proper fallbacks
 * @param {Object} transaction - The raw transaction data from Firestore
 * @returns {Object} Standardized transaction object
 */
export function standardizeTransactionData(transaction) {
    if (!transaction) {
        return {
            type: 'unknown',
            amount: 0,
            status: 'unknown',
            timestamp: new Date(),
            description: 'Unknown transaction'
        };
    }

    // Log the raw transaction for debugging
    console.log('Raw transaction data:', JSON.stringify(transaction));

    // Standardize timestamp/createdAt
    let timestamp;
    try {
        if (transaction.timestamp && typeof transaction.timestamp.toDate === 'function') {
            timestamp = transaction.timestamp.toDate();
            console.log('Using timestamp.toDate()');
        } else if (transaction.createdAt && typeof transaction.createdAt.toDate === 'function') {
            timestamp = transaction.createdAt.toDate();
            console.log('Using createdAt.toDate()');
        } else if (transaction.timestamp && transaction.timestamp._seconds) {
            // Handle Firestore timestamp in serialized form
            timestamp = new Date(transaction.timestamp._seconds * 1000);
            console.log('Using timestamp._seconds');
        } else if (transaction.createdAt && transaction.createdAt._seconds) {
            // Handle Firestore timestamp in serialized form
            timestamp = new Date(transaction.createdAt._seconds * 1000);
            console.log('Using createdAt._seconds');
        } else if (transaction.timestamp) {
            timestamp = new Date(transaction.timestamp);
            console.log('Using new Date(timestamp)');
        } else if (transaction.createdAt) {
            timestamp = new Date(transaction.createdAt);
            console.log('Using new Date(createdAt)');
        } else if (transaction.date) {
            // Some transactions might use 'date' field
            timestamp = new Date(transaction.date);
            console.log('Using date field');
        } else {
            timestamp = new Date();
            console.log('Using current date as fallback');
        }
    } catch (error) {
        console.error('Error parsing timestamp:', error);
        timestamp = new Date(); // Fallback to current date
    }

    // Standardize transaction type
    let type = transaction.type || 'unknown';
    let typeDisplay = '';

    // Log the original transaction type
    console.log('Original transaction type:', type);

    // Normalize type to lowercase for consistent comparison
    if (typeof type === 'string') {
        type = type.toLowerCase();
        console.log('Normalized transaction type:', type);
    }

    switch (type) {
        case 'work':
        case 'translation':
        case 'translations':
        case 'video':
        case 'videos':
            typeDisplay = 'Videos Submitted';
            type = 'work'; // Normalize type
            break;
        case 'earnings':
        case 'credit':
        case 'translation earnings':
        case 'translation_earnings':
        case 'video earnings':
        case 'video_earnings':
        case 'earning':
            typeDisplay = 'Earnings';
            type = 'earnings'; // Normalize type
            break;
        case 'transfer-earning':
        case 'transfer_earning':
        case 'transfer-earnings':
        case 'transfer_earnings':
            typeDisplay = 'Transferred from Earning Wallet';
            type = 'transfer'; // Normalize type
            break;
        case 'transfer-bonus':
        case 'transfer_bonus':
            typeDisplay = 'Transferred from Bonus Wallet';
            type = 'transfer'; // Normalize type
            break;
        case 'withdrawal':
        case 'debit':
        case 'withdraw':
            typeDisplay = 'Withdrawal';
            type = 'withdrawal'; // Normalize type
            break;
        case 'withdrawal_cancelled':
        case 'withdrawal cancelled':
            typeDisplay = 'Withdrawal Cancelled';
            type = 'withdrawal'; // Group with withdrawals
            break;
        case 'withdrawal_rejected':
        case 'withdrawal rejected':
            typeDisplay = 'Withdrawal Rejected';
            type = 'withdrawal'; // Group with withdrawals
            break;
        case 'referral':
        case 'referral_bonus':
        case 'referral bonus':
            typeDisplay = 'Referral Bonus';
            type = 'referral'; // Normalize type
            break;
        case 'plan':
        case 'plan_activated':
        case 'plan_activation':
        case 'plan activated':
        case 'plan activation':
            typeDisplay = 'Plan Activated';
            type = 'plan'; // Normalize type
            break;
        case 'transfer':
        case 'wallet_transfer':
        case 'wallet transfer':
            typeDisplay = transaction.description || 'Wallet Transfer';
            type = 'transfer'; // Normalize type
            break;
        case 'bonus':
        case 'bonus_credit':
        case 'bonus credit':
            typeDisplay = 'Bonus Credit';
            type = 'bonus'; // Normalize type
            break;
        default:
            // Try to categorize unknown types based on description
            if (transaction.description) {
                const desc = transaction.description.toLowerCase();
                if (desc.includes('plan') || desc.includes('activated')) {
                    typeDisplay = 'Plan Activated';
                    type = 'plan';
                } else if (desc.includes('referral') || desc.includes('bonus')) {
                    typeDisplay = 'Referral Bonus';
                    type = 'referral';
                } else if (desc.includes('work') || desc.includes('translation') || desc.includes('video')) {
                    typeDisplay = 'Videos Submitted';
                    type = 'work';
                } else if (desc.includes('earning') || desc.includes('credit')) {
                    typeDisplay = 'Earnings';
                    type = 'earnings';
                } else if (desc.includes('withdrawal') || desc.includes('withdraw')) {
                    typeDisplay = 'Withdrawal';
                    type = 'withdrawal';
                } else if (desc.includes('transfer')) {
                    typeDisplay = 'Wallet Transfer';
                    type = 'transfer';
                } else {
                    typeDisplay = transaction.description;
                }
            } else {
                typeDisplay = type.charAt(0).toUpperCase() + type.slice(1) || 'Transaction';
            }
    }

    // Standardize amount
    let amount = 0;
    try {
        if (transaction.amount !== undefined && transaction.amount !== null) {
            amount = parseFloat(transaction.amount);
        } else if (transaction.value !== undefined && transaction.value !== null) {
            // Some transactions might use 'value' instead of 'amount'
            amount = parseFloat(transaction.value);
        }
    } catch (error) {
        console.error('Error parsing amount:', error);
    }

    // Standardize status
    let status = transaction.status || 'completed';
    if (typeof status === 'string') {
        status = status.toLowerCase();
    }

    // Create standardized transaction object
    const standardized = {
        ...transaction,
        type,
        typeDisplay,
        amount,
        status,
        timestamp,
        description: transaction.description || typeDisplay,
        _standardized: true
    };

    // Log the standardized transaction for debugging
    console.log('Standardized transaction:', standardized);

    return standardized;
}
