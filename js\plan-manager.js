// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import {
    getFirestore,
    doc,
    getDoc,
    updateDoc,
    collection,
    addDoc,
    serverTimestamp
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

// Firebase Config
const firebaseConfig = {
    apiKey: "AIzaSyAMMabAMVSrNzKdTEVOcn4Ffh7oDVxD2EM",
    authDomain: "mytube-earnings.firebaseapp.com",
    projectId: "mytube-earnings",
    storageBucket: "mytube-earnings.firebasestorage.app",
    messagingSenderId: "116772605182",
    appId: "1:116772605182:web:a5e9875d48867cca03e9af",
    measurementId: "G-MR5HFN8J4V"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Available plans
const plans = {
    'trial': {
        name: 'Trial',
        price: 0, // Free
        dailyLimit: 100,
        rate: 0.1, // ₹10 for 100 videos = ₹0.1 per video
        duration: 2, // 2-day trial period
        languages: 0,
        description: '2-Day Access — Explore and test your video watching potential',
        videoDuration: 300 // 5 minutes = 300 seconds
    },
    'starter': {
        name: 'Starter',
        price: 499, // ₹499
        dailyLimit: 100,
        rate: 0.25, // ₹25 for 100 videos = ₹0.25 per video
        duration: 31, // 31-day period (changed from 30 to 31 active days)
        languages: 0,
        description: 'Basic Video Access — Entry-level for new video watchers',
        refundable: true,
        refundPeriod: 180, // 6 months (180 days) for 100% refund
        videoDuration: 300 // 5 minutes = 300 seconds
    },
    'premium': {
        name: 'Premium',
        price: 2999, // ₹2999
        dailyLimit: 100,
        rate: 1.5, // ₹150 for 100 videos = ₹1.5 per video
        duration: 30, // 30-day period
        languages: 0,
        description: 'Premium Video Access — Advanced video watching features',
        refundable: true,
        refundPeriod: 180, // 6 months (180 days) for 100% refund
        videoDuration: 300 // 5 minutes = 300 seconds
    },
    'elite': {
        name: 'Elite',
        price: 4999, // ₹4999
        dailyLimit: 100,
        rate: 2.5, // ₹250 for 100 videos = ₹2.5 per video
        duration: 30, // 30-day period
        languages: 0,
        description: 'Elite Video Access — High volume video watching with competitive rates',
        refundable: true,
        refundPeriod: 180, // 6 months (180 days) for 100% refund
        videoDuration: 300 // 5 minutes = 300 seconds
    },
    'ultimate': {
        name: 'Ultimate',
        price: 9999, // ₹9999
        dailyLimit: 100,
        rate: 5, // ₹500 for 100 videos = ₹5 per video
        duration: 30, // 30-day period
        languages: 0,
        description: 'Ultimate Video Access — Maximum earnings with premium video watching experience',
        refundable: true,
        refundPeriod: 180, // 6 months (180 days) for 100% refund
        videoDuration: 300 // 5 minutes = 300 seconds
    }
};

// Import the referral bonus processing function
import { processReferralBonus, normalizePlanName } from "./referral-bonus.js";

/**
 * Upgrade user's plan and record the transaction
 * @param {string} planId - The ID of the plan to upgrade to
 * @returns {Promise<boolean>} - Whether the upgrade was successful
 */
export async function upgradePlan(planId) {
    try {
        // Validate plan
        if (!plans[planId]) {
            console.error('Invalid plan ID:', planId);
            return false;
        }

        // Get current user
        const user = auth.currentUser;
        if (!user) {
            console.error('User not authenticated');
            return false;
        }

        const plan = plans[planId];

        // Get current user data
        const userRef = doc(db, 'users', user.uid);
        const userSnap = await getDoc(userRef);

        if (!userSnap.exists()) {
            console.error('User document not found');
            return false;
        }

        const userData = userSnap.data();
        const currentPlan = userData.plan?.name || 'Unknown';

        // Update user's plan in Firestore
        await updateDoc(userRef, {
            plan: {
                name: plan.name,
                dailyLimit: plan.dailyLimit,
                rate: plan.rate
            },
            planUpdatedAt: serverTimestamp(),
            planActivatedAt: serverTimestamp(),
            // Reset active days when upgrading to a new plan
            activeDays: 1,
            lastActiveDayUpdate: serverTimestamp()
        });

        // Create a transaction record for the plan upgrade
        const transactionData = {
            userId: user.uid,
            type: 'plan',
            description: `${currentPlan} → ${plan.name}`,
            amount: -plan.price, // Negative amount as it's a payment
            status: 'completed',
            timestamp: serverTimestamp(),
            planDetails: {
                name: plan.name,
                dailyLimit: plan.dailyLimit,
                rate: plan.rate,
                previousPlan: currentPlan
            }
        };

        // Add the transaction to Firestore
        await addDoc(collection(db, 'transactions'), transactionData);

        console.log(`Successfully upgraded from ${currentPlan} to ${plan.name}`);

        // Process referral bonus if user has a referral code
        if (userData.referredBy) {
            try {
                console.log('User has referral code, processing referral bonus...');

                // Normalize the plan name for referral bonus processing
                const normalizedPlanName = normalizePlanName(plan.name);

                // Create plan details object for referral bonus processing
                const planDetails = {
                    name: normalizedPlanName,
                    dailyLimit: plan.dailyLimit,
                    rate: plan.rate
                };

                // Process the referral bonus
                const bonusProcessed = await processReferralBonus(user.uid, planDetails);

                if (bonusProcessed) {
                    console.log('Referral bonus processed successfully');
                } else {
                    console.log('No referral bonus processed (user may not have been referred or already received a bonus)');
                }
            } catch (bonusError) {
                console.error('Error processing referral bonus:', bonusError);
                // Continue even if referral bonus processing fails
            }
        } else {
            console.log('User does not have a referral code, skipping referral bonus processing');
        }

        return true;

    } catch (error) {
        console.error('Error upgrading plan:', error);
        return false;
    }
}

/**
 * Check and fix any inconsistencies in the user's plan data
 * @param {string} userId - The user ID to check
 * @returns {Promise<Object>} - Object containing the check results
 */
export async function checkAndFixPlanData(userId) {
    try {
        console.log(`Checking plan data for user: ${userId}`);

        if (!userId) {
            console.error('No user ID provided for plan check');
            return { success: false, message: 'No user ID provided' };
        }

        // Get the user document
        const userRef = doc(db, 'users', userId);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
            console.error('User document not found');
            return { success: false, message: 'User document not found' };
        }

        const userData = userDoc.data();
        let fixesApplied = [];
        let needsUpdate = false;

        // Check if plan data exists
        if (!userData.plan) {
            console.warn('User has no plan data, setting to trial plan');
            userData.plan = {
                name: 'Trial',
                dailyLimit: plans.trial.dailyLimit,
                rate: plans.trial.rate,
                activatedAt: serverTimestamp()
            };
            needsUpdate = true;
            fixesApplied.push('Added missing plan data (set to Trial)');
        }

        // Check if plan has all required fields
        const requiredFields = ['name', 'dailyLimit', 'rate'];
        for (const field of requiredFields) {
            if (!userData.plan[field]) {
                console.warn(`Plan missing required field: ${field}`);

                // Set default values based on field
                if (field === 'name') userData.plan.name = 'Trial';
                if (field === 'dailyLimit') userData.plan.dailyLimit = plans.trial.dailyLimit;
                if (field === 'rate') userData.plan.rate = plans.trial.rate;

                needsUpdate = true;
                fixesApplied.push(`Added missing plan field: ${field}`);
            }
        }

        // Check if plan name is valid
        const planName = userData.plan.name;
        let validPlanFound = false;

        for (const [planId, planDetails] of Object.entries(plans)) {
            if (planDetails.name === planName) {
                validPlanFound = true;

                // Check if rate matches the plan
                if (userData.plan.rate !== planDetails.rate) {
                    console.warn(`Plan rate doesn't match plan name. Name: ${planName}, Expected rate: ${planDetails.rate}, Actual rate: ${userData.plan.rate}`);
                    userData.plan.rate = planDetails.rate;
                    needsUpdate = true;
                    fixesApplied.push(`Corrected plan rate to match plan name (${planName})`);
                }

                // Check if daily limit matches the plan
                if (userData.plan.dailyLimit !== planDetails.dailyLimit) {
                    console.warn(`Plan daily limit doesn't match plan name. Name: ${planName}, Expected limit: ${planDetails.dailyLimit}, Actual limit: ${userData.plan.dailyLimit}`);
                    userData.plan.dailyLimit = planDetails.dailyLimit;
                    needsUpdate = true;
                    fixesApplied.push(`Corrected plan daily limit to match plan name (${planName})`);
                }

                break;
            }
        }

        // If plan name is not valid, set to Trial
        if (!validPlanFound) {
            console.warn(`Invalid plan name: ${planName}, setting to Trial`);
            userData.plan.name = 'Trial';
            userData.plan.dailyLimit = plans.trial.dailyLimit;
            userData.plan.rate = plans.trial.rate;
            needsUpdate = true;
            fixesApplied.push(`Corrected invalid plan name: ${planName} to Trial`);
        }

        // Apply fixes if needed
        if (needsUpdate) {
            console.log('Applying fixes to plan data:', fixesApplied);

            try {
                await updateDoc(userRef, {
                    plan: userData.plan,
                    planFixedAt: serverTimestamp(),
                    planFixesApplied: fixesApplied
                });

                console.log('Successfully applied plan fixes');
                return {
                    success: true,
                    message: 'Plan data fixed successfully',
                    fixesApplied,
                    planData: userData.plan
                };
            } catch (updateError) {
                console.error('Error updating plan data:', updateError);
                return {
                    success: false,
                    message: 'Error updating plan data: ' + (updateError.message || 'Unknown error'),
                    fixesApplied,
                    planData: userData.plan
                };
            }
        } else {
            console.log('No plan fixes needed');
            return {
                success: true,
                message: 'Plan data is valid, no fixes needed',
                planData: userData.plan
            };
        }
    } catch (error) {
        console.error('Error checking plan data:', error);
        return {
            success: false,
            message: 'Error checking plan data: ' + (error.message || 'Unknown error')
        };
    }
}

// Make the functions available globally
window.upgradePlan = upgradePlan;
window.checkAndFixPlanData = checkAndFixPlanData;

// Export the functions
export { plans };
