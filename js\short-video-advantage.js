/**
 * Short Video Advantage System
 *
 * This module handles the short video advantage system based on:
 * 1. Initial plan-based advantages (Trial: default 30 seconds, Starter/Premium: 7 days of 30-second videos)
 * 2. Extended advantages based on referrals (3 successful referrals for permanent 30-second videos)
 */

import {
    getFirestore,
    doc,
    getDoc,
    updateDoc,
    collection,
    query,
    where,
    getDocs,
    Timestamp
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";

// Initialize Firebase services
const db = getFirestore();
const auth = getAuth();

// Short video advantage duration by plan (in days)
const SHORT_VIDEO_DURATION = {
    'Trial': 2,  // Trial users get 30-second videos by default for 2 days
    'Starter': 7, // Starter users get 7 days of 30-second videos
    'Premium': 7  // Premium users get 7 days of 30-second videos
};

// Required referrals to maintain short video advantage
const REQUIRED_REFERRALS = 3;

// Video durations in seconds
const SHORT_VIDEO_TIME = 30;  // 30 seconds for short videos
const REGULAR_VIDEO_TIME = 300; // 5 minutes for regular videos

/**
 * Check if user has short video advantage
 * @returns {Promise<Object>} Advantage status and details
 */
export async function checkShortVideoAdvantage() {
    try {
        const user = auth.currentUser;
        if (!user) {
            console.log('No user is signed in');
            return {
                hasAdvantage: false,
                reason: 'not_authenticated',
                videoDuration: REGULAR_VIDEO_TIME
            };
        }

        // Get user data
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (!userDoc.exists()) {
            console.log('User document not found');
            return {
                hasAdvantage: false,
                reason: 'user_not_found',
                videoDuration: REGULAR_VIDEO_TIME
            };
        }

        const userData = userDoc.data();

        // Check if short video advantage is already enabled
        if (userData.shortVideoAdvantage === true) {
            // Check if advantage has expired
            if (userData.shortVideoExpiryDate) {
                const expiryDate = userData.shortVideoExpiryDate.toDate();
                const currentDate = new Date();

                if (currentDate > expiryDate) {
                    console.log('Short video advantage has expired on', expiryDate.toLocaleDateString());

                    // If advantage was set by admin, don't auto-extend
                    if (userData.advantageSource === 'admin') {
                        console.log('Advantage was set by admin, not auto-extending');

                        // Disable expired advantage
                        await updateDoc(doc(db, 'users', user.uid), {
                            shortVideoAdvantage: false,
                            shortVideoExpiryDate: null
                        });

                        // Check if user has enough referrals for permanent advantage
                        const hasEnoughReferrals = await checkReferralRequirement(user.uid);
                        if (hasEnoughReferrals) {
                            return {
                                hasAdvantage: true,
                                reason: 'referral_permanent',
                                videoDuration: SHORT_VIDEO_TIME
                            };
                        }

                        return {
                            hasAdvantage: false,
                            reason: 'admin_advantage_expired',
                            message: 'Your admin-granted short video advantage has expired.',
                            videoDuration: REGULAR_VIDEO_TIME
                        };
                    }

                    // For automatic advantages, check if user has enough referrals to maintain access
                    const hasEnoughReferrals = await checkReferralRequirement(user.uid);

                    if (hasEnoughReferrals) {
                        // User has permanent advantage due to referrals
                        return {
                            hasAdvantage: true,
                            reason: 'referral_permanent',
                            videoDuration: SHORT_VIDEO_TIME
                        };
                    } else {
                        // Disable expired advantage
                        await updateDoc(doc(db, 'users', user.uid), {
                            shortVideoAdvantage: false,
                            shortVideoExpiryDate: null,
                            advantageSource: null
                        });

                        return {
                            hasAdvantage: false,
                            reason: 'expired_no_referrals',
                            requiredReferrals: REQUIRED_REFERRALS,
                            videoDuration: REGULAR_VIDEO_TIME
                        };
                    }
                } else {
                    // Advantage is still valid
                    const daysRemaining = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));
                    const source = userData.advantageSource || 'automatic';
                    console.log(`Short video advantage active (source: ${source}). Expires in ${daysRemaining} days`);

                    return {
                        hasAdvantage: true,
                        reason: 'active',
                        daysRemaining: daysRemaining,
                        expiryDate: expiryDate,
                        source: source,
                        videoDuration: SHORT_VIDEO_TIME
                    };
                }
            } else {
                // Advantage is enabled but no expiry date (permanent advantage)
                console.log('Short video advantage is enabled with no expiry date (permanent)');
                return {
                    hasAdvantage: true,
                    reason: 'permanent',
                    videoDuration: SHORT_VIDEO_TIME
                };
            }
        } else {
            // Check if admin explicitly disabled advantage
            if (userData.advantageSource === 'admin' && userData.shortVideoAdvantage === false) {
                console.log('Short video advantage explicitly disabled by admin');

                // Check if user has enough referrals for permanent advantage
                const hasEnoughReferrals = await checkReferralRequirement(user.uid);
                if (hasEnoughReferrals) {
                    return {
                        hasAdvantage: true,
                        reason: 'referral_permanent',
                        videoDuration: SHORT_VIDEO_TIME
                    };
                }

                return {
                    hasAdvantage: false,
                    reason: 'admin_disabled',
                    message: 'Short video advantage has been disabled by an administrator.',
                    videoDuration: REGULAR_VIDEO_TIME
                };
            }

            // Advantage is not enabled, check if user is eligible for initial advantage
            const planName = userData.plan?.name || 'Trial';

            // Trial users get short videos for 2 days
            if (planName === 'Trial') {
                console.log(`User has Trial plan, enabling short video advantage for ${SHORT_VIDEO_DURATION['Trial']} days`);

                try {
                    // Calculate expiry date for Trial users (2 days)
                    const expiryDate = new Date();
                    expiryDate.setDate(expiryDate.getDate() + SHORT_VIDEO_DURATION['Trial']);

                    // Enable short video advantage for Trial users with 2-day expiry
                    await updateDoc(doc(db, 'users', user.uid), {
                        shortVideoAdvantage: true,
                        shortVideoExpiryDate: Timestamp.fromDate(expiryDate),
                        advantageSource: 'automatic'
                    });

                    console.log(`Enabled Trial plan short video advantage for ${SHORT_VIDEO_DURATION['Trial']} days until ${expiryDate.toLocaleDateString()}`);
                } catch (updateError) {
                    console.error('Error updating user document for Trial plan advantage:', updateError);
                    // Continue anyway - we'll still return short video advantage
                }

                // Calculate expiry date for return value if we couldn't update the database
                const expiryDate = new Date();
                expiryDate.setDate(expiryDate.getDate() + SHORT_VIDEO_DURATION['Trial']);

                return {
                    hasAdvantage: true,
                    reason: 'trial_plan',
                    planName: planName,
                    daysGranted: SHORT_VIDEO_DURATION['Trial'],
                    expiryDate: expiryDate,
                    videoDuration: SHORT_VIDEO_TIME
                };
            }

            // For other plans, check duration
            const planDuration = SHORT_VIDEO_DURATION[planName] || 0;

            if (planDuration > 0) {
                // Check if plan was recently activated (within 24 hours)
                const planActivatedAt = userData.planActivatedAt?.toDate() || new Date();
                const currentDate = new Date();
                const hoursSinceActivation = (currentDate - planActivatedAt) / (1000 * 60 * 60);

                if (hoursSinceActivation <= 24) {
                    // Enable initial short video advantage
                    const expiryDate = await enableInitialShortVideoAdvantage(user.uid, planName);

                    return {
                        hasAdvantage: true,
                        reason: 'initial_enabled',
                        planName: planName,
                        daysGranted: planDuration,
                        expiryDate: expiryDate,
                        videoDuration: SHORT_VIDEO_TIME
                    };
                } else {
                    // Plan was activated more than 24 hours ago

                    // Check if plan has been active for more than 7 days
                    // Use activeDays from user data if available, otherwise fall back to calendar days
                    if (planActivatedAt) {
                        const activationDate = new Date(planActivatedAt);
                        const now = new Date();
                        const diffTime = Math.abs(now - activationDate);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                        // Get active days from user data
                        const activeDays = userData.activeDays || 1;

                        // Use the larger of diffDays or activeDays to ensure we're using the most accurate measure
                        const effectiveDays = Math.max(diffDays, activeDays);

                        console.log(`Plan activation check: diffDays=${diffDays}, activeDays=${activeDays}, effectiveDays=${effectiveDays}`);

                        // If plan is active for more than 7 days, check referrals
                        if (effectiveDays > 7) {
                            console.log(`Plan ${planName} is active for ${effectiveDays} days (>7), checking referrals`);

                            // Check if user has enough referrals for permanent advantage
                            const hasEnoughReferrals = await checkReferralRequirement(user.uid);

                            if (hasEnoughReferrals) {
                                // User has permanent advantage due to referrals
                                return {
                                    hasAdvantage: true,
                                    reason: 'referral_permanent',
                                    videoDuration: SHORT_VIDEO_TIME
                                };
                            } else {
                                // Not eligible for short video advantage after 7 days
                                // Update user document to explicitly disable short video advantage
                                await updateDoc(doc(db, 'users', user.uid), {
                                    shortVideoAdvantage: false,
                                    shortVideoExpiryDate: null,
                                    advantageSource: 'automatic'
                                });

                                return {
                                    hasAdvantage: false,
                                    reason: 'plan_active_over_7_days',
                                    message: `Your ${planName} plan has been active for more than 7 active days. Short videos are no longer available.`,
                                    requiredReferrals: REQUIRED_REFERRALS,
                                    currentReferrals: await getCurrentReferralCount(user.uid),
                                    videoDuration: REGULAR_VIDEO_TIME
                                };
                            }
                        }
                    }

                    // If we're here, either the plan is still within 7 days or we couldn't determine the activation date
                    // Check referrals as a fallback
                    const hasEnoughReferrals = await checkReferralRequirement(user.uid);

                    if (hasEnoughReferrals) {
                        // User has permanent advantage due to referrals
                        return {
                            hasAdvantage: true,
                            reason: 'referral_permanent',
                            videoDuration: SHORT_VIDEO_TIME
                        };
                    } else {
                        // Not eligible for short video advantage
                        return {
                            hasAdvantage: false,
                            reason: 'no_advantage',
                            requiredReferrals: REQUIRED_REFERRALS,
                            currentReferrals: await getCurrentReferralCount(user.uid),
                            videoDuration: REGULAR_VIDEO_TIME
                        };
                    }
                }
            } else {
                // Check if user has enough referrals for permanent advantage
                const hasEnoughReferrals = await checkReferralRequirement(user.uid);
                if (hasEnoughReferrals) {
                    return {
                        hasAdvantage: true,
                        reason: 'referral_permanent',
                        videoDuration: SHORT_VIDEO_TIME
                    };
                }

                // For unknown plans, default to short video advantage for better user experience
                console.log('Unknown plan or no advantage duration specified, defaulting to short video advantage');

                try {
                    // Enable short video advantage for unknown plans
                    await updateDoc(doc(db, 'users', user.uid), {
                        shortVideoAdvantage: true,
                        shortVideoExpiryDate: null,
                        advantageSource: 'automatic'
                    });
                } catch (updateError) {
                    console.error('Error updating user document for unknown plan advantage:', updateError);
                    // Continue anyway - we'll still return short video advantage
                }

                return {
                    hasAdvantage: true,
                    reason: 'default_advantage',
                    planName: planName,
                    videoDuration: SHORT_VIDEO_TIME
                };
            }
        }
    } catch (error) {
        console.error('Error checking short video advantage:', error);

        // Instead of always defaulting to short video advantage,
        // try to check the plan activation date directly
        try {
            console.log('Attempting to check plan activation date directly after error');
            const user = auth.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(db, 'users', user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const planName = userData.plan?.name || 'Trial';
                    const planActivationDate = userData.plan?.activationDate ||
                                              userData.planActivatedAt?.toDate() ||
                                              userData.planUpdatedAt?.toDate();

                    // Only check activation date for Starter and Premium plans
                    if ((planName === 'Starter' || planName === 'Premium') && planActivationDate) {
                        const activationDate = new Date(planActivationDate);
                        const now = new Date();
                        const diffTime = Math.abs(now - activationDate);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                        // Get active days from user data
                        const activeDays = userData.activeDays || 1;

                        // Use the larger of diffDays or activeDays to ensure we're using the most accurate measure
                        const effectiveDays = Math.max(diffDays, activeDays);

                        console.log(`Plan activation check in error handler: diffDays=${diffDays}, activeDays=${activeDays}, effectiveDays=${effectiveDays}`);

                        // If plan is active for more than 7 days, don't give short video advantage
                        if (effectiveDays > 7) {
                            console.log(`Plan ${planName} is active for ${effectiveDays} days (>7), using regular video duration`);
                            return {
                                hasAdvantage: false,
                                reason: 'plan_active_over_7_days',
                                error: error.message,
                                videoDuration: REGULAR_VIDEO_TIME
                            };
                        }
                    }
                }
            }
        } catch (fallbackError) {
            console.error('Error in fallback plan check:', fallbackError);
        }

        // If we couldn't determine the plan status or for Trial users, default to short video advantage
        console.log('Error occurred, defaulting to short video advantage');
        return {
            hasAdvantage: true,
            reason: 'error_default',
            error: error.message,
            videoDuration: SHORT_VIDEO_TIME
        };
    }
}

/**
 * Check if user has enough referrals for short video advantage
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} Whether user has enough referrals
 */
async function checkReferralRequirement(userId) {
    try {
        const referralCount = await getCurrentReferralCount(userId);
        return referralCount >= REQUIRED_REFERRALS;
    } catch (error) {
        console.error('Error checking referral requirement:', error);
        return false;
    }
}

/**
 * Get current referral count for a user
 * @param {string} userId - User ID
 * @returns {Promise<number>} Number of referrals
 */
async function getCurrentReferralCount(userId) {
    try {
        // First check user document for referral count
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
            const userData = userDoc.data();

            // Check various fields where referral count might be stored
            const referralCount = userData.stats?.totalReferrals ||
                                 userData.stats?.referrals ||
                                 userData.referralCount || 0;

            if (referralCount > 0) {
                return referralCount;
            }
        }

        // If not found in user document, check referrals collection
        const referralsQuery = query(
            collection(db, 'referrals'),
            where('referredBy', '==', userId)
        );

        const referralsSnapshot = await getDocs(referralsQuery);
        return referralsSnapshot.size;
    } catch (error) {
        console.error('Error getting referral count:', error);
        return 0;
    }
}

/**
 * Enable initial short video advantage based on plan
 * @param {string} userId - User ID
 * @param {string} planName - Plan name
 * @returns {Promise<Date>} Expiry date
 */
async function enableInitialShortVideoAdvantage(userId, planName) {
    try {
        // First check if admin has explicitly set advantage
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
            const userData = userDoc.data();

            // If admin has explicitly set advantage (either enabled or disabled), respect that setting
            if (userData.advantageSource === 'admin') {
                console.log('Admin has explicitly set short video advantage, respecting that setting');

                // If advantage is enabled and has a valid expiry date, return that
                if (userData.shortVideoAdvantage === true && userData.shortVideoExpiryDate) {
                    const expiryDate = userData.shortVideoExpiryDate.toDate();
                    console.log(`Using admin-set short video advantage expiring on ${expiryDate.toLocaleDateString()}`);
                    return expiryDate;
                }

                // If admin disabled it, don't enable it automatically
                if (userData.shortVideoAdvantage === false) {
                    console.log('Admin has explicitly disabled short video advantage, not enabling automatically');
                    throw new Error('Short video advantage disabled by admin');
                }
            }
        }

        // Check if user has enough referrals for permanent advantage
        const hasEnoughReferrals = await checkReferralRequirement(userId);
        if (hasEnoughReferrals) {
            // Enable permanent advantage
            await updateDoc(doc(db, 'users', userId), {
                shortVideoAdvantage: true,
                shortVideoExpiryDate: null, // No expiry for referral-based advantage
                advantageSource: 'referrals'
            });

            console.log(`Enabled permanent short video advantage based on referrals`);
            return null; // No expiry date for permanent advantage
        }

        const planDuration = SHORT_VIDEO_DURATION[planName] || 0;
        if (planDuration <= 0) {
            throw new Error(`Plan ${planName} does not have short video advantage`);
        }

        // For Starter and Premium plans, double-check the activation date
        if (planName === 'Starter' || planName === 'Premium') {
            // Get user data to check activation date
            const userDoc = await getDoc(doc(db, 'users', userId));
            if (userDoc.exists()) {
                const userData = userDoc.data();
                const planActivationDate = userData.plan?.activationDate ||
                                          userData.planActivatedAt?.toDate() ||
                                          userData.planUpdatedAt?.toDate();

                if (planActivationDate) {
                    const activationDate = new Date(planActivationDate);
                    const now = new Date();
                    const diffTime = Math.abs(now - activationDate);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    // Get active days from user data
                    const activeDays = userData.activeDays || 1;

                    // Use the larger of diffDays or activeDays to ensure we're using the most accurate measure
                    const effectiveDays = Math.max(diffDays, activeDays);

                    console.log(`Plan activation check in enableInitialShortVideoAdvantage: diffDays=${diffDays}, activeDays=${activeDays}, effectiveDays=${effectiveDays}`);

                    // If plan is active for more than 7 days, don't enable short video advantage
                    if (effectiveDays > 7) {
                        console.log(`Plan ${planName} is active for ${effectiveDays} days (>7), not enabling short video advantage`);

                        // Update user document to explicitly disable short video advantage
                        await updateDoc(doc(db, 'users', userId), {
                            shortVideoAdvantage: false,
                            shortVideoExpiryDate: null,
                            advantageSource: 'automatic'
                        });

                        throw new Error(`Plan ${planName} is active for more than ${effectiveDays} active days, short video advantage not enabled`);
                    }
                }
            }
        }

        // Calculate expiry date
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + planDuration);

        // Update user document
        await updateDoc(doc(db, 'users', userId), {
            shortVideoAdvantage: true,
            shortVideoExpiryDate: Timestamp.fromDate(expiryDate),
            advantageSource: 'automatic'
        });

        console.log(`Enabled initial short video advantage for ${planDuration} days until ${expiryDate.toLocaleDateString()}`);
        return expiryDate;
    } catch (error) {
        console.error('Error enabling initial short video advantage:', error);
        throw error;
    }
}

/**
 * Get the appropriate video duration based on user's advantage status and custom settings
 * @param {boolean} hasAdvantage - Whether the user has short video advantage
 * @param {number|null} customDuration - Custom video duration set by admin (in seconds)
 * @returns {number} - The video duration in seconds
 */
export function getVideoDuration(hasAdvantage, customDuration = null) {
    // If admin has set a custom duration for this user, use that
    if (customDuration !== null && customDuration !== undefined) {
        return customDuration;
    }

    // Return 30 seconds for users with advantage, otherwise 5 minutes (300 seconds)
    return hasAdvantage ? SHORT_VIDEO_TIME : REGULAR_VIDEO_TIME;
}

/**
 * Get video duration for a specific user (checks for custom duration)
 * @param {string} userId - The user ID
 * @returns {Promise<number>} - The video duration in seconds
 */
export async function getUserVideoDuration(userId) {
    try {
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
            const userData = userDoc.data();

            // Check if user has custom video duration set by admin
            if (userData.customVideoDuration !== null && userData.customVideoDuration !== undefined) {
                console.log(`Using custom video duration for user ${userId}: ${userData.customVideoDuration} seconds`);
                return userData.customVideoDuration;
            }

            // Otherwise, check for short video advantage
            const advantageStatus = await checkShortVideoAdvantage(userId);
            return getVideoDuration(advantageStatus.hasAdvantage);
        }

        // Default to regular video time if user not found
        return REGULAR_VIDEO_TIME;
    } catch (error) {
        console.error('Error getting user video duration:', error);
        return REGULAR_VIDEO_TIME;
    }
}

/**
 * Export constants for use in other modules
 */
export const VIDEO_DURATIONS = {
    SHORT: SHORT_VIDEO_TIME,
    REGULAR: REGULAR_VIDEO_TIME
};
