// Import Firebase modules
import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import {
    getFirestore,
    doc,
    getDoc,
    updateDoc,
    increment,
    serverTimestamp,
    collection,
    addDoc
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

// Import cache utilities
import {
    cacheTranslationCount,
    getCachedTranslationCount,
    cacheUserData,
    getCachedUserData
} from './cache-utils.js';

// Use the existing Firebase app from firebase-config.js
// This avoids duplicate initialization errors
let auth, db;

// Wait for the firebase-config.js to initialize Firebase
document.addEventListener('firebaseInitialized', (event) => {
    console.log('Firebase initialized event received in video-limits.js');
    auth = event.detail.auth;
    db = event.detail.db;
});

// Also check on DOMContentLoaded in case the event was already fired
document.addEventListener('DOMContentLoaded', () => {
    if (window.firebase) {
        console.log('Using existing Firebase app in video-limits.js');
        auth = window.firebase.auth;
        db = window.firebase.db;
    } else {
        console.warn('Firebase not initialized yet in video-limits.js. Waiting for initialization event.');
    }
});

// Local storage keys for tracking videos when Firebase is not available
const LOCAL_DAILY_COUNT_KEY = 'instra_video_count';
const LOCAL_DATE_KEY = 'instra_daily_video_date';

/**
 * Check if the user has reached their daily video watching limit
 * @returns {Promise<Object>} - Object containing limit status and details
 */
export async function checkDailyVideoLimit() {
    try {
        console.log('Checking daily video limit...');

        // Define now at the beginning of the function so it's available throughout
        const now = new Date();

        // Get current user
        const user = auth.currentUser;
        if (!user) {
            console.warn('User not authenticated, using local storage for testing');
            // Check local storage as fallback
            const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';
            if (videosSubmitted) {
                // Calculate next reset time (midnight tonight)
                const nextResetTime = new Date(now);
                nextResetTime.setHours(24, 0, 0, 0);

                return {
                    limitReached: true,
                    message: 'You have already submitted your videos for today. Please come back tomorrow.',
                    dailyCount: 100,
                    dailyLimit: 100,
                    remainingCount: 0,
                    nextResetTime: nextResetTime,
                    currentCount: 100,
                    alreadySubmitted: true // This flag indicates videos were submitted
                };
            }
            return getLocalVideoLimit();
        }

        // First check Firebase for submission status - this is the source of truth
        try {
            const userRef = doc(db, 'users', user.uid);
            const userSnap = await getDoc(userRef);

            if (userSnap.exists()) {
                const userData = userSnap.data();
                const stats = userData.stats || {};

                // Get the current date in UTC format for consistent comparison
                const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format

                // Check if dailyVideos exists and has been submitted today
                if (stats.dailyVideos && stats.dailyVideos.submitted === true) {
                    // Check if the submission was today
                    const submissionDate = stats.dailyVideos.date;

                    // If submission date matches current date, user has already submitted today
                    if (submissionDate === currentDateUTC || submissionDate === now.toLocaleDateString('en-US')) {
                        console.log('User has already submitted videos for today according to Firebase');

                        // Set localStorage flag to ensure consistent state
                        localStorage.setItem('instra_videos_submitted', 'true');
                        localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

                        // Also ensure the today's videos count is set to 100 in localStorage
                        localStorage.setItem(LOCAL_DAILY_COUNT_KEY, '100');

                        // Ensure the count in Firebase is 100 for consistency
                        try {
                            await updateDoc(userRef, {
                                'stats.todayVideos': 100,
                                'stats.todayTranslations': 100,
                                'stats.dailyVideos.count': 100,
                                'stats.dailyTranslations.count': 100
                            });
                            console.log('Ensured today\'s videos count is 100 in Firebase');
                        } catch (updateError) {
                            console.error('Error updating today\'s videos count in Firebase:', updateError);
                        }

                        // Calculate next reset time (midnight tonight)
                        const nextResetTime = new Date(now);
                        nextResetTime.setHours(24, 0, 0, 0);

                        return {
                            limitReached: true,
                            message: 'You have already submitted your videos for today. Please come back tomorrow.',
                            dailyCount: 100,
                            dailyLimit: 100,
                            remainingCount: 0,
                            nextResetTime: nextResetTime,
                            currentCount: 100,
                            alreadySubmitted: true,
                            firebaseVerified: true // Flag to indicate this was verified in Firebase
                        };
                    } else {
                        // If the submission date doesn't match today, reset the submitted flag and counts
                        console.log('Submission flag is true but date doesn\'t match today. Resetting submission status and counts.');

                        try {
                            // Get the current date in both formats for consistency
                            const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                            const currentDateLocal = now.toLocaleDateString('en-US');

                            // Create a new video data object for the new day
                            const newVideoData = {
                                date: currentDateLocal,
                                count: 0,
                                lastUpdated: serverTimestamp(),
                                submitted: false // Reset the submitted flag for the new day
                            };

                            // Comprehensive update to reset all relevant fields
                            await updateDoc(userRef, {
                                // Reset today's video counts
                                'stats.todayVideos': 0,
                                'stats.todayTranslations': 0,

                                // Reset daily videos tracking
                                'stats.dailyVideos': newVideoData,
                                'stats.dailyVideos.count': 0,
                                'stats.dailyVideos.submitted': false,

                                // Reset daily translations tracking (legacy)
                                'stats.dailyTranslations': newVideoData,
                                'stats.dailyTranslations.count': 0,
                                'stats.dailyTranslations.submitted': false
                            });

                            console.log('Reset all video count fields in Firebase to 0 for new day');

                            // Also reset localStorage
                            localStorage.setItem('instra_video_count', '0');
                            localStorage.setItem('instra_daily_video_date', currentDateLocal);
                            localStorage.removeItem('instra_videos_submitted');
                            localStorage.removeItem('instra_videos_submitted_date');
                            console.log('Reset all video count fields in localStorage for new day');
                        } catch (resetError) {
                            console.error('Error resetting submission flag and counts in Firebase:', resetError);
                        }
                    }
                }

                // Enhanced check for submission status using multiple indicators
                // Check all possible indicators that videos have been submitted today
                const hasSubmittedToday = (
                    // Check 1: dailyVideos.submitted flag is true AND date matches today
                    (stats.dailyVideos && stats.dailyVideos.submitted === true &&
                     (stats.dailyVideos.date === currentDateUTC || stats.dailyVideos.date === now.toLocaleDateString('en-US'))) ||

                    // Check 2: lastSubmission exists with submitted=true AND date matches today
                    (stats.lastSubmission && stats.lastSubmission.submitted === true &&
                     (stats.lastSubmission.date === currentDateUTC)) ||

                    // Check 3: todayVideos/todayTranslations is 100 AND lastSubmission date matches today
                    ((stats.todayVideos === 100 || stats.todayTranslations === 100) &&
                     stats.lastSubmission && stats.lastSubmission.date === currentDateUTC)
                );

                if (hasSubmittedToday) {
                    console.log('User has already submitted videos for today based on comprehensive check');

                    // Set localStorage flag to ensure consistent state
                    localStorage.setItem('instra_videos_submitted', 'true');
                    localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

                    // Also ensure the today's videos count is set to 100 in localStorage
                    localStorage.setItem(LOCAL_DAILY_COUNT_KEY, '100');

                    // Ensure all Firebase flags are consistent
                    try {
                        await updateDoc(userRef, {
                            'stats.todayVideos': 100,
                            'stats.todayTranslations': 100,
                            'stats.dailyVideos.submitted': true,
                            'stats.dailyTranslations.submitted': true,
                            'stats.dailyVideos.count': 100,
                            'stats.dailyTranslations.count': 100
                        });
                        console.log('Ensured all submission flags are consistent in Firebase');
                    } catch (updateError) {
                        console.error('Error ensuring submission flags in Firebase:', updateError);
                    }

                    // Calculate next reset time (midnight tonight)
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    return {
                        limitReached: true,
                        message: 'You have already submitted your videos for today. Please come back tomorrow.',
                        dailyCount: 100,
                        dailyLimit: 100,
                        remainingCount: 0,
                        nextResetTime: nextResetTime,
                        currentCount: 100,
                        alreadySubmitted: true,
                        firebaseVerified: true
                    };
                } else if (stats.todayVideos === 100 || stats.todayTranslations === 100) {
                    // If todayVideos or todayTranslations is 100 but lastSubmission doesn't match today's date,
                    // this is likely a case where the count is 100 but videos haven't been submitted yet
                    console.log('Found todayVideos/todayTranslations count of 100, but lastSubmission date doesn\'t match today - videos not submitted yet');

                    // Reset the submitted flag in localStorage if it's set
                    if (localStorage.getItem('instra_videos_submitted') === 'true') {
                        console.log('Resetting submitted flag in localStorage since videos are not actually submitted');
                        localStorage.removeItem('instra_videos_submitted');
                        localStorage.removeItem('instra_videos_submitted_date');
                    }

                    // Also ensure the submitted flag is false in Firebase
                    try {
                        await updateDoc(userRef, {
                            'stats.dailyVideos.submitted': false,
                            'stats.dailyTranslations.submitted': false
                        });
                        console.log('Ensured submitted flag is false in Firebase');
                    } catch (updateError) {
                        console.error('Error updating submitted flag in Firebase:', updateError);
                    }

                    // Don't set the submitted flag, just return the current count
                    return {
                        limitReached: false,
                        message: 'You have 100 videos ready to submit.',
                        dailyCount: 100,
                        dailyLimit: 100,
                        remainingCount: 0,
                        currentCount: 100,
                        alreadySubmitted: false,
                        firebaseVerified: true
                    };
                }
            }
        } catch (firebaseError) {
            console.error('Error checking Firebase for submission status:', firebaseError);
            // Continue to check localStorage as fallback
        }

        // Check localStorage as secondary verification
        const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';
        const submissionDateUTC = localStorage.getItem('instra_videos_submitted_date');

        // Get the current date in UTC format for comparison
        const currentDateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format

        // Only consider videos submitted if both the flag is true AND the date matches today
        if (videosSubmitted && submissionDateUTC === currentDateStr) {
            console.log('Videos submitted flag is true in localStorage and date matches today:', submissionDateUTC);

            // Calculate next reset time (midnight tonight)
            const nextResetTime = new Date(now);
            nextResetTime.setHours(24, 0, 0, 0);

            // Double-check with Firebase to ensure consistency
            try {
                const userRef = doc(db, 'users', user.uid);
                await updateDoc(userRef, {
                    'stats.todayVideos': 100,
                    'stats.todayTranslations': 100,
                    'stats.dailyVideos.submitted': true,
                    'stats.dailyTranslations.submitted': true,
                    'stats.dailyVideos.count': 100,
                    'stats.dailyTranslations.count': 100,
                    'stats.dailyVideos.date': currentDateStr,
                    'stats.dailyTranslations.date': currentDateStr,
                    'stats.lastSubmission': {
                        date: currentDateStr,
                        timestamp: serverTimestamp(),
                        count: 50
                    }
                });
                console.log('Updated Firebase to match localStorage submitted status');
            } catch (updateError) {
                console.error('Error updating Firebase to match localStorage:', updateError);
            }

            return {
                limitReached: true,
                message: 'You have already submitted your videos for today. Please come back tomorrow.',
                dailyCount: 100,
                dailyLimit: 100,
                remainingCount: 0,
                nextResetTime: nextResetTime,
                currentCount: 100,
                alreadySubmitted: true // This flag indicates videos were submitted
            };
        } else if (videosSubmitted && submissionDateUTC !== currentDateStr) {
            // If the flag is true but the date doesn't match today, reset the flag and counts
            console.log('Videos submitted flag is true in localStorage but date doesn\'t match today. Stored:', submissionDateUTC, 'Current:', currentDateStr);

            // Reset localStorage flags and counts
            localStorage.removeItem('instra_videos_submitted');
            localStorage.removeItem('instra_videos_submitted_date');
            localStorage.setItem(LOCAL_DAILY_COUNT_KEY, '0');
            localStorage.setItem(LOCAL_DATE_KEY, now.toLocaleDateString('en-US'));

            console.log('Reset videos submitted flag and counts in localStorage due to date mismatch');

            // Also reset in Firebase if user is authenticated
            if (user) {
                try {
                    const userRef = doc(db, 'users', user.uid);

                    // Get the current date in both formats for consistency
                    const currentDateLocal = now.toLocaleDateString('en-US');

                    // Create a new video data object for the new day
                    const newVideoData = {
                        date: currentDateLocal,
                        count: 0,
                        lastUpdated: serverTimestamp(),
                        submitted: false // Reset the submitted flag for the new day
                    };

                    // Comprehensive update to reset all relevant fields
                    await updateDoc(userRef, {
                        // Reset today's video counts
                        'stats.todayVideos': 0,
                        'stats.todayTranslations': 0,

                        // Reset daily videos tracking
                        'stats.dailyVideos': newVideoData,
                        'stats.dailyVideos.count': 0,
                        'stats.dailyVideos.submitted': false,

                        // Reset daily translations tracking (legacy)
                        'stats.dailyTranslations': newVideoData,
                        'stats.dailyTranslations.count': 0,
                        'stats.dailyTranslations.submitted': false
                    });

                    console.log('Reset all video count fields in Firebase to 0 for new day');
                } catch (resetError) {
                    console.error('Error resetting counts in Firebase:', resetError);
                }
            }
        }

        // First check local storage - we prioritize this during the video watching process
        // since we're only updating Firebase after submitting all 100 videos
        const storedCount = localStorage.getItem(LOCAL_DAILY_COUNT_KEY);
        if (storedCount !== null) {
            console.log('Using local storage count for daily limit check:', storedCount);
            return getLocalVideoLimit();
        }

        // If no local storage count, then check cache or Firebase
        // We already have the user from earlier, just check it again
        if (!user) {
            console.warn('User not authenticated, using local storage for testing');
            return getLocalVideoLimit();
        }

        // Try to get from cache first
        let userData = null;
        const cachedVideoData = getCachedTranslationCount(user.uid);

        if (cachedVideoData) {
            console.log('Using cached video count data');

            // Get the current date in user's local timezone
            const currentDate = now.toLocaleDateString('en-US');

            // Check if the cached data is for today
            if (cachedVideoData.date === currentDate) {
                // Try to get full user data from cache
                const cachedUserData = getCachedUserData(user.uid);
                if (cachedUserData) {
                    userData = cachedUserData;
                    console.log('Using cached user data for video limit check');
                } else {
                    // If we don't have full user data, we still need to get it for the plan info
                    const userRef = doc(db, 'users', user.uid);
                    const userSnap = await getDoc(userRef);

                    if (userSnap.exists()) {
                        userData = userSnap.data();
                        // Cache the user data for future use
                        cacheUserData(user.uid, userData);
                    } else {
                        console.warn('User document not found, using local storage for testing');
                        return getLocalVideoLimit();
                    }
                }
            } else {
                // Cached data is not for today, need to get fresh data
                console.log('Cached video data is not for today, getting fresh data');
                const userRef = doc(db, 'users', user.uid);
                const userSnap = await getDoc(userRef);

                if (userSnap.exists()) {
                    userData = userSnap.data();
                    // Cache the user data for future use
                    cacheUserData(user.uid, userData);
                } else {
                    console.warn('User document not found, using local storage for testing');
                    return getLocalVideoLimit();
                }
            }
        } else {
            // No cached data, get from Firebase
            console.log('No cached video data, getting from Firebase');
            const userRef = doc(db, 'users', user.uid);
            const userSnap = await getDoc(userRef);

            if (userSnap.exists()) {
                userData = userSnap.data();
                // Cache the user data for future use
                cacheUserData(user.uid, userData);
            } else {
                console.warn('User document not found, using local storage for testing');
                return getLocalVideoLimit();
            }
        }
        const plan = userData.plan || {};
        const dailyLimit = plan.dailyLimit || 100; // Default to 100 for all plans
        const stats = userData.stats || {};

        // Get the current date in user's local timezone
        const currentDate = now.toLocaleDateString('en-US');

        // Calculate next reset time (midnight tonight)
        const nextResetTime = new Date(now);
        nextResetTime.setHours(24, 0, 0, 0); // Set to midnight tonight

        // Check if we have daily stats for today - use stats.dailyVideos.count with fallbacks
        if (!stats.dailyVideos || stats.dailyVideos.date !== currentDate) {
            // It's a new day, but check if we have todayVideos or todayTranslations as fallback
            const todayCount = stats.todayVideos || stats.todayTranslations || 0;
            console.log('No daily videos data for today, checking fallbacks. Found count:', todayCount);

            try {
                // Create a reference to the user document
                const userRef = doc(db, 'users', user.uid);

                const newVideoData = {
                    date: currentDate,
                    count: todayCount, // Use existing count if available
                    lastUpdated: serverTimestamp(),
                    submitted: false // Reset the submitted flag for the new day
                };

                await updateDoc(userRef, {
                    'stats.dailyVideos': newVideoData,
                    'stats.dailyVideos.count': 0,
                    'stats.dailyVideos.submitted': false,
                    // Ensure todayVideos is set to 0 for a new day
                    'stats.todayVideos': 0,
                    'stats.todayTranslations': 0,
                    // Also reset daily translations for consistency
                    'stats.dailyTranslations': newVideoData,
                    'stats.dailyTranslations.count': 0,
                    'stats.dailyTranslations.submitted': false
                });

                // Cache the updated video data
                cacheTranslationCount(user.uid, {
                    date: currentDate,
                    count: 0,
                    submitted: false
                });

                console.log('Reset daily count for new day in stats.dailyVideos.count');
            } catch (error) {
                console.warn('Could not update daily videos:', error);
                // Continue anyway
            }

            return {
                limitReached: false,
                message: 'Daily count reset for new day',
                dailyCount: 0,
                dailyLimit: dailyLimit,
                remainingCount: dailyLimit,
                nextResetTime: nextResetTime,
                currentCount: 0
            };
        }

        // Check if the user has already submitted their videos for today
        if (stats.dailyVideos && stats.dailyVideos.submitted === true) {
            console.log('User has already submitted videos for today');
            return {
                limitReached: true,
                message: 'You have already submitted your videos for today. Please come back tomorrow.',
                dailyCount: dailyLimit,
                dailyLimit: dailyLimit,
                remainingCount: 0,
                nextResetTime: nextResetTime,
                currentCount: dailyLimit,
                alreadySubmitted: true // Add flag to indicate videos were already submitted
            };
        }

        // We have stats for today, check if limit is reached
        const dailyCount = stats.dailyVideos.count || 0;
        const remainingCount = Math.max(0, dailyLimit - dailyCount);

        console.log(`Daily count: ${dailyCount}/${dailyLimit}`);

        if (dailyCount >= dailyLimit) {
            return {
                limitReached: true,
                message: `You've reached your daily limit of ${dailyLimit} videos. The counter will reset at midnight.`,
                dailyCount: dailyCount,
                dailyLimit: dailyLimit,
                remainingCount: 0,
                nextResetTime: nextResetTime,
                currentCount: dailyCount
            };
        } else {
            return {
                limitReached: false,
                message: `You have ${remainingCount} videos remaining for today.`,
                dailyCount: dailyCount,
                dailyLimit: dailyLimit,
                remainingCount: remainingCount,
                nextResetTime: nextResetTime,
                currentCount: dailyCount
            };
        }

    } catch (error) {
        console.error('Error checking daily video limit:', error);
        console.warn('Using local storage due to error');
        return getLocalVideoLimit();
    }
}

/**
 * Get video limit from local storage (for testing when Firebase is not available)
 */
function getLocalVideoLimit() {
    try {
        // Get the current date in both formats for consistent comparison
        const now = new Date();
        const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
        const currentDateLocal = now.toLocaleDateString('en-US');
        const storedDate = localStorage.getItem(LOCAL_DATE_KEY);

        // Get the stored submission date if available
        const submissionDateUTC = localStorage.getItem('instra_videos_submitted_date');

        // Calculate next reset time (midnight tonight)
        const nextResetTime = new Date(now);
        nextResetTime.setHours(24, 0, 0, 0); // Set to midnight tonight

        // If it's a new day, reset the counter and submitted flag
        if (storedDate !== currentDateLocal ||
            (submissionDateUTC && submissionDateUTC !== currentDateUTC)) {

            console.log('New day detected, resetting video count and submission status');
            console.log('Date comparison:', {
                storedDate,
                currentDateLocal,
                submissionDateUTC,
                currentDateUTC,
                datesDifferent: storedDate !== currentDateLocal,
                submissionDifferent: submissionDateUTC && submissionDateUTC !== currentDateUTC
            });

            // Reset all localStorage values
            localStorage.setItem(LOCAL_DAILY_COUNT_KEY, '0');
            localStorage.setItem(LOCAL_DATE_KEY, currentDateLocal);
            localStorage.removeItem('instra_videos_submitted'); // Reset the submitted flag
            localStorage.removeItem('instra_videos_submitted_date'); // Reset the submission date

            // Try to update Firebase if user is authenticated
            try {
                const user = auth.currentUser;
                if (user) {
                    const userRef = doc(db, 'users', user.uid);

                    // Create a new video data object for the new day
                    const newVideoData = {
                        date: currentDateLocal,
                        count: 0,
                        lastUpdated: serverTimestamp(),
                        submitted: false // Reset the submitted flag for the new day
                    };

                    // Update Firebase asynchronously
                    updateDoc(userRef, {
                        // Reset today's video counts
                        'stats.todayVideos': 0,
                        'stats.todayTranslations': 0,

                        // Reset daily videos tracking
                        'stats.dailyVideos': newVideoData,
                        'stats.dailyVideos.count': 0,
                        'stats.dailyVideos.submitted': false,

                        // Reset daily translations tracking (legacy)
                        'stats.dailyTranslations': newVideoData,
                        'stats.dailyTranslations.count': 0,
                        'stats.dailyTranslations.submitted': false
                    }).then(() => {
                        console.log('Reset all video count fields in Firebase to 0 for new day in getLocalVideoLimit');
                    }).catch(error => {
                        console.error('Error updating Firebase in getLocalVideoLimit:', error);
                    });
                }
            } catch (firebaseError) {
                console.error('Error trying to update Firebase in getLocalVideoLimit:', firebaseError);
                // Continue with local storage reset even if Firebase update fails
            }

            return {
                limitReached: false,
                message: 'Daily count reset for new day (local)',
                dailyCount: 0,
                dailyLimit: 100,
                remainingCount: 100,
                nextResetTime: nextResetTime,
                currentCount: 0
            };
        }

        // Check if videos have already been submitted today
        const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';

        // Double-check with the submission date if available
        if (videosSubmitted) {
            // If we have a submission date, verify it matches today's date
            if (submissionDateUTC) {
                const isSubmittedToday = submissionDateUTC === currentDateUTC;
                console.log(`Submission date check: stored=${submissionDateUTC}, current=${currentDateUTC}, match=${isSubmittedToday}`);

                if (isSubmittedToday) {
                    console.log('Videos already submitted today according to localStorage date check');
                    return {
                        limitReached: true,
                        message: 'You have already submitted your videos for today. Please come back tomorrow.',
                        dailyCount: 100,
                        dailyLimit: 100,
                        remainingCount: 0,
                        nextResetTime: nextResetTime,
                        currentCount: 100,
                        alreadySubmitted: true, // Add flag to indicate videos were submitted
                        submissionDate: submissionDateUTC // Include the submission date
                    };
                } else {
                    // Submission date doesn't match today, reset the flag
                    console.log('Submission flag is set but date is not today, resetting flag');
                    localStorage.removeItem('instra_videos_submitted');
                    localStorage.removeItem('instra_videos_submitted_date');

                    // Continue with normal flow
                }
            } else {
                // No submission date but flag is set, assume it's for today
                console.log('Videos already submitted flag is set without date, assuming today');
                return {
                    limitReached: true,
                    message: 'You have already submitted your videos for today. Please come back tomorrow.',
                    dailyCount: 100,
                    dailyLimit: 100,
                    remainingCount: 0,
                    nextResetTime: nextResetTime,
                    currentCount: 100,
                    alreadySubmitted: true // Add flag to indicate videos were submitted
                };
            }
        }

        // Get the current count
        const dailyCount = parseInt(localStorage.getItem(LOCAL_DAILY_COUNT_KEY) || '0');
        const remainingCount = Math.max(0, 100 - dailyCount);

        if (dailyCount >= 100) {
            return {
                limitReached: true,
                message: 'You\'ve reached your daily limit of 100 videos. The counter will reset at midnight.',
                dailyCount: dailyCount,
                dailyLimit: 100,
                remainingCount: 0,
                nextResetTime: nextResetTime,
                currentCount: dailyCount
            };
        } else {
            return {
                limitReached: false,
                message: `You have ${remainingCount} videos remaining for today.`,
                dailyCount: dailyCount,
                dailyLimit: 100,
                remainingCount: remainingCount,
                nextResetTime: nextResetTime,
                currentCount: dailyCount
            };
        }
    } catch (error) {
        console.error('Error getting local video limit:', error);
        return {
            limitReached: false,
            message: 'Error checking limit, allowing videos',
            dailyCount: 0,
            dailyLimit: 100,
            remainingCount: 100,
            nextResetTime: null,
            currentCount: 0
        };
    }
}

/**
 * Increment the daily video count in Firebase
 * IMPORTANT: This function should ONLY be called when submitting all 100 videos at once
 * It should NOT be called when saving individual videos
 * Individual videos are tracked locally until submission to reduce API calls
 * @returns {Promise<Object>} - Object containing updated limit status
 */
export async function incrementDailyVideoCount() {
    try {
        console.log('Incrementing daily video count...');

        // Get current user
        const user = auth.currentUser;
        if (!user) {
            console.warn('User not authenticated, using local storage for testing');
            return incrementLocalVideoCount();
        }

        // Get the current date in user's local timezone
        const now = new Date();
        const currentDate = now.toLocaleDateString('en-US');

        // Try to get user data from cache first
        let userData = getCachedUserData(user.uid);

        if (!userData) {
            // If not in cache, get from Firebase
            console.log('No cached user data, getting from Firebase');
            const userRef = doc(db, 'users', user.uid);
            const userSnap = await getDoc(userRef);

            if (!userSnap.exists()) {
                console.warn('User document not found, using local storage for testing');
                return incrementLocalVideoCount();
            }

            userData = userSnap.data();
            // Cache the user data for future use
            cacheUserData(user.uid, userData);
        } else {
            console.log('Using cached user data for video count increment');
        }
        const stats = userData.stats || {};
        const dailyVideos = stats.dailyVideos || { date: '', count: 0 };

        // Check for todayVideos or todayTranslations as fallback
        const todayCount = stats.todayVideos || stats.todayTranslations || 0;

        // If it's a new day, reset the counter - use stats.dailyVideos.count and stats.totalVideosCompleted
        if (dailyVideos.date !== currentDate) {
            try {
                // Create a reference to the user document
                const userRef = doc(db, 'users', user.uid);

                // If we have an existing count from todayVideos or todayTranslations, use it
                const startCount = todayCount > 0 ? todayCount : 0;

                const newVideoData = {
                    date: currentDate,
                    count: startCount + 1, // Add 1 to the existing count
                    lastUpdated: serverTimestamp(),
                    submitted: false
                };

                await updateDoc(userRef, {
                    'stats.dailyVideos': newVideoData,
                    'stats.todayVideos': startCount + 1, // Update todayVideos as well
                    'stats.totalVideosCompleted': increment(1)
                });

                // Cache the updated video data
                cacheTranslationCount(user.uid, {
                    date: currentDate,
                    count: 1,
                    submitted: false
                });

                console.log('First video of the day recorded in standardized fields');
            } catch (error) {
                console.warn('Could not update daily videos:', error);
                return incrementLocalVideoCount();
            }

            return {
                success: true,
                message: 'First video of the day recorded',
                newCount: 1
            };
        }

        // Check if we've reached the daily limit
        const plan = userData.plan || {};
        const dailyLimit = plan.dailyLimit || 100; // Default to 100 for all plans

        // Check if the user has already submitted their videos for today
        if (dailyVideos.submitted === true) {
            return {
                success: false,
                message: `You have already submitted your videos for today. Please come back tomorrow.`,
                newCount: dailyLimit
            };
        }

        if (dailyVideos.count >= dailyLimit) {
            return {
                success: false,
                message: `Daily limit of ${dailyLimit} videos reached`,
                newCount: dailyVideos.count
            };
        }

        // Increment the counter - update all relevant fields for consistency
        try {
            const userRef = doc(db, 'users', user.uid);

            // Calculate the new count
            const newCount = dailyVideos.count + 1;

            await updateDoc(userRef, {
                // Primary fields for videos
                'stats.todayVideos': newCount,
                'stats.totalVideosCompleted': increment(1),

                // Structured fields for detailed tracking
                'stats.dailyVideos.count': increment(1),
                'stats.dailyVideos.lastUpdated': serverTimestamp(),

                // Legacy fields for backward compatibility
                'stats.dailyTranslations.count': increment(1),
                'stats.dailyTranslations.lastUpdated': serverTimestamp(),
                'stats.totalTranslationsCompleted': increment(1),
                'stats.todayTranslations': newCount
            });

            // Update the cached video count
            cacheTranslationCount(user.uid, {
                date: currentDate,
                count: newCount,
                submitted: dailyVideos.submitted || false
            });

            // Update the cached user data if available
            const cachedUserData = getCachedUserData(user.uid);
            if (cachedUserData && cachedUserData.stats && cachedUserData.stats.dailyVideos) {
                cachedUserData.stats.dailyVideos.count = newCount;
                cachedUserData.stats.dailyVideos.lastUpdated = new Date();
                if (cachedUserData.stats.totalVideosCompleted !== undefined) {
                    cachedUserData.stats.totalVideosCompleted += 1;
                }
                cacheUserData(user.uid, cachedUserData);
            }

            console.log('Video count incremented in standardized fields');
        } catch (error) {
            console.warn('Could not increment video count:', error);
            return incrementLocalVideoCount();
        }

        return {
            success: true,
            message: 'Video count incremented',
            newCount: dailyVideos.count + 1
        };

    } catch (error) {
        console.error('Error incrementing daily video count:', error);
        console.warn('Using local storage due to error');
        return incrementLocalVideoCount();
    }
}

/**
 * Increment video count in local storage (for testing when Firebase is not available)
 */
function incrementLocalVideoCount() {
    try {
        // Get the current date in both formats for consistent comparison
        const now = new Date();
        const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
        const currentDateLocal = now.toLocaleDateString('en-US');
        const storedDate = localStorage.getItem(LOCAL_DATE_KEY);

        // Get the stored submission date if available
        const submissionDateUTC = localStorage.getItem('instra_videos_submitted_date');

        // If it's a new day, reset the counter and submitted flag
        if (storedDate !== currentDateLocal ||
            (submissionDateUTC && submissionDateUTC !== currentDateUTC)) {

            console.log('New day detected in incrementLocalVideoCount, resetting video count and submission status');
            console.log('Date comparison in incrementLocalVideoCount:', {
                storedDate,
                currentDateLocal,
                submissionDateUTC,
                currentDateUTC,
                datesDifferent: storedDate !== currentDateLocal,
                submissionDifferent: submissionDateUTC && submissionDateUTC !== currentDateUTC
            });

            // Reset localStorage values and set count to 1 for the first video
            localStorage.setItem(LOCAL_DAILY_COUNT_KEY, '1');
            localStorage.setItem(LOCAL_DATE_KEY, currentDateLocal);
            localStorage.removeItem('instra_videos_submitted'); // Reset the submitted flag
            localStorage.removeItem('instra_videos_submitted_date'); // Reset the submission date

            // Try to update Firebase if user is authenticated
            try {
                const user = auth.currentUser;
                if (user) {
                    const userRef = doc(db, 'users', user.uid);

                    // Create a new video data object for the new day with count 1
                    const newVideoData = {
                        date: currentDateLocal,
                        count: 1, // Set to 1 for the first video
                        lastUpdated: serverTimestamp(),
                        submitted: false // Reset the submitted flag for the new day
                    };

                    // Update Firebase asynchronously
                    updateDoc(userRef, {
                        // Set today's video counts to 1
                        'stats.todayVideos': 1,
                        'stats.todayTranslations': 1,

                        // Reset daily videos tracking with count 1
                        'stats.dailyVideos': newVideoData,
                        'stats.dailyVideos.count': 1,
                        'stats.dailyVideos.submitted': false,

                        // Reset daily translations tracking (legacy) with count 1
                        'stats.dailyTranslations': newVideoData,
                        'stats.dailyTranslations.count': 1,
                        'stats.dailyTranslations.submitted': false,

                        // Increment total videos completed
                        'stats.totalVideosCompleted': increment(1),
                        'stats.totalTranslationsCompleted': increment(1)
                    }).then(() => {
                        console.log('Reset video count fields in Firebase and set to 1 for new day in incrementLocalVideoCount');
                    }).catch(error => {
                        console.error('Error updating Firebase in incrementLocalVideoCount:', error);
                    });
                }
            } catch (firebaseError) {
                console.error('Error trying to update Firebase in incrementLocalVideoCount:', firebaseError);
                // Continue with local storage reset even if Firebase update fails
            }

            return {
                success: true,
                message: 'First video of the day recorded (local)',
                newCount: 1
            };
        }

        // Check if videos have already been submitted today
        const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';

        // Double-check with the submission date if available
        if (videosSubmitted) {
            // If we have a submission date, verify it matches today's date
            if (submissionDateUTC) {
                const isSubmittedToday = submissionDateUTC === currentDateUTC;
                console.log(`Submission date check in increment: stored=${submissionDateUTC}, current=${currentDateUTC}, match=${isSubmittedToday}`);

                if (isSubmittedToday) {
                    console.log('Videos already submitted today according to localStorage date check');
                    return {
                        success: false,
                        message: 'You have already submitted your videos for today. Please come back tomorrow.',
                        newCount: 50,
                        alreadySubmitted: true
                    };
                } else {
                    // Submission date doesn't match today, reset the flag
                    console.log('Submission flag is set but date is not today, resetting flag');
                    localStorage.removeItem('instra_videos_submitted');
                    localStorage.removeItem('instra_videos_submitted_date');

                    // Continue with normal flow
                }
            } else {
                // No submission date but flag is set, assume it's for today
                console.log('Videos already submitted flag is set without date, assuming today');
                return {
                    success: false,
                    message: 'You have already submitted your videos for today. Please come back tomorrow.',
                    newCount: 100,
                    alreadySubmitted: true
                };
            }
        }

        // Get the current count
        const dailyCount = parseInt(localStorage.getItem(LOCAL_DAILY_COUNT_KEY) || '0');

        // Check if we've reached the daily limit
        if (dailyCount >= 100) {
            return {
                success: false,
                message: 'Daily limit of 100 videos reached (local)',
                newCount: dailyCount
            };
        }

        // Increment the counter
        const newCount = dailyCount + 1;
        localStorage.setItem(LOCAL_DAILY_COUNT_KEY, newCount.toString());

        return {
            success: true,
            message: 'Video count incremented (local)',
            newCount: newCount
        };
    } catch (error) {
        console.error('Error incrementing local video count:', error);
        return {
            success: true,
            message: 'Error incrementing count, but allowing for testing',
            newCount: 1
        };
    }
}

/**
 * Mark videos as submitted for the day and update Firebase
 * This function should be called when the user submits their 100 videos
 * It will update the submitted flag in Firebase and reset the count to 0
 * @returns {Promise<Object>} - Object containing success status and message
 */
export async function markVideosAsSubmitted() {
    try {
        console.log('Marking videos as submitted...');

        // Get current date first to avoid undefined variable
        const now = new Date();
        const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
        const currentDateLocal = now.toLocaleDateString('en-US');

        // Get current user
        const user = auth.currentUser;
        if (!user) {
            console.warn('User not authenticated, using local storage for testing');
            // Mark as submitted in local storage
            localStorage.setItem('instra_videos_submitted', 'true');
            localStorage.setItem('instra_videos_submitted_date', currentDateUTC);
            return {
                success: true,
                message: 'Videos marked as submitted (local)'
            };
        }

        // First check if videos have already been submitted today
        try {
            // Check Firebase first - this is the source of truth
            const userRef = doc(db, 'users', user.uid);
            const userSnap = await getDoc(userRef);

            if (userSnap.exists()) {
                const userData = userSnap.data();
                const stats = userData.stats || {};

                // Check if dailyVideos exists and has been submitted today
                if (stats.dailyVideos && stats.dailyVideos.submitted === true) {
                    // Check if the submission was today
                    const submissionDate = stats.dailyVideos.date;

                    // If submission date matches current date, user has already submitted today
                    if (submissionDate === currentDateUTC || submissionDate === currentDateLocal) {
                        console.log('User has already submitted videos for today according to Firebase');

                        // Set localStorage flag to ensure consistent state
                        localStorage.setItem('instra_videos_submitted', 'true');
                        localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

                        return {
                            success: false,
                            message: 'You have already submitted your videos for today. Please come back tomorrow.',
                            alreadySubmitted: true
                        };
                    } else {
                        // If the submission date doesn't match today, reset the submitted flag
                        console.log('Submission flag is true but date doesn\'t match today. Resetting submission status.');

                        try {
                            // Update Firebase to reset the submitted flag
                            await updateDoc(userRef, {
                                'stats.dailyVideos.submitted': false,
                                'stats.dailyTranslations.submitted': false
                            });
                            console.log('Reset submission flag in Firebase due to date mismatch');
                        } catch (resetError) {
                            console.error('Error resetting submission flag in Firebase:', resetError);
                        }
                    }
                }

                // Also check if todayVideos or todayTranslations is 100 but lastSubmission doesn't match today
                if ((stats.todayVideos === 100 || stats.todayTranslations === 100) &&
                    (!stats.lastSubmission || stats.lastSubmission.date !== currentDateUTC)) {
                    console.log('Found todayVideos/todayTranslations count of 100, but lastSubmission date doesn\'t match today');
                    console.log('This indicates videos are ready to submit but have not been submitted yet');

                    // Don't mark as submitted, just log the state
                }
            }
        } catch (checkError) {
            console.error('Error checking if videos were already submitted:', checkError);
            // Continue with submission attempt
        }

        // We already have the date variables defined above, no need to redefine

        try {
            // Create a reference to the user document
            const userRef = doc(db, 'users', user.uid);

            // Get the cached video data
            const cachedVideoData = getCachedTranslationCount(user.uid);

            // Always set the video count to 100 when submitting
            const videoCount = 100; // Force to 100 for consistency

            // Update the submitted flag in Firebase and ensure all fields are updated
            // IMPORTANT: Keep todayVideos at 100 to indicate completion
            await updateDoc(userRef, {
                // Primary fields for videos - always set to 100 when submitting
                'stats.todayVideos': videoCount,

                // IMPORTANT: Update total videos count - this is critical for tracking progress
                'stats.totalVideosCompleted': increment(videoCount),
                'videosCount': increment(videoCount), // Legacy field for backward compatibility

                // Structured fields for detailed tracking
                'stats.dailyVideos.submitted': true,
                'stats.dailyVideos.lastUpdated': serverTimestamp(),
                'stats.dailyVideos.date': currentDateUTC, // Store UTC date for consistent comparison
                'stats.dailyVideos.submissionTimestamp': serverTimestamp(), // Add server timestamp
                'stats.dailyVideos.count': videoCount, // Ensure count is set to 100

                // Legacy fields for backward compatibility
                'stats.dailyTranslations.submitted': true,
                'stats.dailyTranslations.lastUpdated': serverTimestamp(),
                'stats.dailyTranslations.date': currentDateUTC, // Store UTC date for consistent comparison
                'stats.todayTranslations': videoCount,
                'stats.dailyTranslations.count': videoCount, // Ensure count is set to 100
                'stats.totalTranslationsCompleted': increment(videoCount), // Legacy field for backward compatibility

                // Add a submission record with timestamp for audit purposes
                'stats.lastSubmission': {
                    date: currentDateUTC,
                    localDate: currentDateLocal,
                    timestamp: serverTimestamp(),
                    count: videoCount,
                    submitted: true // Explicitly mark as submitted
                }
            });

            // Update the cached video count in memory
            if (cachedVideoData) {
                cachedVideoData.submitted = true;
                cachedVideoData.date = currentDateUTC;
                cacheTranslationCount(user.uid, cachedVideoData);
            }

            // Update the cached user data if available
            const cachedUserData = getCachedUserData(user.uid);
            if (cachedUserData && cachedUserData.stats && cachedUserData.stats.dailyVideos) {
                cachedUserData.stats.dailyVideos.submitted = true;
                cachedUserData.stats.dailyVideos.date = currentDateUTC;
                cachedUserData.stats.dailyVideos.lastUpdated = new Date();
                cacheUserData(user.uid, cachedUserData);
            }

            // Also mark as submitted in local storage as a backup
            localStorage.setItem('instra_videos_submitted', 'true');

            // Store submission date in localStorage for additional verification
            localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

            console.log('Videos marked as submitted in Firebase with date:', currentDateUTC);
            return {
                success: true,
                message: 'Videos marked as submitted',
                submissionDate: currentDateUTC
            };
        } catch (error) {
            console.warn('Could not mark videos as submitted in Firebase:', error);
            // Mark as submitted in local storage as a fallback
            localStorage.setItem('instra_videos_submitted', 'true');
            localStorage.setItem('instra_videos_submitted_date', currentDateUTC);
            return {
                success: true,
                message: 'Videos marked as submitted (local fallback)',
                submissionDate: currentDateUTC
            };
        }
    } catch (error) {
        console.error('Error marking videos as submitted:', error);
        // Mark as submitted in local storage as a fallback
        localStorage.setItem('instra_videos_submitted', 'true');

        // Get the current date in UTC format
        const currentDateUTC = (new Date()).toISOString().split('T')[0];
        localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

        return {
            success: true,
            message: 'Error marking videos as submitted, but marked locally',
            submissionDate: currentDateUTC
        };
    }
}

/**
 * Update user's wallet with earnings for completed videos
 * @param {number} earningsAmount - Amount to add to the earning wallet
 * @param {number} retryCount - Number of retries attempted (internal use)
 * @returns {Promise<Object>} - Object containing success status and message
 */
export async function updateWalletWithEarnings(earningsAmount, retryCount = 0) {
    try {
        console.log(`Updating wallet with earnings: ₹${earningsAmount} (retry: ${retryCount})`);

        // Validate earnings amount
        const validEarningsAmount = parseFloat(earningsAmount);
        if (isNaN(validEarningsAmount) || validEarningsAmount <= 0) {
            console.error('Invalid earnings amount:', earningsAmount);
            return {
                success: false,
                message: `Invalid earnings amount: ${earningsAmount}`
            };
        }

        // Get current user
        const user = auth.currentUser;
        if (!user) {
            console.warn('User not authenticated, cannot update wallet');
            return {
                success: false,
                message: 'User not authenticated'
            };
        }

        try {
            // Create a reference to the user document
            const userRef = doc(db, 'users', user.uid);

            // Get current wallet data to verify update
            const userDoc = await getDoc(userRef);
            if (!userDoc.exists()) {
                console.error('User document not found');

                // If this is the first attempt, try to create the user document with a wallet
                if (retryCount === 0) {
                    try {
                        console.log('User document not found, attempting to create wallet structure');
                        await updateDoc(userRef, {
                            'wallet': {
                                earning: validEarningsAmount,
                                balance: 0,
                                bonus: 0
                            },
                            'walletUpdatedAt': serverTimestamp()
                        });

                        // Verify the creation was successful
                        const createdDoc = await getDoc(userRef);
                        if (createdDoc.exists()) {
                            const createdData = createdDoc.data();
                            const createdWallet = createdData.wallet || {};
                            const createdEarning = parseFloat(createdWallet.earning) || 0;

                            console.log('Created wallet structure with earnings:', createdEarning);
                            return {
                                success: true,
                                message: `Created wallet with ₹${validEarningsAmount} in earnings`,
                                previousAmount: 0,
                                newAmount: createdEarning,
                                walletCreated: true
                            };
                        }
                    } catch (createError) {
                        console.error('Failed to create wallet structure:', createError);
                    }
                }

                return {
                    success: false,
                    message: 'User document not found'
                };
            }

            const userData = userDoc.data();
            console.log('Current user data before wallet update:', userData);

            // Check if wallet exists, create it if it doesn't
            if (!userData.wallet) {
                console.log('Wallet object does not exist, creating it first');
                try {
                    await updateDoc(userRef, {
                        'wallet': {
                            earning: validEarningsAmount,
                            balance: 0,
                            bonus: 0
                        },
                        'walletUpdatedAt': serverTimestamp()
                    });

                    console.log('Created wallet with initial earnings:', validEarningsAmount);
                    return {
                        success: true,
                        message: `Created wallet with ₹${validEarningsAmount} in earnings`,
                        previousAmount: 0,
                        newAmount: validEarningsAmount,
                        walletCreated: true
                    };
                } catch (createWalletError) {
                    console.error('Failed to create wallet:', createWalletError);
                    return {
                        success: false,
                        message: 'Failed to create wallet: ' + (createWalletError.message || 'Unknown error')
                    };
                }
            }

            // Wallet exists, proceed with update
            const currentWallet = userData.wallet || {};
            const currentEarning = parseFloat(currentWallet.earning) || 0;
            const currentBalance = parseFloat(currentWallet.balance) || 0;

            console.log('Current wallet before update:', currentWallet);
            console.log('Current earning amount:', currentEarning);
            console.log('Current balance amount:', currentBalance); // For reference only
            console.log('Amount to add to earning:', validEarningsAmount);
            console.log('Expected new earning amount:', currentEarning + validEarningsAmount);

            // Update the wallet in Firebase - only update the earning field
            // The balance field should only be updated when user transfers from earning wallet
            await updateDoc(userRef, {
                'wallet.earning': increment(validEarningsAmount),
                'walletUpdatedAt': serverTimestamp() // Add timestamp for tracking
            });

            // Verify the update was successful
            const updatedDoc = await getDoc(userRef);
            const updatedData = updatedDoc.data();
            const updatedWallet = updatedData.wallet || {};
            const updatedEarning = parseFloat(updatedWallet.earning) || 0;

            console.log('Updated wallet after update:', updatedWallet);
            console.log('Updated earning amount:', updatedEarning);

            // Check if the update was successful
            if (Math.abs(updatedEarning - (currentEarning + validEarningsAmount)) < 0.01) {
                console.log(`Wallet successfully updated with ₹${validEarningsAmount} in earnings`);

                // Store successful update in localStorage for recovery purposes
                try {
                    const successRecord = {
                        timestamp: new Date().toISOString(),
                        previousAmount: currentEarning,
                        addedAmount: validEarningsAmount,
                        newAmount: updatedEarning,
                        userId: user.uid
                    };
                    localStorage.setItem('last_successful_wallet_update', JSON.stringify(successRecord));
                } catch (storageError) {
                    console.warn('Could not store wallet update record in localStorage:', storageError);
                }

                return {
                    success: true,
                    message: `Wallet updated with ₹${validEarningsAmount} in earnings`,
                    previousAmount: currentEarning,
                    newAmount: updatedEarning
                };
            } else {
                console.warn(`Wallet update verification failed. Expected: ${currentEarning + validEarningsAmount}, Actual: ${updatedEarning}`);

                // If this is not the final retry, attempt again
                if (retryCount < 2) {
                    console.log(`Retrying wallet update (attempt ${retryCount + 1} of 2)...`);

                    // Wait a short time before retrying
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Try a direct update with the exact amount instead of increment
                    try {
                        const exactAmount = currentEarning + validEarningsAmount;
                        await updateDoc(userRef, {
                            'wallet.earning': exactAmount,
                            'walletUpdatedAt': serverTimestamp(),
                            'walletUpdateRetried': true
                        });

                        console.log(`Direct wallet update with exact amount: ${exactAmount}`);

                        // Verify the direct update
                        const directDoc = await getDoc(userRef);
                        const directData = directDoc.data();
                        const directWallet = directData.wallet || {};
                        const directEarning = parseFloat(directWallet.earning) || 0;

                        if (Math.abs(directEarning - exactAmount) < 0.01) {
                            console.log(`Direct wallet update successful: ${directEarning}`);
                            return {
                                success: true,
                                message: `Wallet updated with direct method: ₹${validEarningsAmount} in earnings`,
                                previousAmount: currentEarning,
                                newAmount: directEarning,
                                directUpdateUsed: true
                            };
                        } else {
                            console.warn(`Direct wallet update verification failed. Expected: ${exactAmount}, Actual: ${directEarning}`);
                            // Recursive retry with increment
                            return await updateWalletWithEarnings(validEarningsAmount, retryCount + 1);
                        }
                    } catch (directError) {
                        console.error('Direct wallet update failed:', directError);
                        // Recursive retry with increment
                        return await updateWalletWithEarnings(validEarningsAmount, retryCount + 1);
                    }
                }

                // Store failed update in localStorage for recovery
                try {
                    const failedUpdate = {
                        timestamp: new Date().toISOString(),
                        userId: user.uid,
                        amount: validEarningsAmount,
                        previousAmount: currentEarning,
                        actualAmount: updatedEarning,
                        expectedAmount: currentEarning + validEarningsAmount
                    };

                    // Get existing failed updates
                    let failedUpdates = [];
                    const storedFailures = localStorage.getItem('failed_wallet_updates');
                    if (storedFailures) {
                        try {
                            failedUpdates = JSON.parse(storedFailures);
                            if (!Array.isArray(failedUpdates)) failedUpdates = [];
                        } catch (parseError) {
                            failedUpdates = [];
                        }
                    }

                    // Add this failure and store
                    failedUpdates.push(failedUpdate);
                    localStorage.setItem('failed_wallet_updates', JSON.stringify(failedUpdates));
                    console.log('Stored failed wallet update for recovery:', failedUpdate);
                } catch (storageError) {
                    console.warn('Could not store failed wallet update in localStorage:', storageError);
                }

                // Return failure after all retries
                return {
                    success: false,
                    message: `Wallet update verification failed after retries. Expected: ${currentEarning + validEarningsAmount}, Actual: ${updatedEarning}`,
                    previousAmount: currentEarning,
                    newAmount: updatedEarning,
                    verificationFailed: true
                };
            }
        } catch (error) {
            console.error('Could not update wallet in Firebase:', error);
            console.error('Error details:', {
                userId: user.uid,
                earningsAmount: validEarningsAmount,
                errorMessage: error.message || 'Unknown error',
                errorStack: error.stack
            });

            // Store failed update in localStorage for recovery
            try {
                const failedUpdate = {
                    timestamp: new Date().toISOString(),
                    userId: user.uid,
                    amount: validEarningsAmount,
                    error: error.message || 'Unknown error'
                };

                // Get existing failed updates
                let failedUpdates = [];
                const storedFailures = localStorage.getItem('failed_wallet_updates');
                if (storedFailures) {
                    try {
                        failedUpdates = JSON.parse(storedFailures);
                        if (!Array.isArray(failedUpdates)) failedUpdates = [];
                    } catch (parseError) {
                        failedUpdates = [];
                    }
                }

                // Add this failure and store
                failedUpdates.push(failedUpdate);
                localStorage.setItem('failed_wallet_updates', JSON.stringify(failedUpdates));
                console.log('Stored failed wallet update for recovery:', failedUpdate);
            } catch (storageError) {
                console.warn('Could not store failed wallet update in localStorage:', storageError);
            }

            return {
                success: false,
                message: 'Could not update wallet: ' + (error.message || 'Unknown error')
            };
        }
    } catch (error) {
        console.error('Error updating wallet with earnings:', error);
        return {
            success: false,
            message: 'Error updating wallet: ' + (error.message || 'Unknown error')
        };
    }
}

/**
 * Attempt to recover any failed wallet updates from localStorage
 * This function should be called when the user logs in or navigates to the dashboard
 * @returns {Promise<Object>} - Object containing recovery results
 */
export async function recoverFailedWalletUpdates() {
    try {
        console.log('Checking for failed wallet updates to recover...');

        // Get failed updates from localStorage
        const storedFailures = localStorage.getItem('failed_wallet_updates');
        if (!storedFailures) {
            console.log('No failed wallet updates found in localStorage');
            return { recovered: false, message: 'No failed updates found' };
        }

        // Parse the stored failures
        let failedUpdates = [];
        try {
            failedUpdates = JSON.parse(storedFailures);
            if (!Array.isArray(failedUpdates) || failedUpdates.length === 0) {
                console.log('No valid failed wallet updates found in localStorage');
                localStorage.removeItem('failed_wallet_updates');
                return { recovered: false, message: 'No valid failed updates found' };
            }
        } catch (parseError) {
            console.error('Error parsing failed wallet updates:', parseError);
            localStorage.removeItem('failed_wallet_updates');
            return { recovered: false, message: 'Error parsing failed updates' };
        }

        console.log(`Found ${failedUpdates.length} failed wallet updates to recover`);

        // Get current user
        const user = auth.currentUser;
        if (!user) {
            console.warn('User not authenticated, cannot recover wallet updates');
            return { recovered: false, message: 'User not authenticated' };
        }

        // Process each failed update
        const results = [];
        let totalRecovered = 0;

        for (const failedUpdate of failedUpdates) {
            // Skip updates for other users
            if (failedUpdate.userId && failedUpdate.userId !== user.uid) {
                console.log(`Skipping update for different user: ${failedUpdate.userId}`);
                continue;
            }

            // Skip updates without an amount
            if (!failedUpdate.amount || isNaN(parseFloat(failedUpdate.amount)) || parseFloat(failedUpdate.amount) <= 0) {
                console.log('Skipping update with invalid amount:', failedUpdate.amount);
                continue;
            }

            // Attempt to apply the update
            try {
                console.log(`Attempting to recover wallet update: ₹${failedUpdate.amount}`);

                // Get current wallet data
                const userRef = doc(db, 'users', user.uid);
                const userDoc = await getDoc(userRef);

                if (!userDoc.exists()) {
                    console.error('User document not found during recovery');
                    results.push({
                        amount: failedUpdate.amount,
                        success: false,
                        message: 'User document not found'
                    });
                    continue;
                }

                const userData = userDoc.data();
                const currentWallet = userData.wallet || {};
                const currentEarning = parseFloat(currentWallet.earning) || 0;

                // Update the wallet with the failed amount
                await updateDoc(userRef, {
                    'wallet.earning': increment(parseFloat(failedUpdate.amount)),
                    'walletUpdatedAt': serverTimestamp(),
                    'walletRecoveryApplied': true
                });

                // Verify the update
                const updatedDoc = await getDoc(userRef);
                const updatedData = updatedDoc.data();
                const updatedWallet = updatedData.wallet || {};
                const updatedEarning = parseFloat(updatedWallet.earning) || 0;

                if (Math.abs(updatedEarning - (currentEarning + parseFloat(failedUpdate.amount))) < 0.01) {
                    console.log(`Successfully recovered wallet update: ₹${failedUpdate.amount}`);
                    totalRecovered += parseFloat(failedUpdate.amount);

                    // Add to results
                    results.push({
                        amount: failedUpdate.amount,
                        success: true,
                        previousAmount: currentEarning,
                        newAmount: updatedEarning
                    });

                    // Create a transaction record for the recovered amount
                    try {
                        const transactionRef = await addDoc(collection(db, 'transactions'), {
                            userId: user.uid,
                            timestamp: serverTimestamp(),
                            type: 'earnings',
                            amount: parseFloat(failedUpdate.amount),
                            status: 'completed',
                            description: `Recovered earnings from failed update on ${new Date().toLocaleDateString()}`,
                            recoveryDetails: {
                                originalTimestamp: failedUpdate.timestamp,
                                recoveredAt: new Date().toISOString()
                            }
                        });

                        console.log('Created transaction record for recovered amount:', transactionRef.id);
                    } catch (transactionError) {
                        console.error('Error creating transaction record for recovered amount:', transactionError);
                        // Continue even if transaction creation fails
                    }
                } else {
                    console.warn(`Recovery verification failed. Expected: ${currentEarning + parseFloat(failedUpdate.amount)}, Actual: ${updatedEarning}`);

                    // Add to results
                    results.push({
                        amount: failedUpdate.amount,
                        success: false,
                        message: 'Verification failed',
                        previousAmount: currentEarning,
                        newAmount: updatedEarning
                    });
                }
            } catch (error) {
                console.error('Error recovering wallet update:', error);

                // Add to results
                results.push({
                    amount: failedUpdate.amount,
                    success: false,
                    message: error.message || 'Unknown error'
                });
            }
        }

        // Clear the failed updates from localStorage if any were processed
        if (results.length > 0) {
            localStorage.removeItem('failed_wallet_updates');
            console.log('Cleared failed wallet updates from localStorage');
        }

        // Return the results
        return {
            recovered: totalRecovered > 0,
            totalRecovered,
            results,
            message: totalRecovered > 0 ?
                `Recovered ₹${totalRecovered} from failed wallet updates` :
                'No wallet updates were recovered'
        };
    } catch (error) {
        console.error('Error in recoverFailedWalletUpdates:', error);
        return {
            recovered: false,
            message: 'Error recovering failed wallet updates: ' + (error.message || 'Unknown error')
        };
    }
}

// Make the functions available globally
window.checkDailyVideoLimit = checkDailyVideoLimit;
window.incrementDailyVideoCount = incrementDailyVideoCount;
window.markVideosAsSubmitted = markVideosAsSubmitted;
window.updateWalletWithEarnings = updateWalletWithEarnings;
window.recoverFailedWalletUpdates = recoverFailedWalletUpdates;

// Export the functions
export default {
    checkDailyVideoLimit,
    incrementDailyVideoCount,
    markVideosAsSubmitted,
    updateWalletWithEarnings,
    recoverFailedWalletUpdates
};
