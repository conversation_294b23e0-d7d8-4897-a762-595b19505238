/* Global Variables */
:root {
    --primary-color: #22c55e;
    --secondary-color: #16a34a;
    --accent-color: #15803d;
    --text-color: #1f2937;
    --bg-color: #ffffff;
    --card-bg: rgba(34, 197, 94, 0.1);
}

/* Common Page Styles */
body {
    background: var(--bg-color);
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
    margin: 0;
    min-height: 100vh;
    padding: 0;
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--bg-color);
    overflow: hidden;
}

.animated-bg::before,
.animated-bg::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0.1;
    filter: blur(100px);
}

.animated-bg::before {
    top: -150px;
    left: -150px;
    animation: float 8s infinite;
}

.animated-bg::after {
    bottom: -150px;
    right: -150px;
    animation: float 8s infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(100px, 100px); }
}

/* Header Styles */
.page-header {
    padding: 1rem;
    margin-bottom: 2rem;
    position: relative;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.back-button {
    position: fixed;
    left: 2rem;
    top: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: rgba(34, 197, 94, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    z-index: 100;
}

.back-button i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.back-button:hover {
    transform: translateX(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.8rem;
    text-align: center;
    margin: 0 auto;
}

/* Card Styles */
.glass-card {
    background: var(--bg-card);
    border: var(--glass-border);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s, box-shadow 0.3s;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--glass-shadow);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-light);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s, box-shadow 0.3s;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-light);
    border: var(--glass-border);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s;
}

.btn-secondary:hover {
    transform: translateY(-2px);
}

/* Text Styles */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 600;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Status Colors */
.status-success { color: #10B981; }
.status-pending { color: #F59E0B; }
.status-error { color: #EF4444; }

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .grid {
        grid-template-columns: 1fr;
    }

    .glass-card {
        padding: 1rem;
    }

    /* Center content on mobile */
    body {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}

/* Common Page Layout */
.page-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    min-height: 100vh;
    background: var(--bg-dark);
    color: var(--text-primary);
    animation: fadeIn 0.5s ease-out;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    padding-top: 5rem;
    box-sizing: border-box;
}

.page-content {
    margin-left: 280px;
    padding: 30px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-radius: 20px;
    margin-bottom: 30px;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.header-icon:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transform: translateY(-2px);
}

.header-icon i {
    font-size: 1.2rem;
}

.header-logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: rgba(239, 68, 68, 0.2);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.header-logout-btn:hover {
    background: rgba(239, 68, 68, 0.4);
    transform: translateY(-2px);
}

.header-logout-btn i {
    color: #ef4444;
}

.page-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.page-header p {
    color: rgba(255, 255, 255, 0.7);
}

/* Work Page */
.work-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.work-form {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.work-form h2 {
    font-size: 1.3rem;
    margin: 0 0 20px 0;
}



/* Wallet Page */
.wallet-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.wallet-card {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    text-align: center;
}

.wallet-card h3 {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0 0 15px 0;
}

.balance {
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 20px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Transactions Page */
.transactions-list {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 15px;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.transaction-info {
    flex: 1;
}

.transaction-amount {
    font-weight: 600;
    color: var(--text-primary);
}

/* Refer Page */
.refer-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.refer-info {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.referral-link {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.refer-stats {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

/* Profile Page */
.profile-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.profile-card {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.profile-pic {
    text-align: center;
    margin-bottom: 30px;
}

.profile-pic img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.1);
    padding: 5px;
}

.danger-zone {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .wallet-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1024px) {
    .work-container,
    .refer-container,
    .profile-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .page-container {
        grid-template-columns: 1fr;
        padding: 1rem;
        padding-top: 4rem;
        max-width: 100%;
        box-sizing: border-box;
    }

    .page-content {
        margin-left: 0;
        padding: 20px;
    }

    .wallet-grid {
        grid-template-columns: 1fr;
    }

    .header-actions {
        gap: 10px;
    }

    .header-icon {
        width: 35px;
        height: 35px;
    }

    .header-icon i {
        font-size: 1rem;
    }

    .header-logout-btn {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    .header-logout-btn span {
        display: none;
    }

    /* Fix for How It Works and FAQ pages */
    .main-content {
        padding: 0.5rem;
        width: 100%;
        box-sizing: border-box;
        overflow-x: hidden;
    }

    .glass-card {
        padding: 1rem;
        margin-bottom: 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }

    .step-card {
        flex-direction: column;
        align-items: flex-start;
    }

    .step-number {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .feature-grid {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .feature-card {
        width: 100% !important;
        margin-bottom: 1rem;
    }

    /* Table responsiveness */
    .copy-paste-table {
        font-size: 0.85rem;
        width: 100%;
        display: block;
        overflow-x: auto;
    }

    .copy-paste-table th,
    .copy-paste-table td {
        padding: 0.6rem;
    }

    /* Fix for FAQ accordion */
    .faq-item {
        margin-bottom: 1rem;
        width: 100%;
    }

    .faq-question {
        font-size: 0.95rem;
        padding: 0.75rem;
    }

    .faq-answer {
        padding: 0.75rem;
        font-size: 0.85rem;
    }

    /* Fix for category buttons */
    .faq-categories {
        flex-wrap: wrap;
        justify-content: center;
    }

    .category-btn {
        margin-bottom: 0.5rem;
        font-size: 0.85rem;
        padding: 0.4rem 0.7rem;
    }

    /* Fix for steps container */
    .steps-container {
        width: 100%;
    }

    /* Fix for gradient text */
    .gradient-text {
        font-size: 1.5rem;
    }

    /* Fix for back button */
    .back-button {
        position: fixed;
        left: 1rem;
        top: 1rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }
}

/* Back to Dashboard Button */
.back-to-dashboard {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.back-to-dashboard:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-5px);
}

.back-to-dashboard i {
    font-size: 1.1rem;
}

/* Glass Card Effect */
.glass-effect {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Card Grid */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Stats Card */
.stats-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
}

.stats-card i {
    font-size: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stats-info h3 {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stats-info p {
    font-size: 1.5rem;
    font-weight: 600;
}

/* Action Buttons */
.action-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 8px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    background: var(--card-bg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* Table Styles */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.data-table th {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #2c3e50;
}

.progress-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.progress-bar {
    width: 300px;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.qr-count {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

/* Main Content Styles */
.data-entry {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.correct-data, .input-form {
    padding: 20px;
}

.correct-data h3, .input-form h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.5rem;
}

.data-fields, .form-fields {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.field {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.field label {
    font-weight: bold;
    color: #555;
}

.field input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.field input:focus {
    outline: none;
    border-color: #4CAF50;
}

.field span {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    min-height: 40px;
}

/* Button Styles */
.generate-btn {
    margin-top: 20px;
    padding: 12px 24px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.generate-btn:hover {
    background-color: #45a049;
}

.generate-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .data-entry {
        grid-template-columns: 1fr;
    }

    .progress-bar {
        width: 200px;
    }

    header h1 {
        font-size: 2rem;
    }
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.card-title {
    font-size: 1.2rem;
    color: #2c3e50;
    font-weight: 600;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #4CAF50;
}

/* Table Styles */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

/* Status Colors */
.status-success {
    color: #4CAF50;
}

.status-error {
    color: #f44336;
}

.status-warning {
    color: #ff9800;
}

.status-info {
    color: #2196F3;
}

/* Action Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
}

.btn-secondary {
    background-color: #2196F3;
    color: white;
}

.btn-secondary:hover {
    background-color: #1976D2;
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

/* Grid Layout */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Section Styles */
.section {
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
}

/* Alert Styles */
.alert {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.alert-error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.alert-warning {
    background-color: #fcf8e3;
    color: #8a6d3b;
    border: 1px solid #faebcc;
}

.alert-info {
    background-color: #d9edf7;
    color: #31708f;
    border: 1px solid #bce8f1;
}
.password-hint {
    color: rgb(177, 147, 9);
  }
