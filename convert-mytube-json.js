/**
 * MyTube JSON Converter
 * Converts the current mytube.json format to a cleaner format with valid YouTube URLs
 */

const fs = require('fs');
const path = require('path');

// Function to validate and fix YouTube URL
function validateAndFixYouTubeUrl(url) {
    if (!url || typeof url !== 'string') return null;
    
    // Extract video ID from various YouTube URL formats
    let videoId = null;
    
    // Standard YouTube URL: https://www.youtube.com/watch?v=VIDEO_ID
    let match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
    if (match) {
        videoId = match[1];
    }
    
    // If no valid video ID found, try to extract from the end of the URL
    if (!videoId) {
        match = url.match(/([a-zA-Z0-9_-]{11})$/);
        if (match) {
            videoId = match[1];
        }
    }
    
    // If still no video ID, check if the URL itself might be a video ID
    if (!videoId && url.length === 11 && /^[a-zA-Z0-9_-]+$/.test(url)) {
        videoId = url;
    }
    
    // Validate video ID format (YouTube video IDs are 11 characters)
    if (videoId && videoId.length === 11 && /^[a-zA-Z0-9_-]+$/.test(videoId)) {
        // Return properly formatted YouTube URL
        return `https://www.youtube.com/watch?v=${videoId}`;
    }
    
    return null;
}

// Function to check if video ID looks problematic
function isProblematicVideoId(videoId) {
    const problematicPatterns = [
        /^.{1,10}$/, // Too short
        /^.{12,}$/, // Too long
        /[^a-zA-Z0-9_-]/, // Invalid characters
        /^video_\d+$/, // Generic placeholder
        /^\d+$/, // Only numbers
        /^[a-z]+$/, // Only lowercase letters
    ];
    
    return problematicPatterns.some(pattern => pattern.test(videoId));
}

// Reliable sample videos
const reliableSamples = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Rick Roll
    'https://www.youtube.com/watch?v=jNQXAC9IVRw', // Me at the zoo
    'https://www.youtube.com/watch?v=kJQP7kiw5Fk', // Despacito
    'https://www.youtube.com/watch?v=9bZkp7q19f0', // Gangnam Style
    'https://www.youtube.com/watch?v=OPf0YbXqDm0', // Uptown Funk
    'https://www.youtube.com/watch?v=fJ9rUzIMcZQ', // Bohemian Rhapsody
    'https://www.youtube.com/watch?v=YQHsXMglC9A', // Hello - Adele
    'https://www.youtube.com/watch?v=hTWKbfoikeg', // Smells Like Teen Spirit
    'https://www.youtube.com/watch?v=L_jWHffIx5E', // Alternative
    'https://www.youtube.com/watch?v=rYEDA3JcQqw', // Rolling in the Deep
];

async function convertMyTubeJson() {
    try {
        console.log('🔄 Converting mytube.json...');
        
        // Read the current mytube.json file
        const inputFile = 'mytube.json';
        const outputFile = 'mytube-converted.json';
        
        if (!fs.existsSync(inputFile)) {
            console.error('❌ mytube.json file not found!');
            return;
        }
        
        console.log('📖 Reading mytube.json...');
        const rawData = fs.readFileSync(inputFile, 'utf8');
        
        let jsonData;
        try {
            jsonData = JSON.parse(rawData);
        } catch (parseError) {
            console.error('❌ Error parsing JSON:', parseError.message);
            return;
        }
        
        console.log(`📊 Found ${jsonData.length} entries in mytube.json`);
        
        const validUrls = [];
        const skippedUrls = [];
        
        // Process each entry
        for (const obj of jsonData) {
            // Extract the target URL (value) from each object
            const targetUrl = Object.values(obj)[0];
            
            if (targetUrl && typeof targetUrl === 'string') {
                // Validate and fix the YouTube URL
                const validUrl = validateAndFixYouTubeUrl(targetUrl);
                
                if (validUrl) {
                    // Extract video ID to check for problematic patterns
                    const videoId = validUrl.match(/v=([a-zA-Z0-9_-]{11})/)?.[1];
                    
                    if (videoId && !isProblematicVideoId(videoId)) {
                        validUrls.push(validUrl);
                    } else {
                        skippedUrls.push(targetUrl);
                    }
                } else {
                    skippedUrls.push(targetUrl);
                }
            }
        }
        
        // Remove duplicates
        const uniqueUrls = [...new Set(validUrls)];
        
        // Add reliable samples if we don't have enough
        if (uniqueUrls.length < 100) {
            console.log(`⚠️  Only ${uniqueUrls.length} valid videos found, adding reliable samples`);
            uniqueUrls.push(...reliableSamples);
        }
        
        // Remove duplicates again after adding samples
        const finalUrls = [...new Set(uniqueUrls)];
        
        console.log(`✅ Conversion complete:`);
        console.log(`   📥 Original entries: ${jsonData.length}`);
        console.log(`   ✅ Valid URLs: ${validUrls.length}`);
        console.log(`   ❌ Skipped URLs: ${skippedUrls.length}`);
        console.log(`   🔄 Unique URLs: ${uniqueUrls.length}`);
        console.log(`   📤 Final URLs: ${finalUrls.length}`);
        
        // Write the converted file
        fs.writeFileSync(outputFile, JSON.stringify(finalUrls, null, 2));
        console.log(`💾 Converted file saved as: ${outputFile}`);
        
        // Show some examples of skipped URLs
        if (skippedUrls.length > 0) {
            console.log(`\n❌ Examples of skipped URLs:`);
            skippedUrls.slice(0, 10).forEach(url => {
                console.log(`   - ${url}`);
            });
        }
        
        console.log(`\n🎯 To use the converted file:`);
        console.log(`   1. Backup your current mytube.json: mv mytube.json mytube-backup.json`);
        console.log(`   2. Replace with converted file: mv mytube-converted.json mytube.json`);
        console.log(`   3. Clear browser cache and localStorage`);
        console.log(`   4. Refresh the work page`);
        
    } catch (error) {
        console.error('❌ Error during conversion:', error);
    }
}

// Run the conversion
convertMyTubeJson();
