/* Base styles */
.auth-container {
    max-width: 350px;
    width: 90%;
    padding: 1.5rem;
    margin: 3rem auto 1rem auto;
    background: rgba(34, 197, 94, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    height: auto;
    position: relative;
}

body {
    min-height: 100vh;
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    margin: 0;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.auth-container h1 {
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: #16a34a;
    font-weight: 600;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: #16a34a;
    font-weight: 500;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group label i {
    font-size: 1rem;
    color: var(--primary-purple);
}

.form-group input {
    padding: 0.8rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-purple);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.form-group input::placeholder {
    color: #999;
}

/* Optional field styling */
.form-group input:not([required]) {
    background: rgba(255, 255, 255, 0.05);
}

/* Login Button Styles */
.btn-primary.login-btn {
    background: linear-gradient(135deg, #8b5cf6, #ec4899);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.btn-primary.login-btn i {
    font-size: 1.2rem;
    color: white;
    transition: transform 0.3s ease;
}

.btn-primary.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.5);
}

.btn-primary.login-btn:hover i {
    transform: translateX(4px);
}

/* Password Input Group */
.password-input-group {
    position: relative;
    width: 100%;
}

.password-input-group input {
    width: 100%;
    padding-right: 40px !important;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: #ffffff;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #8b5cf6;
}

.toggle-password i {
    font-size: 1.1rem;
}

.auth-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
    text-align: center;
}

.auth-options p {
    color: white;
    font-size: 0.95rem;
}

.auth-options a {
    color: #8b5cf6;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.auth-options a:hover {
    color: #ec4899;
    text-decoration: underline;
}

/* Help Links */
.help-links {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.help-link {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem !important;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.help-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    text-decoration: none !important;
}

.back-button {
    position: fixed;
    top: 1rem;
    left: 1rem;
    color: #fff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    z-index: 100;
    background: rgba(0, 0, 0, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.back-button:hover {
    color: var(--primary-purple);
    background: rgba(0, 0, 0, 0.3);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .auth-container {
        width: 100%;
        max-width: none;
        margin: 3.5rem 0 0 0;
        border-radius: 0;
        min-height: calc(100vh - 3.5rem);
        padding: 2rem 1.5rem;
    }

    body {
        padding: 0;
    }

    .back-button {
        top: 0.8rem;
        left: 0.8rem;
    }
}

/* Small Screens */
@media (max-width: 480px) {
    .auth-container {
        margin: 3rem 0 0 0;
        padding: 1.5rem 1rem;
    }

    .auth-container h1 {
        font-size: 1.3rem;
        margin-bottom: 1.2rem;
    }

    .form-group label {
        font-size: 0.85rem;
    }

    .form-group input {
        padding: 0.7rem;
        font-size: 0.85rem;
    }

    .btn-primary {
        padding: 0.8rem;
    font-size: 0.9rem;
}

    .auth-options {
        gap: 0.8rem;
        margin-top: 1.2rem;
    }

    .auth-options a {
        font-size: 0.85rem;
    }

    .help-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .help-link {
        font-size: 0.8rem !important;
        padding: 4px 8px;
    }

    .back-button {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}

/* Password Strength Meter */
.password-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-top: 0.3rem;
}

.password-strength-meter {
    height: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.strength-bar {
    height: 100%;
    width: 0;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 3px;
}

.strength-bar.weak {
    width: 33%;
    background-color: #ff4d4d; /* Red */
}

.strength-bar.medium {
    width: 66%;
    background-color: #ffaa00; /* Orange */
}

.strength-bar.strong {
    width: 100%;
    background-color: #00cc44; /* Green */
}

.password-strength-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-top: 0.3rem;
    display: block;
}

/* Error Messages */
.error-message {
    background-color: rgba(255, 56, 96, 0.1);
    border-left: 3px solid #ff3860;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    animation: fadeIn 0.3s ease-in-out;
    display: flex;
    align-items: center;
    gap: 8px;
}

.error-message i {
    color: #ff3860;
    font-size: 16px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Error Modal */
#errorModal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    max-width: 400px;
    width: 90%;
    padding: 1.5rem;
}

.modal-header {
    margin-bottom: 1rem;
}

.modal-body {
    margin-bottom: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
}