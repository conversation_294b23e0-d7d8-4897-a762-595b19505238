{"version": "1.0.20", "releaseDate": "2023-05-15", "requiredUpdate": true, "forceUpdate": true, "forceUpdateTimestamp": 1684152000000, "message": "Fixed plan display issues and improved performance", "changes": ["Fixed issue with user plans not displaying correctly", "Improved plan detection and display in user interface", "Enhanced plan structure in database for better consistency", "Fixed plan rates for all subscription types", "Added detailed logging for easier troubleshooting", "Improved caching for better performance", "Updated Trial plan: Rs 10 for 100 videos (5 mins each)", "Updated Starter plan: Rs 25 for 100 videos (5 mins each)", "Updated Premium plan: Rs 150 for 100 videos (5 mins each)", "Added Elite plan: Rs 250 for 100 videos (5 mins each)", "Added Ultimate plan: Rs 500 for 100 videos (5 mins each)", "Changed all plans to 100 videos per day including Trial plan", "Changed video duration from 2 minutes to 5 minutes for all plans", "Updated referral bonuses: Starter Rs 50, Premium Rs 300, Elite Rs 500, Ultimate Rs 1000", "Added custom video duration feature for admin user editing (1 sec, 30 sec, 1 min, 2 mins)", "Added comprehensive time tracking system for work page", "Added time spent display in work page statistics", "Added detailed time tracking in transaction records (total time, video time, average per video)", "Added time tracking summary in submission success message", "Removed platform migration notice from login page", "Removed QR code functionality completely from the platform", "Removed qrcode.min.js library and all QR-related CSS", "Fixed duplicate startVideoTimer function declaration error", "Fixed syntax error with unexpected 'catch' token in work-video.js", "Fixed video loading issues by updating video-manager.js to handle JSON array format", "Added YouTube URL validation to ensure only valid video links are loaded", "Fixed admin user edit form to include 5-minute video duration option", "Updated admin settings default video duration from 2 minutes to 5 minutes", "Updated admin user edit form to show 'Earnings Per 100 Videos' instead of 50", "Fixed submit button enabling after 50 videos instead of 100", "Added video URL validation and error handling for unavailable videos", "Added iframe loading timeout and error recovery mechanisms", "Improved video loading reliability with automatic fallback to sample videos", "Enhanced video URL validation and fixing for mytube.json format issues", "Added filtering for problematic video IDs and automatic sample video injection", "Created conversion utility (convert-mytube-json.js) to fix mytube.json format", "Added reliable sample videos as fallback for unavailable content", "Updated profile page to show video stats instead of QR stats", "Updated admin panel to support new Elite and Ultimate plans", "Updated admin plan rates to reflect 100 videos for paid plans", "Updated admin plan creation and editing with correct video counts", "Enhanced error handling for plan-related operations", "Improved user experience with clearer plan information"]}