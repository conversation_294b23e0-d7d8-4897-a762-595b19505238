<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Plans</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <h1>Plans in Firebase</h1>
    <div id="plans-container"></div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
        import { getFirestore, collection, getDocs } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

        // Your Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyAMMabAMVSrNzKdTEVOcn4Ffh7oDVxD2EM",
            authDomain: "mytube-earnings.firebaseapp.com",
            projectId: "mytube-earnings",
            storageBucket: "mytube-earnings.firebasestorage.app",
            messagingSenderId: "116772605182",
            appId: "1:116772605182:web:a5e9875d48867cca03e9af",
            measurementId: "G-MR5HFN8J4V"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Get plans from Firestore
        async function getPlans() {
            const plansContainer = document.getElementById('plans-container');
            plansContainer.innerHTML = '<p>Loading plans...</p>';

            try {
                const plansCollection = collection(db, 'plans');
                const plansSnapshot = await getDocs(plansCollection);

                if (plansSnapshot.empty) {
                    plansContainer.innerHTML = '<p>No plans found in Firebase.</p>';
                    return;
                }

                let plansHTML = '<table border="1" style="border-collapse: collapse; width: 100%;">';
                plansHTML += '<tr><th>ID</th><th>Name</th><th>Price</th><th>Duration</th><th>Languages</th><th>Earnings per Batch</th><th>Active</th><th>Description</th></tr>';

                plansSnapshot.forEach((doc) => {
                    const plan = doc.data();
                    plansHTML += `
                        <tr>
                            <td>${doc.id}</td>
                            <td>${plan.name || 'N/A'}</td>
                            <td>₹${plan.price || 0}</td>
                            <td>${plan.duration || 0} days</td>
                            <td>${plan.languages || 0}</td>
                            <td>₹${plan.earningsPerBatch || 0}</td>
                            <td>${plan.active !== false ? 'Yes' : 'No'}</td>
                            <td>${plan.description || 'N/A'}</td>
                        </tr>
                    `;
                });

                plansHTML += '</table>';
                plansContainer.innerHTML = plansHTML;
            } catch (error) {
                console.error('Error getting plans:', error);
                plansContainer.innerHTML = `<p>Error getting plans: ${error.message}</p>`;
            }
        }

        // Call the function when the page loads
        document.addEventListener('DOMContentLoaded', getPlans);
    </script>
</body>
</html>
