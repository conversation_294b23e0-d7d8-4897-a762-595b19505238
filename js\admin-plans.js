// Admin Plans Module
import { auth, db, showLoading, hideLoading } from './admin-auth.js';
import {
    collection,
    query,
    getDocs,
    orderBy,
    limit,
    where,
    Timestamp,
    doc,
    getDoc,
    updateDoc,
    addDoc,
    deleteDoc,
    serverTimestamp,
    setDoc,
    startAfter
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import {
    formatDate,
    formatCurrency,
    showModal,
    hideModal,
    getUserDetails
} from './admin.js';

// DOM Elements
const plansTable = document.getElementById('plans-table');
const addPlanBtn = document.getElementById('add-plan-btn');
const planModal = document.getElementById('plan-modal');
const planModalTitle = document.getElementById('plan-modal-title');
const planNameInput = document.getElementById('plan-name');
const planPriceInput = document.getElementById('plan-price');
const planDefaultDurationInput = document.getElementById('plan-default-duration');
const planFeaturesInput = document.getElementById('plan-features');
const planStatusSelect = document.getElementById('plan-status');
const savePlanBtn = document.getElementById('save-plan-btn');

// Users with plans elements
const usersPlansTable = document.getElementById('users-plans-table');
const planFilterSelect = document.getElementById('plan-filter');
const userSearchInput = document.getElementById('user-search');
const searchBtn = document.getElementById('search-btn');
const prevPageBtn = document.getElementById('prev-page');
const nextPageBtn = document.getElementById('next-page');
const pageInfoElement = document.getElementById('page-info');

// Assign plan modal elements
const assignPlanModal = document.getElementById('assign-plan-modal');
const modalUserName = document.getElementById('modal-user-name');
const modalUserEmail = document.getElementById('modal-user-email');
const modalUserMobile = document.getElementById('modal-user-mobile');
const modalPlanSelect = document.getElementById('modal-plan-select');
const modalPlanDuration = document.getElementById('modal-plan-duration');
const currentPlanInfo = document.getElementById('current-plan-info');
const modalAssignPlanBtn = document.getElementById('modal-assign-plan-btn');

// State variables
let currentPlans = [];
let currentPlanId = null;
let currentUsers = [];
let selectedUserId = null;
let searchTimeout = null;
let lastVisibleUser = null;
let currentPage = 1;
let totalPages = 1;
let itemsPerPage = 10;
let currentFilters = {
    plan: 'all',
    search: ''
};

// Initialize plans page
async function initPlansPage() {
    showLoading('Loading plans data...');

    try {
        // Add event listeners
        setupEventListeners();

        // Load initial data
        await loadPlans();
        await loadPlansForSelect();
        await loadUsersWithPlans();

        hideLoading();
    } catch (error) {
        console.error('Error initializing plans page:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load plans data: ' + error.message,
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Add plan button
    if (addPlanBtn) {
        addPlanBtn.addEventListener('click', () => {
            resetPlanForm();
            planModalTitle.innerHTML = '<i class="fas fa-crown"></i> Add New Plan';
            showModal('plan-modal');
        });
    }

    // Save plan button
    if (savePlanBtn) {
        savePlanBtn.addEventListener('click', savePlan);
    }

    // Plan filter select
    if (planFilterSelect) {
        planFilterSelect.addEventListener('change', handleFilterChange);
    }

    // User search input
    if (userSearchInput) {
        userSearchInput.addEventListener('input', debounce(handleFilterChange, 500));
    }

    // Search button
    if (searchBtn) {
        searchBtn.addEventListener('click', handleFilterChange);
    }

    // Pagination buttons
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', goToPreviousPage);
    }

    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', goToNextPage);
    }

    // Modal assign plan button
    if (modalAssignPlanBtn) {
        modalAssignPlanBtn.addEventListener('click', assignPlanToUser);
    }
}

// Load plans
async function loadPlans() {
    try {
        // Get plans from Firestore
        // First try to get plans from the 'plans' collection
        let plansQuery = query(collection(db, 'plans'));
        let snapshot = await getDocs(plansQuery);

        // If no plans found, create some default plans
        if (snapshot.empty) {
            console.log('No plans found. Creating default plans...');

            // Create default plans
            const defaultPlans = [
                {
                    name: 'Trial',
                    price: 0, // Free
                    defaultDuration: 2, // 2-day trial period
                    features: [
                        '100 videos per day',
                        '₹0.1 per video (₹10 for 100 videos)',
                        '5 minutes per video',
                        '2 days of short video advantage',
                        'Basic support'
                    ],
                    status: 'active',
                    createdAt: serverTimestamp(),
                    createdBy: auth.currentUser.uid,
                    dailyLimit: 100,
                    rate: 0.1,
                    earningsPerBatch: 10
                },
                {
                    name: 'Starter',
                    price: 499,
                    defaultDuration: 30,
                    features: [
                        '100 videos per day',
                        '₹0.25 per video (₹25 for 100 videos)',
                        '5 minutes per video',
                        '7 days of short video advantage',
                        'Standard support'
                    ],
                    status: 'active',
                    createdAt: serverTimestamp(),
                    createdBy: auth.currentUser.uid,
                    dailyLimit: 100,
                    rate: 0.25,
                    earningsPerBatch: 25
                },
                {
                    name: 'Premium',
                    price: 2999,
                    defaultDuration: 30,
                    features: [
                        '100 videos per day',
                        '₹1.5 per video (₹150 for 100 videos)',
                        '5 minutes per video',
                        '7 days of short video advantage',
                        'Premium support'
                    ],
                    status: 'active',
                    createdAt: serverTimestamp(),
                    createdBy: auth.currentUser.uid,
                    dailyLimit: 100,
                    rate: 1.5,
                    earningsPerBatch: 150
                },
                {
                    name: 'Elite',
                    price: 4999,
                    defaultDuration: 30,
                    features: [
                        '100 videos per day',
                        '₹2.5 per video (₹250 for 100 videos)',
                        '5 minutes per video',
                        '7 days of short video advantage',
                        'Premium support'
                    ],
                    status: 'active',
                    createdAt: serverTimestamp(),
                    createdBy: auth.currentUser.uid,
                    dailyLimit: 100,
                    rate: 2.5,
                    earningsPerBatch: 250
                },
                {
                    name: 'Ultimate',
                    price: 9999,
                    defaultDuration: 30,
                    features: [
                        '100 videos per day',
                        '₹5 per video (₹500 for 100 videos)',
                        '5 minutes per video',
                        '7 days of short video advantage',
                        'Premium support'
                    ],
                    status: 'active',
                    createdAt: serverTimestamp(),
                    createdBy: auth.currentUser.uid,
                    dailyLimit: 100,
                    rate: 5,
                    earningsPerBatch: 500
                }
            ];

            // Add default plans to Firestore
            await Promise.all(defaultPlans.map(plan => addDoc(collection(db, 'plans'), plan)));

            // Get plans again
            plansQuery = query(collection(db, 'plans'));
            snapshot = await getDocs(plansQuery);
        }

        if (snapshot.empty) {
            plansTable.innerHTML = `
                <tr>
                    <td colspan="6" class="admin-empty-cell">No plans found</td>
                </tr>
            `;
            currentPlans = [];
            return;
        }

        // Process plans data
        currentPlans = [];
        let tableHTML = '';

        snapshot.forEach(doc => {
            const plan = { id: doc.id, ...doc.data() };
            currentPlans.push(plan);

            // Format status
            const statusClass = plan.status === 'active' ? 'success' : 'error';

            // Format features
            const features = plan.features ? plan.features.join(', ') : 'No features';

            // Create table row
            tableHTML += `
                <tr>
                    <td>${plan.name}</td>
                    <td>${formatCurrency(plan.price || 0)}</td>
                    <td>${plan.defaultDuration || 30} days</td>
                    <td>${features}</td>
                    <td><span class="admin-badge ${statusClass}">${plan.status || 'inactive'}</span></td>
                    <td>
                        <button class="admin-btn-small" onclick="editPlan('${plan.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="admin-btn-small admin-btn-danger" onclick="deletePlan('${plan.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        plansTable.innerHTML = tableHTML;

        // Add global functions for buttons
        window.editPlan = editPlan;
        window.deletePlan = deletePlan;

    } catch (error) {
        console.error('Error loading plans:', error);
        plansTable.innerHTML = `
            <tr>
                <td colspan="6" class="admin-empty-cell">Error loading plans</td>
            </tr>
        `;
    }
}

// Load plans for select dropdown
async function loadPlansForSelect() {
    try {
        // Load plans for filter dropdown
        if (planFilterSelect) {
            // Clear existing options except the first one
            while (planFilterSelect.options.length > 1) {
                planFilterSelect.remove(1);
            }

            // Add options from current plans
            currentPlans.forEach(plan => {
                const option = document.createElement('option');
                option.value = plan.id;
                option.textContent = plan.name;
                planFilterSelect.appendChild(option);
            });
        }

        // Load plans for modal dropdown
        if (modalPlanSelect) {
            // Clear existing options except the first one
            while (modalPlanSelect.options.length > 1) {
                modalPlanSelect.remove(1);
            }

            // Add options from current plans
            currentPlans.forEach(plan => {
                if (plan.status === 'active') {
                    const option = document.createElement('option');
                    option.value = plan.id;
                    option.textContent = `${plan.name} - ${formatCurrency(plan.price || 0)}`;
                    modalPlanSelect.appendChild(option);
                }
            });
        }
    } catch (error) {
        console.error('Error loading plans for select:', error);
    }
}

// Reset plan form
function resetPlanForm() {
    currentPlanId = null;
    planNameInput.value = '';
    planPriceInput.value = '';
    planDefaultDurationInput.value = '30';
    planFeaturesInput.value = '';
    planStatusSelect.value = 'active';
}

// Edit plan
async function editPlan(planId) {
    try {
        showLoading('Loading plan details...');

        // Get plan details
        const planDoc = await getDoc(doc(db, 'plans', planId));

        if (!planDoc.exists()) {
            hideLoading();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Plan not found',
            });
            return;
        }

        const plan = planDoc.data();
        currentPlanId = planId;

        // Populate form
        planNameInput.value = plan.name || '';
        planPriceInput.value = plan.price || '';
        planDefaultDurationInput.value = plan.defaultDuration || 30;
        planFeaturesInput.value = plan.features ? plan.features.join('\\n') : '';
        planStatusSelect.value = plan.status || 'active';

        // Store additional fields in data attributes for reference
        planNameInput.dataset.dailyLimit = plan.dailyLimit || 50;
        planNameInput.dataset.rate = plan.rate || 0.2;
        planNameInput.dataset.earningsPerBatch = plan.earningsPerBatch || 10;

        // Update modal title
        planModalTitle.innerHTML = '<i class="fas fa-crown"></i> Edit Plan';

        hideLoading();
        showModal('plan-modal');

    } catch (error) {
        console.error('Error editing plan:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load plan details',
        });
    }
}

// Save plan
async function savePlan() {
    try {
        // Validate form
        const name = planNameInput.value.trim();
        const price = parseFloat(planPriceInput.value);
        const defaultDuration = parseInt(planDefaultDurationInput.value);
        const featuresText = planFeaturesInput.value.trim();
        const status = planStatusSelect.value;

        if (!name) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Plan name is required',
            });
            return;
        }

        if (isNaN(price) || price < 0) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please enter a valid price',
            });
            return;
        }

        if (isNaN(defaultDuration) || defaultDuration < 1) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please enter a valid duration',
            });
            return;
        }

        // Parse features
        const features = featuresText ? featuresText.split('\\n').map(f => f.trim()).filter(f => f) : [];

        showLoading('Saving plan...');

        // Calculate rate and earnings per batch based on plan name
        let rate = 0.1; // Default rate for Trial plan (₹0.1 per video)
        let earningsPerBatch = 10; // Default earnings for Trial plan
        let dailyLimit = 100; // All plans now have 100 videos

        if (name === 'Starter') {
            rate = 0.25;
            earningsPerBatch = 25;
            dailyLimit = 100;
        } else if (name === 'Premium') {
            rate = 1.5;
            earningsPerBatch = 150;
            dailyLimit = 100;
        } else if (name === 'Elite') {
            rate = 2.5;
            earningsPerBatch = 250;
            dailyLimit = 100;
        } else if (name === 'Ultimate') {
            rate = 5;
            earningsPerBatch = 500;
            dailyLimit = 100;
        }

        // Prepare plan data
        const planData = {
            name,
            price,
            defaultDuration,
            features,
            status,
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid,
            dailyLimit,
            rate,
            earningsPerBatch
        };

        if (currentPlanId) {
            // Update existing plan
            await updateDoc(doc(db, 'plans', currentPlanId), planData);
        } else {
            // Add new plan
            planData.createdAt = serverTimestamp();
            planData.createdBy = auth.currentUser.uid;
            await addDoc(collection(db, 'plans'), planData);
        }

        hideLoading();
        hideModal('plan-modal');

        // Refresh data
        await loadPlans();
        await loadPlansForSelect();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: `Plan has been ${currentPlanId ? 'updated' : 'created'}`,
        });

    } catch (error) {
        console.error('Error saving plan:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to save plan',
        });
    }
}

// Delete plan
async function deletePlan(planId) {
    try {
        const result = await Swal.fire({
            title: 'Delete Plan',
            text: 'Are you sure you want to delete this plan?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#d33'
        });

        if (result.isConfirmed) {
            showLoading('Deleting plan...');

            // Delete plan document
            await deleteDoc(doc(db, 'plans', planId));

            hideLoading();

            // Refresh data
            await loadPlans();
            await loadPlansForSelect();

            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Plan has been deleted',
            });
        }
    } catch (error) {
        console.error('Error deleting plan:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to delete plan',
        });
    }
}

// Search users
async function searchUsers() {
    try {
        const searchTerm = userSearchInput.value.trim();

        if (!searchTerm || searchTerm.length < 2) {
            userSearchResults.style.display = 'none';
            return;
        }

        showLoading('Searching users...');

        // Search users by name, email, or mobile
        const usersRef = collection(db, 'users');
        const snapshot = await getDocs(usersRef);

        if (snapshot.empty) {
            userResultsList.innerHTML = '<div class="admin-search-result-item">No users found</div>';
            userSearchResults.style.display = 'block';
            hideLoading();
            return;
        }

        // Filter users by search term
        const searchResults = [];

        snapshot.forEach(doc => {
            const user = { id: doc.id, ...doc.data() };
            const userName = user.fullName || user.name || user.displayName || '';
            const userEmail = user.email || '';
            const userMobile = user.mobile || user.phone || user.phoneNumber || '';

            // Check if search term matches any field - case insensitive for text fields
            if (
                userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                userMobile.includes(searchTerm)
            ) {
                searchResults.push(user);
            }
        });

        // Display search results
        if (searchResults.length === 0) {
            userResultsList.innerHTML = '<div class="admin-search-result-item">No users found</div>';
        } else {
            let resultsHTML = '';

            searchResults.forEach(user => {
                resultsHTML += `
                    <div class="admin-search-result-item" onclick="selectUser('${user.id}', '${user.fullName || user.name || user.displayName || 'User'}')">
                        <div class="admin-search-result-name">${user.fullName || user.name || user.displayName || 'User'}</div>
                        <div class="admin-search-result-details">
                            <span>${user.email || 'N/A'}</span>
                            <span>${user.mobile || user.phone || user.phoneNumber || 'N/A'}</span>
                        </div>
                    </div>
                `;
            });

            userResultsList.innerHTML = resultsHTML;
        }

        userSearchResults.style.display = 'block';
        hideLoading();

        // Add global function for selecting user
        window.selectUser = selectUser;

    } catch (error) {
        console.error('Error searching users:', error);
        hideLoading();

        userResultsList.innerHTML = '<div class="admin-search-result-item">Error searching users</div>';
        userSearchResults.style.display = 'block';
    }
}

// Select user
function selectUser(userId, userName) {
    selectedUserId = userId;
    userSelectedDiv.innerHTML = `
        <div class="admin-selected-user-info">
            <span>${userName}</span>
            <button class="admin-btn-small" onclick="clearSelectedUser()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    userSearchResults.style.display = 'none';
    assignPlanBtn.disabled = !selectedUserId || !planSelect.value;

    // Add global function for clearing selected user
    window.clearSelectedUser = clearSelectedUser;
}

// Clear selected user
function clearSelectedUser() {
    selectedUserId = null;
    userSelectedDiv.innerHTML = '<span>No user selected</span>';
    assignPlanBtn.disabled = true;
}

// Load users with plans
async function loadUsersWithPlans(isNextPage = false) {
    try {
        if (!usersPlansTable) return;

        if (!isNextPage) {
            lastVisibleUser = null;
            currentPage = 1;
        }

        // Build query based on filters
        let usersQuery = buildUsersQuery(isNextPage);

        // Execute query
        const snapshot = await getDocs(usersQuery);

        // Update pagination state
        if (!isNextPage) {
            // Get total count for pagination
            const countQuery = query(collection(db, 'users'));
            const countSnapshot = await getDocs(countQuery);
            totalPages = Math.ceil(countSnapshot.size / itemsPerPage);
        } else {
            currentPage++;
        }

        // Update last visible for pagination
        if (!snapshot.empty) {
            lastVisibleUser = snapshot.docs[snapshot.docs.length - 1];
        } else {
            lastVisibleUser = null;
        }

        // Update UI
        updatePaginationUI();
        await renderUsersTable(snapshot);

    } catch (error) {
        console.error('Error loading users with plans:', error);
        usersPlansTable.innerHTML = `
            <tr>
                <td colspan="8" class="admin-empty-cell">Error loading users: ${error.message}</td>
            </tr>
        `;
    }
}

// Build users query based on filters
function buildUsersQuery(isNextPage = false) {
    let baseQuery = collection(db, 'users');
    let conditions = [];

    // Apply plan filter
    if (currentFilters.plan !== 'all') {
        conditions.push(where('plan.id', '==', currentFilters.plan));
    }

    // Apply search filter
    if (currentFilters.search && currentFilters.search.trim() !== '') {
        // Note: This is a simplified approach - in a real app, you'd want to use a proper search solution
        // Firestore doesn't support direct text search across multiple fields
        // Here we're just checking if the search term is in the name
        const searchTerm = currentFilters.search.trim().toLowerCase();

        // This is a workaround - in a real app, you'd use Algolia or similar
        // We'll do the filtering client-side after fetching the data
    }

    // Create query with conditions
    let usersQuery;

    if (conditions.length > 0) {
        usersQuery = query(baseQuery, ...conditions);
    } else {
        usersQuery = query(baseQuery);
    }

    // Add pagination
    if (isNextPage && lastVisibleUser) {
        usersQuery = query(usersQuery, startAfter(lastVisibleUser), limit(itemsPerPage));
    } else {
        usersQuery = query(usersQuery, limit(itemsPerPage));
    }

    return usersQuery;
}

// Render users table
async function renderUsersTable(snapshot) {
    if (!usersPlansTable) return;

    if (snapshot.empty) {
        usersPlansTable.innerHTML = `
            <tr>
                <td colspan="8" class="admin-empty-cell">No users found</td>
            </tr>
        `;
        currentUsers = [];
        return;
    }

    // Process users data
    currentUsers = [];
    let tableHTML = '';
    const planCache = {}; // Cache plan details to avoid duplicate fetches

    // First, cache all plan details
    for (const plan of currentPlans) {
        planCache[plan.id] = plan;
    }

    for (const doc of snapshot.docs) {
        const user = { id: doc.id, ...doc.data() };
        currentUsers.push(user);

        // Apply search filter if needed
        if (currentFilters.search && currentFilters.search.trim() !== '') {
            const searchTerm = currentFilters.search.trim().toLowerCase();
            const userName = (user.fullName || user.name || '').toLowerCase();
            const userEmail = (user.email || '').toLowerCase();
            const userMobile = (user.mobile || user.phone || user.phoneNumber || '');

            if (!userName.includes(searchTerm) && !userEmail.includes(searchTerm) && !userMobile.includes(searchTerm)) {
                continue; // Skip this user if it doesn't match the search
            }
        }

        // Get plan details
        let planName = 'No Plan';
        let planStatus = 'inactive';
        let planActivatedDate = 'N/A';
        let planExpiryDate = 'N/A';

        if (user.plan && user.plan.id) {
            planName = user.plan.name || 'Unknown Plan';

            // Check if plan is active based on expiry date
            if (user.planExpiresAt) {
                const expiryDate = user.planExpiresAt.toDate();
                const now = new Date();
                planStatus = expiryDate > now ? 'active' : 'expired';
                planExpiryDate = formatDate(user.planExpiresAt);
            }

            if (user.planActivatedAt) {
                planActivatedDate = formatDate(user.planActivatedAt);
            }
        }

        // Format status badge
        const statusClass = planStatus === 'active' ? 'success' : (planStatus === 'expired' ? 'warning' : 'error');

        // Create table row
        tableHTML += `
            <tr>
                <td>${user.fullName || user.name || 'User'}</td>
                <td>${user.email || 'N/A'}</td>
                <td>${user.mobile || user.phone || 'N/A'}</td>
                <td>${planName}</td>
                <td>${planActivatedDate}</td>
                <td>${planExpiryDate}</td>
                <td><span class="admin-badge ${statusClass}">${planStatus}</span></td>
                <td>
                    <button class="admin-btn-small" onclick="openAssignPlanModal('${user.id}')">
                        <i class="fas fa-crown"></i>
                    </button>
                </td>
            </tr>
        `;
    }

    usersPlansTable.innerHTML = tableHTML;

    // Add global function for assign plan button
    window.openAssignPlanModal = openAssignPlanModal;
}

// Open assign plan modal
async function openAssignPlanModal(userId) {
    try {
        showLoading('Loading user details...');
        selectedUserId = userId;

        // Get user details
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (!userDoc.exists()) {
            hideLoading();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'User not found',
            });
            return;
        }

        const user = userDoc.data();

        // Update modal content
        modalUserName.textContent = user.fullName || user.name || 'User';
        modalUserEmail.textContent = user.email || 'N/A';
        modalUserMobile.textContent = user.mobile || user.phone || 'N/A';

        // Set first letter of name as avatar
        const userName = user.fullName || user.name || 'User';
        document.getElementById('user-avatar').textContent = userName.charAt(0).toUpperCase();

        // Show current plan info
        if (user.plan && user.plan.id) {
            let planInfo = `${user.plan.name || 'Unknown Plan'}`;

            if (user.planExpiresAt) {
                const expiryDate = user.planExpiresAt.toDate();
                const now = new Date();
                const status = expiryDate > now ? 'Active' : 'Expired';
                planInfo += ` (${status}, Expires: ${formatDate(user.planExpiresAt)})`;
            }

            currentPlanInfo.textContent = planInfo;
        } else {
            currentPlanInfo.textContent = 'No active plan';
        }

        // Reset plan selection
        modalPlanSelect.value = '';
        modalPlanDuration.value = '30';

        hideLoading();
        showModal('assign-plan-modal');
    } catch (error) {
        console.error('Error opening assign plan modal:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load user details',
        });
    }
}

// Handle filter change
function handleFilterChange() {
    // Update current filters
    currentFilters.plan = planFilterSelect ? planFilterSelect.value : 'all';
    currentFilters.search = userSearchInput ? userSearchInput.value.trim() : '';

    // Reset pagination and load users
    loadUsersWithPlans(false);
}

// Go to previous page
function goToPreviousPage() {
    if (currentPage > 1) {
        // This is a simplified approach - for a real implementation,
        // you would need to store the first visible item of the previous page
        // Here we just reload from the beginning and skip to the right page
        currentPage--;
        loadUsersWithPlans(false);
    }
}

// Go to next page
function goToNextPage() {
    if (lastVisibleUser && currentPage < totalPages) {
        loadUsersWithPlans(true);
    }
}

// Update pagination UI
function updatePaginationUI() {
    if (pageInfoElement) {
        pageInfoElement.textContent = `Page ${currentPage} of ${totalPages || 1}`;
    }

    if (prevPageBtn) {
        prevPageBtn.disabled = currentPage <= 1;
    }

    if (nextPageBtn) {
        nextPageBtn.disabled = currentPage >= totalPages || !lastVisibleUser;
    }
}

// Debounce function for search input
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', initPlansPage);
