// Work Page JavaScript for Video Watching
// This file contains the functionality for the video watching feature

import { initializeApp, getApps, getApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import { getFirestore, doc, getDoc, updateDoc, serverTimestamp, collection, addDoc, increment, query, where, orderBy, limit, getDocs } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import { checkPlanExpiration } from "./plan-expiration.js";
import { checkDailyVideoLimit, markVideosAsSubmitted, updateWalletWithEarnings } from "./video-limits.js";
import { initializeVideoPlayer, checkVideoAdvantageStatus, sampleVideos } from "./video-player.js";
import { VIDEO_DURATIONS } from "./short-video-advantage.js";
import videoManager from "./video-manager.js";
import { isLeaveDay, getLeaveReason, getNextWorkingDay, formatDateForDisplay, clearLeaveCache } from "./leave-system.js";
import { checkShortVideoAdvantage, getUserVideoDuration } from "./short-video-advantage.js";
import {
    loadUserDataWithCache,
    getWalletDataWithCache
} from "./work-cache.js";
import { plans } from "./plan-manager.js";
import { addTransaction } from "./data-service.js";

// Initialize Firebase
const firebaseConfig = {
    apiKey: "AIzaSyAMMabAMVSrNzKdTEVOcn4Ffh7oDVxD2EM",
    authDomain: "mytube-earnings.firebaseapp.com",
    projectId: "mytube-earnings",
    storageBucket: "mytube-earnings.firebasestorage.app",
    messagingSenderId: "116772605182",
    appId: "1:116772605182:web:a5e9875d48867cca03e9af",
    measurementId: "G-MR5HFN8J4V"
};

const app = getApps().length ? getApp() : initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Note: We already import recoverFailedWalletUpdates from video-limits.js on line 8
// Import plan check function
import { checkAndFixPlanData } from "./plan-manager.js";

// Global variables
let videoPlayer = null;
let todayVideoCount = 0;
let userHasShortVideoAdvantage = false;
let videoDuration = 300; // Default to 5 minutes (300 seconds)
let currentVideoIndex = 0;

// Time tracking variables
let workSessionStartTime = null;
let totalTimeSpent = 0; // in seconds
let videoWatchingTime = 0; // actual video watching time in seconds
let sessionTimeInterval = null;
let currentVideoStartTime = null;

// DOM Elements
const videoElement = document.getElementById("videoPlayer");
const startVideoBtn = document.getElementById("startVideoBtn");
const nextVideoBtn = document.getElementById("nextVideoBtn");
const submitButton = document.getElementById("submitBtn");
const todayVideosElement = document.querySelector('.stat-item:nth-child(1) .stat-value');
const totalVideosElement = document.querySelector('.stat-item:nth-child(2) .stat-value');
const videosLeftElement = document.getElementById("videosLeft");
const referralInfoElement = document.getElementById("referralInfo");
const counterElement = document.getElementById("counter");

// Videos storage
let cachedVideos = [];

// Loading overlay elements
const loadingOverlay = document.getElementById('loadingOverlay');

// Time tracking functions
function startWorkSession() {
    if (!workSessionStartTime) {
        workSessionStartTime = new Date();
        console.log('Work session started at:', workSessionStartTime.toLocaleTimeString());

        // Start session time tracking interval
        sessionTimeInterval = setInterval(() => {
            if (workSessionStartTime) {
                totalTimeSpent = Math.floor((new Date() - workSessionStartTime) / 1000);
                updateTimeDisplay();
            }
        }, 1000);
    }
}

function startVideoTimeTracking() {
    currentVideoStartTime = new Date();
    console.log('Video timer started at:', currentVideoStartTime.toLocaleTimeString());
}

function stopVideoTimer() {
    if (currentVideoStartTime) {
        const videoEndTime = new Date();
        const videoTime = Math.floor((videoEndTime - currentVideoStartTime) / 1000);
        videoWatchingTime += videoTime;
        console.log(`Video completed in ${videoTime} seconds. Total video time: ${videoWatchingTime} seconds`);
        currentVideoStartTime = null;
    }
}

function formatTimeSpent(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}

function updateTimeDisplay() {
    // Add time display to the stats section
    const timeDisplayElement = document.getElementById('timeSpentValue');
    if (timeDisplayElement) {
        timeDisplayElement.textContent = formatTimeSpent(totalTimeSpent);
    }
}

function updateTargetCount(planName) {
    const targetCountElement = document.getElementById('targetCount');
    if (targetCountElement) {
        const targetCount = 100; // All plans now have 100 videos
        targetCountElement.textContent = `/${targetCount}`;
        console.log(`Updated target count to ${targetCount} for plan: ${planName}`);
    }
}

function getTimeSpentSummary() {
    return {
        totalTimeSpent: totalTimeSpent,
        videoWatchingTime: videoWatchingTime,
        totalTimeFormatted: formatTimeSpent(totalTimeSpent),
        videoTimeFormatted: formatTimeSpent(videoWatchingTime),
        sessionStartTime: workSessionStartTime,
        averageTimePerVideo: todayVideoCount > 0 ? Math.floor(videoWatchingTime / todayVideoCount) : 0
    };
}

// Function to show the loading overlay - used by initializeWork
function showLoading() {
    console.log('Showing loading overlay...');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
}

// Function to hide the loading overlay and show ready message
function hideLoading() {
    console.log('Hiding loading overlay...');
    if (loadingOverlay) {
        // Force the overlay to be hidden
        loadingOverlay.style.display = 'none';
        loadingOverlay.style.visibility = 'hidden';
        loadingOverlay.style.opacity = '0';

        console.log('Loading overlay hidden');

        // Create and show a ready message
        const readyMessage = document.createElement('div');
        readyMessage.className = 'ready-message';
        readyMessage.innerHTML = '<i class="fas fa-check-circle"></i> Page is ready for video watching';
        document.body.appendChild(readyMessage);
        console.log('Ready message displayed');

        // Remove the message after animation completes
        setTimeout(() => {
            if (readyMessage && readyMessage.parentNode) {
                readyMessage.parentNode.removeChild(readyMessage);
                console.log('Ready message removed');
            }
        }, 6000); // 3s display + 3s fade out
    } else {
        console.warn('Loading overlay element not found');
    }
}

/**
 * Disable the work interface during leave days
 * @param {string} message - Message to display
 */
function disableWorkInterface(message) {
    console.log('Disabling work interface for leave day');

    // Create a leave day banner
    const container = document.querySelector('.container');
    if (container) {
        const leaveBanner = document.createElement('div');
        leaveBanner.className = 'leave-banner';
        leaveBanner.innerHTML = `
            <div class="leave-banner-content">
                <div class="leave-banner-icon">
                    <i class="fas fa-calendar-times"></i>
                </div>
                <div class="leave-banner-text">
                    <div class="leave-banner-title">Platform on Leave Today</div>
                    <div class="leave-banner-message" style="color: #000000 !important;">${message}</div>
                </div>
            </div>
        `;

        // Insert at the top of the container
        container.insertBefore(leaveBanner, container.firstChild);
    }

    // Disable all interactive elements
    const interactiveElements = [
        videoElement,
        nextVideoBtn,
        submitButton
    ];

    interactiveElements.forEach(element => {
        if (element) {
            element.disabled = true;
        }
    });

    // Hide the loading overlay
    hideLoading();
}

// Function to check if user is online
function isUserOnline() {
    return navigator.onLine;
}

/**
 * Check if the current user is on leave today
 * @param {string} userId - The user's ID
 * @returns {Promise<Object>} Object with onLeave boolean and reason string
 */
async function checkUserLeaveStatus(userId) {
    try {
        // Get today's date in YYYY-MM-DD format
        const today = new Date();
        const formattedToday = today.toISOString().split('T')[0];

        console.log('Checking user leave status for date:', formattedToday);

        // Query userLeaves collection for approved leave on today's date
        const leavesQuery = query(
            collection(db, 'userLeaves'),
            where('userId', '==', userId),
            where('leaveDate', '==', formattedToday),
            where('status', '==', 'approved')
        );

        const leavesSnapshot = await getDocs(leavesQuery);

        if (!leavesSnapshot.empty) {
            // User has approved leave for today
            const leaveDoc = leavesSnapshot.docs[0];
            const leaveData = leaveDoc.data();

            console.log('User is on leave today:', leaveData);

            return {
                onLeave: true,
                reason: leaveData.reason || 'Personal Leave',
                leaveId: leaveDoc.id,
                appliedAt: leaveData.appliedAt
            };
        } else {
            console.log('User is not on leave today');
            return {
                onLeave: false,
                reason: null
            };
        }
    } catch (error) {
        console.error('Error checking user leave status:', error);
        // Return false on error to avoid blocking work unnecessarily
        return {
            onLeave: false,
            reason: null,
            error: error.message
        };
    }
}

// Function to check authentication and referrer
async function checkAuthAndReferrer() {
    try {
        // Wait for Firebase auth to initialize
        console.log('Waiting for Firebase auth to initialize...');

        // Create a promise that resolves when auth state changes
        const waitForAuth = new Promise((resolve) => {
            const unsubscribe = auth.onAuthStateChanged((user) => {
                unsubscribe(); // Unsubscribe once we get the auth state
                resolve(user);
            });

            // Set a timeout in case auth takes too long
            setTimeout(() => {
                unsubscribe();
                resolve(null);
            }, 5000); // 5 second timeout
        });

        // Wait for auth to initialize
        const user = await waitForAuth;

        // Check if user is authenticated
        if (!user) {
            console.log('User not authenticated, redirecting to login page');

            // Show a message before redirecting
            Swal.fire({
                icon: 'warning',
                title: 'Authentication Required',
                text: 'Please log in to access the video watching page.',
                confirmButtonText: 'Go to Login',
                timer: 3000,
                timerProgressBar: true
            }).then(() => {
                window.location.href = 'login.html';
            });

            return false;
        }

        // Skip referrer check - allow direct access to work page
        console.log('Authentication check passed, allowing access to work page');
        return true;
    } catch (error) {
        console.error('Error checking authentication:', error);

        // Show error message
        Swal.fire({
            icon: 'error',
            title: 'Authentication Error',
            text: 'There was an error checking your authentication status. Please try again.',
            confirmButtonText: 'Go to Login',
            showCancelButton: true,
            cancelButtonText: 'Try Again'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = 'login.html';
            } else {
                // Reload the page to try again
                window.location.reload();
            }
        });

        return false;
    }
}

// Function to update the plan information in the UI
function updatePlanInfo(planInfo) {
    console.log('Updating plan info in UI with data:', planInfo);

    // Add more detailed logging to help diagnose plan issues
    console.log('Plan details from checkPlanExpiration:', {
        planType: planInfo.planType,
        expired: planInfo.expired,
        daysLeft: planInfo.daysLeft,
        activeDays: planInfo.activeDays,
        validDays: planInfo.validDays,
        planActivationDate: planInfo.planActivationDate
    });

    // Update plan name
    const currentPlanElement = document.getElementById('currentPlan');
    if (currentPlanElement) {
        // Make sure we're using the correct plan type from Firebase
        const planType = planInfo.planType || 'Trial';

        // Force update the element with the correct plan type
        currentPlanElement.textContent = planType;

        // Add detailed logging
        console.log('PLAN DISPLAY UPDATE: Setting plan display element to:', planType);
        console.log('PLAN ELEMENT DEBUG:', {
            element: currentPlanElement,
            previousValue: currentPlanElement.textContent,
            newValue: planType,
            planInfoObject: planInfo
        });

        // Force a DOM update by temporarily detaching and reattaching the element
        const parent = currentPlanElement.parentNode;
        if (parent) {
            const clone = currentPlanElement.cloneNode(true);
            clone.textContent = planType;
            parent.replaceChild(clone, currentPlanElement);
            console.log('PLAN ELEMENT: Forced DOM update by replacing element');
        }
    } else {
        console.error('Plan name element (currentPlan) not found in DOM');

        // Try to find the element by other means
        const planBadge = document.querySelector('.plan-badge');
        if (planBadge) {
            const spanElements = planBadge.querySelectorAll('span');
            spanElements.forEach(span => {
                if (span.classList.contains('active-plan')) {
                    span.textContent = planInfo.planType || 'Trial';
                    console.log('PLAN ELEMENT: Found and updated plan element using alternative selector');
                }
            });
        }
    }

    // Update days left
    const daysLeftElement = document.getElementById('daysLeft');
    if (daysLeftElement) {
        daysLeftElement.textContent = `${planInfo.daysLeft} days left`;
        console.log('Updated days left to:', planInfo.daysLeft);
    } else {
        console.error('Days left element (daysLeft) not found in DOM');
    }

    // Update active days
    const activeDaysElement = document.getElementById('activeDaysValue');
    if (activeDaysElement) {
        // Use consistent format with dashboard: activeDays/validDays
        const validDays = planInfo.validDays || (planInfo.planType === 'Trial' ? 2 : 30);
        activeDaysElement.textContent = `${planInfo.activeDays}/${validDays}`;
        activeDaysElement.title = `Day ${planInfo.activeDays} of your ${validDays}-day ${planInfo.planType} plan`;
        console.log('Updated active days to:', `${planInfo.activeDays}/${validDays}`);
    } else {
        console.error('Active days element (activeDaysValue) not found in DOM');

        // Try to find the active days element using the stat-item structure
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach((item) => {
            const label = item.querySelector('.stat-label');
            if (label && label.textContent === 'Active Days') {
                const value = item.querySelector('.stat-value');
                if (value) {
                    // Use consistent format with dashboard: activeDays/validDays
                    const validDays = planInfo.validDays || (planInfo.planType === 'Trial' ? 2 : 30);
                    value.textContent = `${planInfo.activeDays}/${validDays}`;
                    value.title = `Day ${planInfo.activeDays} of your ${validDays}-day ${planInfo.planType} plan`;
                    console.log('Updated active days using stat item label match:', `${planInfo.activeDays}/${validDays}`);
                }
            }
        });
    }

    // Update plan activation date if available
    const planActivationElement = document.getElementById('planActivationDate');
    if (planActivationElement && planInfo.planActivationDate) {
        planActivationElement.textContent = `Activated: ${planInfo.planActivationDate}`;
        planActivationElement.style.display = 'block';
        console.log('Updated plan activation date:', planInfo.planActivationDate);
    } else if (planActivationElement) {
        planActivationElement.style.display = 'none';
    }

    // Update debug info if element exists
    const debugInfoElement = document.getElementById('planDebugInfo');
    if (debugInfoElement) {
        debugInfoElement.innerHTML = `
            <strong>Plan Debug Info:</strong><br>
            Plan Type: ${planInfo.planType}<br>
            Active Days: ${planInfo.activeDays}<br>
            Valid Days: ${planInfo.validDays || (planInfo.planType === 'Trial' ? 2 : 30)}<br>
            Days Left: ${planInfo.daysLeft}<br>
            Activation Date: ${planInfo.planActivationDate || 'Unknown'}<br>
            Expired: ${planInfo.expired ? 'Yes' : 'No'}
        `;

        // Show debug info in development mode or if URL has debug parameter
        const isDevMode = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.search.includes('debug=true');

        debugInfoElement.style.display = isDevMode ? 'block' : 'none';
        console.log('Debug info updated');
    }

    // Update target count based on plan
    updateTargetCount(planInfo.planType || 'Trial');
}

// Function to check plan status
async function checkPlanStatus() {
    try {
        // Direct check for Sunday
        const today = new Date();
        const isSunday = today.getDay() === 0;

        if (isSunday) {
            console.log('Today is Sunday - direct check in checkPlanStatus');
            // Get tomorrow's date for the message
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const formattedNextDay = formatDateForDisplay(tomorrow);

            // Disable the work interface
            disableWorkInterface(`Sunday Leave. Work will resume on ${formattedNextDay}.`);

            // Still get plan status for display purposes
            try {
                const planStatus = await checkPlanExpiration();
                console.log('Got plan status on Sunday for display purposes:', planStatus);

                // Update the UI with plan information
                updatePlanInfo(planStatus);
            } catch (planError) {
                console.error('Error getting plan status on Sunday:', planError);
            }

            return false;
        }

        // Check if today is a system-wide leave day
        console.log('DEBUG: Checking if today is a system-wide leave day...');

        // Clear leave cache to ensure we get fresh data
        console.log('DEBUG: Clearing leave cache to get fresh data...');
        clearLeaveCache();

        const isLeaveToday = await isLeaveDay();
        console.log('DEBUG: isLeaveDay() result:', isLeaveToday);

        if (isLeaveToday) {
            console.log('Today is a system-wide leave day');
            const leaveReason = await getLeaveReason();
            console.log('DEBUG: Leave reason:', leaveReason);
            const nextWorkingDay = await getNextWorkingDay();
            const formattedNextDay = formatDateForDisplay(nextWorkingDay);

            // Disable the work interface
            disableWorkInterface(`${leaveReason}. Work will resume on ${formattedNextDay}.`);
            return false;
        } else {
            console.log('DEBUG: Today is NOT a system-wide leave day');
        }

        // Check if the current user is on individual leave today
        const user = auth.currentUser;
        if (user) {
            const isUserOnLeaveToday = await checkUserLeaveStatus(user.uid);
            if (isUserOnLeaveToday.onLeave) {
                console.log('Current user is on leave today');
                const nextWorkingDay = await getNextWorkingDay();
                const formattedNextDay = formatDateForDisplay(nextWorkingDay);

                // Disable the work interface
                disableWorkInterface(`Personal Leave: ${isUserOnLeaveToday.reason}. Work will resume on ${formattedNextDay}.`);
                return false;
            }
        }

        // Check plan expiration
        const planStatus = await checkPlanExpiration();
        console.log('Plan status from checkPlanExpiration:', planStatus);

        // Add more detailed debugging
        console.log('PLAN CHECK: Retrieved plan information:', {
            planType: planStatus.planType,
            expired: planStatus.expired,
            daysLeft: planStatus.daysLeft,
            activeDays: planStatus.activeDays,
            validDays: planStatus.validDays,
            message: planStatus.message
        });

        // Log the raw plan data for debugging
        console.log('PLAN DEBUG: Raw plan data:', JSON.stringify(planStatus));

        // Force the plan type to be the one from Firebase (not hardcoded)
        if (planStatus.planType) {
            console.log('PLAN OVERRIDE: Setting plan type to:', planStatus.planType);
            // Make sure we're not accidentally using a hardcoded value
            const actualPlanType = planStatus.planType;
            planStatus.planType = actualPlanType;
        }

        // Update the UI with plan information
        updatePlanInfo(planStatus);

        if (planStatus.expired) {
            console.log('Plan has expired');
            // Show plan expired message
            Swal.fire({
                icon: 'warning',
                title: 'Plan Expired',
                html: `
                    <p>Your plan has expired. Please upgrade to continue.</p>
                    <p>Last active: ${planStatus.lastActiveDate}</p>
                `,
                confirmButtonText: 'Upgrade Now',
                showCancelButton: true,
                cancelButtonText: 'Later'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'plans.html';
                } else {
                    window.location.href = 'dashboard.html';
                }
            });
            return false;
        }

        return true;
    } catch (error) {
        console.error('Error checking plan status:', error);

        // Even if there's an error, still check for Sunday as a failsafe
        const today = new Date();
        const isSunday = today.getDay() === 0;

        if (isSunday) {
            console.log('Today is Sunday (failsafe check after error)');
            // Get tomorrow's date for the message
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const formattedNextDay = formatDateForDisplay(tomorrow);

            // Disable the work interface
            disableWorkInterface(`Sunday Leave. Work will resume on ${formattedNextDay}.`);
            return false;
        }

        return true; // Allow work to continue if there's an error checking plan status
    }
}

// Function to format currency
function formatCurrency(value) {
    return `₹${parseFloat(value || 0).toFixed(2)}`;
}

// Function to update short video advantage status in the UI
function updateShortVideoStatus(advantageResult) {
    console.log('Updating short video advantage status in UI:', advantageResult);

    try {
        // Find the status message elements
        const shortVideoEnabledMsg = document.getElementById('shortVideoEnabledMsg');
        const shortVideoDisabledMsg = document.getElementById('shortVideoDisabledMsg');

        if (advantageResult.hasAdvantage) {
            // User has short video advantage
            if (shortVideoEnabledMsg) {
                // Format the message based on the reason
                let statusText = '';

                if (advantageResult.expiryDate) {
                    const expiryDate = new Date(advantageResult.expiryDate);
                    const currentDate = new Date();
                    const daysRemaining = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));
                    statusText = ` (${daysRemaining} days left)`;
                }

                // Add source information if available
                const sourceText = advantageResult.source === 'admin' ? ' (admin granted)' : '';

                // Update the message text and show it
                shortVideoEnabledMsg.innerHTML = `<i class="fas fa-check-circle"></i> Short videos enabled${statusText}${sourceText} (${advantageResult.videoDuration} seconds)`;
                shortVideoEnabledMsg.style.display = 'inline-flex';

                // Hide the disabled message
                if (shortVideoDisabledMsg) {
                    shortVideoDisabledMsg.style.display = 'none';
                }
            }
        } else {
            // User does not have short video advantage
            if (shortVideoDisabledMsg) {
                shortVideoDisabledMsg.style.display = 'inline-flex';

                // Add click event to show more information
                shortVideoDisabledMsg.addEventListener('click', () => {
                    let infoMessage = '';

                    if (advantageResult.reason === 'admin_disabled') {
                        infoMessage = `
                            <p>Short video advantage has been disabled by an administrator.</p>
                            <p>Please contact support for assistance:</p>
                            <p><a href="https://wa.me/+917483681936" target="_blank" class="support-link"><i class="fab fa-whatsapp"></i> WhatsApp Support</a></p>
                        `;
                    } else if (advantageResult.reason === 'expired') {
                        infoMessage = `
                            <p>Your short video advantage has expired.</p>
                            <p>To get short videos (30 seconds instead of 2 minutes):</p>
                            <p>1. Complete ${advantageResult.requiredReferrals || 3} successful referrals</p>
                            <p>2. Upgrade your plan</p>
                            <p>Or contact support for assistance:</p>
                            <p><a href="https://wa.me/+917483681936" target="_blank" class="support-link"><i class="fab fa-whatsapp"></i> WhatsApp Support</a></p>
                        `;
                    } else {
                        infoMessage = `
                            <p>Short video advantage is not active.</p>
                            <p>To get short videos (30 seconds instead of 2 minutes):</p>
                            <p>1. Complete ${advantageResult.requiredReferrals || 3} successful referrals</p>
                            <p>2. Upgrade your plan</p>
                            <p>Or contact support for assistance:</p>
                            <p><a href="https://wa.me/+917483681936" target="_blank" class="support-link"><i class="fab fa-whatsapp"></i> WhatsApp Support</a></p>
                        `;
                    }

                    Swal.fire({
                        title: 'Short Video Advantage',
                        html: infoMessage,
                        icon: 'info',
                        confirmButtonText: 'OK'
                    });
                });
            }

            // Hide the enabled message
            if (shortVideoEnabledMsg) {
                shortVideoEnabledMsg.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error updating short video advantage status:', error);
    }
}

// Function to update wallet displays
function updateWalletDisplays(wallet = {}) {
    console.log('Updating wallet displays with:', wallet);

    try {
        // Parse all wallet values for consistency
        const earning = parseFloat(wallet.earning) || 0;
        const bonus = parseFloat(wallet.bonus) || 0;
        const balance = parseFloat(wallet.balance) || 0; // Main wallet balance (not displayed on work page)

        console.log('Parsed wallet values:', { earning, bonus, balance });

        // Update earning wallet display
        const earningWalletElement = document.querySelector('.earnings-card:nth-child(1) .amount');
        if (earningWalletElement) {
            earningWalletElement.textContent = formatCurrency(earning);
            console.log('Updated earning wallet display:', earningWalletElement.textContent);
        } else {
            console.error('Earning wallet element not found in the DOM');
        }

        // Update bonus wallet display
        const bonusWalletElement = document.querySelector('.earnings-card:nth-child(2) .amount');
        if (bonusWalletElement) {
            bonusWalletElement.textContent = formatCurrency(bonus);
            console.log('Updated bonus wallet display:', bonusWalletElement.textContent);
        } else {
            console.error('Bonus wallet element not found in the DOM');
        }
    } catch (error) {
        console.error('Error updating wallet displays:', error);
    }
}

// Function to preload videos and store in local storage
async function preloadVideos() {
    try {
        console.log('Preloading videos...');

        // Clear cached videos to ensure we get fresh ones on login
        localStorage.removeItem('mytube_cached_videos');

        // Check if we have a record of watched videos
        const watchedVideosStr = localStorage.getItem('mytube_watched_videos');
        let watchedVideos = watchedVideosStr ? JSON.parse(watchedVideosStr) : [];

        // Check if we have cached videos after clearing
        const storedVideos = localStorage.getItem('mytube_cached_videos');

        if (storedVideos) {
            cachedVideos = JSON.parse(storedVideos);
            console.log(`Loaded ${cachedVideos.length} videos from localStorage`);

            // If we have enough videos, return
            if (cachedVideos.length >= 100) {
                // Filter out any videos that have already been watched
                if (watchedVideos.length > 0) {
                    cachedVideos = cachedVideos.filter(video => !watchedVideos.includes(video));
                    console.log(`Filtered out ${watchedVideos.length} already watched videos`);

                    // If we don't have enough unwatched videos, get more
                    if (cachedVideos.length < 100) {
                        console.log(`Need more videos, only have ${cachedVideos.length} unwatched videos`);
                        // Get fresh videos since we don't have enough unwatched ones
                        await refreshVideos();
                    } else {
                        return cachedVideos;
                    }
                } else {
                    return cachedVideos;
                }
            }
        }

        // If we don't have cached videos or need more, get fresh ones
        await refreshVideos();
        return cachedVideos;
    } catch (error) {
        console.error('Error preloading videos:', error);
        // Fallback to sample videos
        cachedVideos = [...sampleVideos];

        // Shuffle the videos
        cachedVideos = shuffleArray(cachedVideos);

        while (cachedVideos.length < 100) {
            cachedVideos = cachedVideos.concat(shuffleArray([...sampleVideos]));
        }
        cachedVideos = cachedVideos.slice(0, 100);
        localStorage.setItem('mytube_cached_videos', JSON.stringify(cachedVideos));
        return cachedVideos;
    }
}

// Function to refresh videos and get fresh ones from the server
async function refreshVideos() {
    try {
        console.log('Refreshing videos and getting fresh ones...');

        // Initialize the video manager if not already initialized
        if (!videoManager.initialized) {
            await videoManager.initialize();
        }

        // Get videos from the VideoManager or use sample videos as fallback
        let videos = [];
        try {
            // Try to get videos from VideoManager
            const fetchedVideos = await videoManager.getVideos(100); // Get more videos to account for filtering
            videos = fetchedVideos;

            // Check if we have a record of watched videos
            const watchedVideosStr = localStorage.getItem('mytube_watched_videos');
            let watchedVideos = watchedVideosStr ? JSON.parse(watchedVideosStr) : [];

            // Filter out any videos that have already been watched
            if (watchedVideos.length > 0) {
                videos = videos.filter(video => !watchedVideos.includes(video));
                console.log(`Filtered out watched videos, have ${videos.length} new videos`);
            }
        } catch (error) {
            console.error('Error getting videos from VideoManager:', error);
            // Use sample videos as fallback
            videos = [...sampleVideos];

            // Check if we have a record of watched videos
            const watchedVideosStr = localStorage.getItem('mytube_watched_videos');
            let watchedVideos = watchedVideosStr ? JSON.parse(watchedVideosStr) : [];

            // Filter out any videos that have already been watched
            if (watchedVideos.length > 0) {
                videos = videos.filter(video => !watchedVideos.includes(video));
            }

            // If we don't have enough sample videos, duplicate them
            while (videos.length < 100) {
                videos = videos.concat(sampleVideos);
            }
        }

        // Shuffle the videos to randomize the order
        videos = shuffleArray(videos);

        // Trim to 100 videos
        videos = videos.slice(0, 100);

        // Store videos in localStorage
        cachedVideos = videos;
        localStorage.setItem('mytube_cached_videos', JSON.stringify(cachedVideos));
        console.log(`Stored ${cachedVideos.length} fresh videos in localStorage`);

        return cachedVideos;
    } catch (error) {
        console.error('Error refreshing videos:', error);
        throw error;
    }
}

// Helper function to shuffle an array
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

// Helper function to validate YouTube URLs
function isValidYouTubeUrl(url) {
    if (!url || typeof url !== 'string') return false;

    // Check for YouTube domain and video ID pattern
    const youtubeRegex = /^https?:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    return youtubeRegex.test(url);
}

// Function to load a new video
async function loadNewVideo() {
    try {
        console.log('Loading new video...');

        // Enhanced check for videos already submitted today
        // First check Firebase via the checkDailyVideoLimit function
        try {
            const limitStatus = await checkDailyVideoLimit();
            console.log('Checking submission status before loading new video:', limitStatus);

            // If videos have been submitted today according to Firebase
            if (limitStatus.alreadySubmitted) {
                console.log('Videos already submitted for today according to Firebase, not loading new video');

                // Ensure localStorage is consistent with Firebase
                const now = new Date();
                const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                localStorage.setItem('instra_videos_submitted', 'true');
                localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

                // Disable all interactive elements
                if (videoElement) videoElement.disabled = true;
                if (startVideoBtn) {
                    startVideoBtn.disabled = true;
                    startVideoBtn.classList.add('disabled-btn');
                    startVideoBtn.classList.add('hidden');
                }
                if (nextVideoBtn) {
                    nextVideoBtn.disabled = true;
                    nextVideoBtn.classList.add('hidden');
                }
                if (submitButton) submitButton.disabled = true;

                // Show a message in the video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    // Calculate next reset time (midnight tonight)
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    videoContainer.innerHTML = `
                        <div class="limit-reached-message">
                            <i class="fas fa-check-circle"></i>
                            <h3>Videos Submitted</h3>
                            <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                            <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                        </div>
                    `;
                }

                return;
            }
        } catch (checkError) {
            console.error('Error checking submission status from Firebase:', checkError);

            // Fallback to localStorage check if Firebase check fails
            const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';
            if (videosSubmitted) {
                console.log('Videos already submitted for today according to localStorage, not loading new video');

                // Disable all interactive elements
                if (videoElement) videoElement.disabled = true;
                if (startVideoBtn) {
                    startVideoBtn.disabled = true;
                    startVideoBtn.classList.add('disabled-btn');
                    startVideoBtn.classList.add('hidden');
                }
                if (nextVideoBtn) {
                    nextVideoBtn.disabled = true;
                    nextVideoBtn.classList.add('hidden');
                }
                if (submitButton) submitButton.disabled = true;

                // Show a message in the video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    // Calculate next reset time (midnight tonight)
                    const now = new Date();
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    videoContainer.innerHTML = `
                        <div class="limit-reached-message">
                            <i class="fas fa-check-circle"></i>
                            <h3>Videos Submitted</h3>
                            <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                            <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                        </div>
                    `;
                }

                return;
            }
        }

        // Make sure we have videos
        if (cachedVideos.length === 0) {
            await preloadVideos();
        }

        // Get the next video URL
        const videoUrl = cachedVideos[currentVideoIndex % cachedVideos.length];
        console.log(`Loading video ${currentVideoIndex + 1}/${cachedVideos.length}: ${videoUrl}`);

        // Validate the video URL before loading
        if (!videoUrl || !isValidYouTubeUrl(videoUrl)) {
            console.warn('Invalid video URL detected, skipping to next video:', videoUrl);
            currentVideoIndex++;
            if (currentVideoIndex < cachedVideos.length) {
                loadNewVideo(); // Try the next video
                return;
            } else {
                // If we've exhausted all videos, use sample videos
                console.log('All videos exhausted, using sample videos');
                cachedVideos = [...sampleVideos];
                currentVideoIndex = 0;
                loadNewVideo();
                return;
            }
        }

        // Set the video source
        if (videoElement) {
            // Check if it's a YouTube URL
            if (videoUrl && videoUrl.includes('youtube.com/watch?v=')) {
                // Extract the video ID from the YouTube URL
                const videoId = videoUrl.split('v=')[1].split('&')[0];

                // Create an iframe for YouTube embedding
                let youtubeIframe = document.getElementById('youtubeIframe');
                if (!youtubeIframe) {
                    // Create a new iframe if it doesn't exist
                    youtubeIframe = document.createElement('iframe');
                    youtubeIframe.id = 'youtubeIframe';
                    youtubeIframe.width = '100%';
                    youtubeIframe.height = '100%';
                    youtubeIframe.frameBorder = '0';
                    youtubeIframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                    youtubeIframe.allowFullscreen = true;

                    // Insert the iframe into the video container
                    const videoContainer = videoElement.parentElement;
                    videoContainer.appendChild(youtubeIframe);
                }

                // Set the iframe source to the YouTube embed URL with controls disabled
                // controls=0: Hide controls
                // disablekb=1: Disable keyboard controls
                // fs=0: Disable fullscreen
                // modestbranding=1: Hide YouTube logo
                // rel=0: Don't show related videos
                // iv_load_policy=3: Hide annotations
                youtubeIframe.src = `https://www.youtube.com/embed/${videoId}?enablejsapi=1&controls=0&disablekb=1&fs=0&modestbranding=1&rel=0&iv_load_policy=3`;

                // Add error handler for iframe loading failures
                youtubeIframe.onerror = () => {
                    console.warn('YouTube iframe failed to load, trying next video:', videoUrl);

                    // Try the next video
                    currentVideoIndex++;
                    if (currentVideoIndex < cachedVideos.length) {
                        loadNewVideo();
                    } else {
                        // If we've exhausted all videos, use sample videos
                        console.log('All videos exhausted due to iframe errors, using sample videos');
                        cachedVideos = [...sampleVideos];
                        currentVideoIndex = 0;
                        loadNewVideo();
                    }
                };

                // Add a timeout to detect videos that don't load within 10 seconds
                const loadTimeout = setTimeout(() => {
                    console.warn('Video loading timeout, trying next video:', videoUrl);

                    // Try the next video
                    currentVideoIndex++;
                    if (currentVideoIndex < cachedVideos.length) {
                        loadNewVideo();
                    } else {
                        // If we've exhausted all videos, use sample videos
                        console.log('All videos exhausted due to timeout, using sample videos');
                        cachedVideos = [...sampleVideos];
                        currentVideoIndex = 0;
                        loadNewVideo();
                    }
                }, 10000); // 10 second timeout

                // Clear timeout when iframe loads successfully
                youtubeIframe.onload = () => {
                    clearTimeout(loadTimeout);
                    console.log('YouTube iframe loaded successfully');
                };

                // Add a transparent overlay to prevent user interaction with the iframe
                let overlayDiv = document.getElementById('youtubeOverlay');
                if (!overlayDiv) {
                    overlayDiv = document.createElement('div');
                    overlayDiv.id = 'youtubeOverlay';
                    overlayDiv.style.position = 'absolute';
                    overlayDiv.style.top = '0';
                    overlayDiv.style.left = '0';
                    overlayDiv.style.width = '100%';
                    overlayDiv.style.height = '100%';
                    overlayDiv.style.zIndex = '10';
                    overlayDiv.style.cursor = 'default';

                    // Add the overlay to the video container
                    const videoContainer = videoElement.parentElement;
                    videoContainer.style.position = 'relative';
                    videoContainer.appendChild(overlayDiv);
                }

                // Hide the video element and show the iframe
                videoElement.style.display = 'none';
                youtubeIframe.style.display = 'block';
                overlayDiv.style.display = 'block';

                console.log('YouTube video loaded:', videoId);
            } else {
                // For direct video URLs, use the video element
                videoElement.src = videoUrl;
                videoElement.load();
                videoElement.style.display = 'block';

                // Hide the YouTube iframe if it exists
                const youtubeIframe = document.getElementById('youtubeIframe');
                if (youtubeIframe) {
                    youtubeIframe.style.display = 'none';
                }
            }

            videoElement.controls = true;

            // Make sure the next button is hidden and disabled
            if (nextVideoBtn) {
                nextVideoBtn.classList.add('hidden');
                nextVideoBtn.disabled = true;
            }

            // Show and enable the start button
            if (startVideoBtn) {
                startVideoBtn.classList.remove('hidden');
                startVideoBtn.disabled = false;
                startVideoBtn.classList.remove('disabled-btn');
            }

            // Hide loading overlay
            const loadingOverlay = document.getElementById('videoLoadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }

            // Don't autoplay - wait for user to click start button
            console.log('Video loaded, waiting for user to click start button');
        }
    } catch (error) {
        console.error('Error loading new video:', error);

        // Fallback to sample videos if there's an error
        try {
            console.log('Using fallback sample videos');
            if (videoElement) {
                const randomIndex = Math.floor(Math.random() * sampleVideos.length);
                const videoUrl = sampleVideos[randomIndex];

                // Check if it's a YouTube URL
                if (videoUrl && videoUrl.includes('youtube.com/watch?v=')) {
                    // Extract the video ID from the YouTube URL
                    const videoId = videoUrl.split('v=')[1].split('&')[0];

                    // Create an iframe for YouTube embedding
                    let youtubeIframe = document.getElementById('youtubeIframe');
                    if (!youtubeIframe) {
                        // Create a new iframe if it doesn't exist
                        youtubeIframe = document.createElement('iframe');
                        youtubeIframe.id = 'youtubeIframe';
                        youtubeIframe.width = '100%';
                        youtubeIframe.height = '100%';
                        youtubeIframe.frameBorder = '0';
                        youtubeIframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                        youtubeIframe.allowFullscreen = true;

                        // Insert the iframe into the video container
                        const videoContainer = videoElement.parentElement;
                        videoContainer.appendChild(youtubeIframe);
                    }

                    // Set the iframe source to the YouTube embed URL with controls disabled
                    // controls=0: Hide controls
                    // disablekb=1: Disable keyboard controls
                    // fs=0: Disable fullscreen
                    // modestbranding=1: Hide YouTube logo
                    // rel=0: Don't show related videos
                    // iv_load_policy=3: Hide annotations
                    youtubeIframe.src = `https://www.youtube.com/embed/${videoId}?enablejsapi=1&controls=0&disablekb=1&fs=0&modestbranding=1&rel=0&iv_load_policy=3`;

                    // Add a transparent overlay to prevent user interaction with the iframe
                    let overlayDiv = document.getElementById('youtubeOverlay');
                    if (!overlayDiv) {
                        overlayDiv = document.createElement('div');
                        overlayDiv.id = 'youtubeOverlay';
                        overlayDiv.style.position = 'absolute';
                        overlayDiv.style.top = '0';
                        overlayDiv.style.left = '0';
                        overlayDiv.style.width = '100%';
                        overlayDiv.style.height = '100%';
                        overlayDiv.style.zIndex = '10';
                        overlayDiv.style.cursor = 'default';

                        // Add the overlay to the video container
                        const videoContainer = videoElement.parentElement;
                        videoContainer.style.position = 'relative';
                        videoContainer.appendChild(overlayDiv);
                    }

                    // Hide the video element and show the iframe
                    videoElement.style.display = 'none';
                    youtubeIframe.style.display = 'block';
                    overlayDiv.style.display = 'block';

                    console.log('Fallback YouTube video loaded:', videoId);
                } else {
                    // For direct video URLs, use the video element
                    videoElement.src = videoUrl;
                    videoElement.load();
                    videoElement.style.display = 'block';

                    // Hide the YouTube iframe if it exists
                    const youtubeIframe = document.getElementById('youtubeIframe');
                    if (youtubeIframe) {
                        youtubeIframe.style.display = 'none';
                    }
                }

                videoElement.controls = true;

                // Make sure the next button is hidden and disabled
                if (nextVideoBtn) {
                    nextVideoBtn.classList.add('hidden');
                    nextVideoBtn.disabled = true;
                }

                // Show the start button
                if (startVideoBtn) {
                    startVideoBtn.classList.remove('hidden');
                }

                // Hide loading overlay
                const loadingOverlay = document.getElementById('videoLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }

                console.log('Fallback video loaded, waiting for user to click start button');
            }
        } catch (fallbackError) {
            console.error('Error loading fallback video:', fallbackError);
        }
    }
}

// Function to handle video completion
function handleVideoComplete() {
    try {
        console.log('Video timer completed, incrementing count and enabling next button...');

        // Stop video timer to track time spent on this video
        stopVideoTimer();

        // Increment the video count when timer actually completes
        todayVideoCount++;
        console.log('Video count incremented to:', todayVideoCount);

        // Update the UI with the new count
        updateVideoCountUI();

        // Enable the next button
        if (nextVideoBtn) {
            nextVideoBtn.classList.remove('hidden');
            nextVideoBtn.disabled = false;

            // Highlight the next button to draw attention to it
            nextVideoBtn.classList.add('highlight-button');

            // Remove the highlight after 2 seconds
            setTimeout(() => {
                nextVideoBtn.classList.remove('highlight-button');
            }, 2000);
        }

        // Make sure the start button remains disabled until next button is clicked
        if (startVideoBtn) {
            startVideoBtn.disabled = true;
            startVideoBtn.classList.add('disabled-btn');
        }

        // Keep video controls enabled so user can continue watching if desired

        // Don't disable the video player - let user continue watching if they want
        // Just make the next button available

        // No dialog box - just let the user proceed to the next video
    } catch (error) {
        console.error('Error handling video completion:', error);
    }
}

// Function to handle next video button click
async function handleNextVideo() {
    try {
        // Direct check for Sunday - as a failsafe
        const today = new Date();
        const isSunday = today.getDay() === 0;

        if (isSunday) {
            console.warn('TODAY IS SUNDAY - DIRECT CHECK IN handleNextVideo');
            // Get tomorrow's date for the message
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const tomorrowFormatted = formatDateForDisplay(tomorrow);

            // Show Sunday message
            Swal.fire({
                icon: 'warning',
                title: 'Sunday Holiday',
                text: 'The platform is on leave today. Please come back tomorrow.',
                confirmButtonText: 'OK',
                customClass: {
                    popup: 'leave-day-popup',
                    title: 'leave-day-title',
                    content: 'leave-day-content',
                    htmlContainer: 'leave-day-html',
                    text: 'leave-day-text'
                }
            });

            // Disable the work interface
            disableWorkInterface(`Sunday Leave. Work will resume on ${tomorrowFormatted}.`);
            return;
        }

        // Check if user is online
        if (!isUserOnline()) {
            Swal.fire({
                icon: 'warning',
                title: 'You are offline',
                text: 'You need to be online to continue watching videos. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Enhanced check for videos already submitted today
        // First check Firebase via the checkDailyVideoLimit function
        try {
            const limitStatus = await checkDailyVideoLimit();
            console.log('Checking submission status before proceeding to next video:', limitStatus);

            // If videos have been submitted today according to Firebase
            if (limitStatus.alreadySubmitted) {
                console.log('Videos already submitted for today according to Firebase, cannot proceed to next video');

                // Ensure localStorage is consistent with Firebase
                const now = new Date();
                const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                localStorage.setItem('instra_videos_submitted', 'true');
                localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

                Swal.fire({
                    icon: 'info',
                    title: 'Videos Submitted',
                    text: 'You have already submitted your videos for today. Please come back tomorrow.',
                    confirmButtonText: 'OK'
                });

                // Disable all interactive elements
                if (videoElement) videoElement.disabled = true;
                if (startVideoBtn) {
                    startVideoBtn.disabled = true;
                    startVideoBtn.classList.add('disabled-btn');
                    startVideoBtn.classList.add('hidden');
                }
                if (nextVideoBtn) {
                    nextVideoBtn.disabled = true;
                    nextVideoBtn.classList.add('hidden');
                }
                if (submitButton) submitButton.disabled = true;

                // Show a message in the video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    // Calculate next reset time (midnight tonight)
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    videoContainer.innerHTML = `
                        <div class="limit-reached-message">
                            <i class="fas fa-check-circle"></i>
                            <h3>Videos Submitted</h3>
                            <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                            <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                        </div>
                    `;
                }

                return;
            }
        } catch (checkError) {
            console.error('Error checking submission status from Firebase:', checkError);

            // Fallback to localStorage check if Firebase check fails
            const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';
            if (videosSubmitted) {
                console.log('Videos already submitted for today according to localStorage, cannot proceed to next video');

                Swal.fire({
                    icon: 'info',
                    title: 'Videos Submitted',
                    text: 'You have already submitted your videos for today. Please come back tomorrow.',
                    confirmButtonText: 'OK'
                });

                // Disable all interactive elements
                if (videoElement) videoElement.disabled = true;
                if (startVideoBtn) {
                    startVideoBtn.disabled = true;
                    startVideoBtn.classList.add('disabled-btn');
                    startVideoBtn.classList.add('hidden');
                }
                if (nextVideoBtn) {
                    nextVideoBtn.disabled = true;
                    nextVideoBtn.classList.add('hidden');
                }
                if (submitButton) submitButton.disabled = true;

                // Show a message in the video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    // Calculate next reset time (midnight tonight)
                    const now = new Date();
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    videoContainer.innerHTML = `
                        <div class="limit-reached-message">
                            <i class="fas fa-check-circle"></i>
                            <h3>Videos Submitted</h3>
                            <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                            <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                        </div>
                    `;
                }

                return;
            }
        }

        // Re-enable the start button for the next video
        if (startVideoBtn) {
            startVideoBtn.disabled = false;
            startVideoBtn.classList.remove('disabled-btn');
            startVideoBtn.classList.remove('hidden');
        }

        // Show loading overlay
        const loadingOverlay = document.getElementById('videoLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }

        console.log('Next video button clicked, processing video completion...');

        // Get the current video URL before incrementing
        const currentVideoUrl = cachedVideos[currentVideoIndex % cachedVideos.length];

        // Add the current video to the watched videos list
        if (currentVideoUrl) {
            // Get the current watched videos list
            const watchedVideosStr = localStorage.getItem('mytube_watched_videos');
            let watchedVideos = watchedVideosStr ? JSON.parse(watchedVideosStr) : [];

            // Add the current video if it's not already in the list
            if (!watchedVideos.includes(currentVideoUrl)) {
                watchedVideos.push(currentVideoUrl);
                // Store the updated list
                localStorage.setItem('mytube_watched_videos', JSON.stringify(watchedVideos));
                console.log(`Added video to watched list. Total watched: ${watchedVideos.length}`);
            }
        }

        // Don't increment the count here - it should only be incremented in handleVideoComplete()
        // when the timer actually finishes. This prevents the count from jumping prematurely.
        console.log('Video marked as watched, count will be incremented when timer completes');

        // Increment the video index
        currentVideoIndex++;

        // Check if we've reached the daily limit
        if (todayVideoCount >= 100) {
            // Enable the submit button
            if (submitButton) {
                submitButton.disabled = false;

                // Highlight the submit button to draw attention to it
                submitButton.classList.add('highlight-button');

                // Remove the highlight after 3 seconds
                setTimeout(() => {
                    submitButton.classList.remove('highlight-button');
                }, 3000);

                // Scroll to the submit button to make it visible
                submitButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            // Hide the next button
            if (nextVideoBtn) {
                nextVideoBtn.classList.add('hidden');
            }

            // Show a success message dialog
            Swal.fire({
                icon: 'success',
                title: 'Daily Limit Reached!',
                text: 'You have watched all 100 videos for today. Click the Submit button to claim your earnings.',
                confirmButtonText: 'OK'
            });

            // Hide loading overlay
            const loadingOverlay = document.getElementById('videoLoadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        } else {
            // Reset the video player for the next video if it exists
            if (videoPlayer) {
                console.log('Resetting video player for next video');
                videoPlayer.resetPlayer();
            }

            // Check if we need to refresh the video list
            // If we've gone through all cached videos or have less than 5 left, refresh the list
            if (currentVideoIndex >= cachedVideos.length || cachedVideos.length - currentVideoIndex < 5) {
                console.log('Running low on videos, refreshing video list...');

                // Show a loading indicator
                const loadingMessage = document.createElement('div');
                loadingMessage.className = 'ready-message';
                loadingMessage.innerHTML = '<i class="fas fa-sync fa-spin"></i> Loading more videos...';
                document.body.appendChild(loadingMessage);

                // Refresh videos asynchronously
                refreshVideos().then(() => {
                    console.log('Video list refreshed successfully');
                    // Reset the video index since we have a new list
                    currentVideoIndex = 0;

                    // Remove the loading message
                    if (loadingMessage && loadingMessage.parentNode) {
                        loadingMessage.parentNode.removeChild(loadingMessage);
                    }

                    // Load the next video
                    loadNewVideo();
                }).catch(error => {
                    console.error('Error refreshing videos:', error);

                    // Remove the loading message
                    if (loadingMessage && loadingMessage.parentNode) {
                        loadingMessage.parentNode.removeChild(loadingMessage);
                    }

                    // Hide loading overlay in case of error
                    const loadingOverlay = document.getElementById('videoLoadingOverlay');
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }

                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Error Loading Videos',
                        text: 'There was an error loading more videos. Please try again.',
                        confirmButtonText: 'OK'
                    });

                    // Try to load the next video anyway
                    loadNewVideo();
                });
            } else {
                // Load the next video
                loadNewVideo().catch(error => {
                    console.error('Error loading next video:', error);

                    // Hide loading overlay in case of error
                    const loadingOverlay = document.getElementById('videoLoadingOverlay');
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }
                });
            }

            // Hide the next button until the video is complete
            if (nextVideoBtn) {
                nextVideoBtn.classList.add('hidden');
            }

            // Re-enable video controls
            if (videoElement) {
                videoElement.controls = true;
            }
        }
    } catch (error) {
        console.error('Error handling next video:', error);
    }
}

/**
 * Clear all video-related data from local storage
 * This function should be called after videos are submitted
 */
function clearVideoLocalStorage() {
    try {
        console.log('Clearing video-related data from local storage...');

        // Clear video count
        localStorage.removeItem('instra_video_count');

        // Clear video date
        localStorage.removeItem('instra_daily_video_date');

        // Clear watched videos list
        localStorage.removeItem('mytube_watched_videos');

        // Clear cached videos
        localStorage.removeItem('mytube_cached_videos');

        // Set the submitted flag (this will be reset at midnight)
        localStorage.setItem('instra_videos_submitted', 'true');

        // Store the submission date in UTC format for consistent comparison
        const now = new Date();
        const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
        localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

        console.log('All video-related data cleared from local storage, submission date set to:', currentDateUTC);

        // Reset the cached videos array
        cachedVideos = [];
        currentVideoIndex = 0;
    } catch (error) {
        console.error('Error clearing video local storage:', error);
    }
}

/**
 * Update UI to show videos submitted state
 * This function updates all UI elements to reflect that videos have been submitted
 */
function updateSubmittedUI() {
    try {
        console.log('Updating UI to show videos submitted state...');

        // Reset the video count to 0
        todayVideoCount = 0;

        // Update the UI to show 0/100 videos
        updateVideoCountUI();

        // Disable all interactive elements
        if (videoElement) videoElement.disabled = true;
        if (startVideoBtn) {
            startVideoBtn.disabled = true;
            startVideoBtn.classList.add('disabled-btn');
            startVideoBtn.classList.add('hidden');
        }
        if (nextVideoBtn) {
            nextVideoBtn.disabled = true;
            nextVideoBtn.classList.add('hidden');
        }
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Submitted';
            submitButton.classList.add('submitted');
        }

        // Show a message in the video container
        const videoContainer = document.querySelector('.video-container');
        if (videoContainer) {
            // Calculate next reset time (midnight tonight)
            const now = new Date();
            const nextResetTime = new Date(now);
            nextResetTime.setHours(24, 0, 0, 0);

            videoContainer.innerHTML = `
                <div class="limit-reached-message">
                    <i class="fas fa-check-circle"></i>
                    <h3>Videos Submitted</h3>
                    <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                    <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                </div>
            `;
        }

        console.log('UI updated to show videos submitted state');
    } catch (error) {
        console.error('Error updating submitted UI:', error);
    }
}

// Function to update the video count UI
function updateVideoCountUI() {
    try {
        // Enforce the 100 videos per day limit
        if (todayVideoCount > 100) {
            todayVideoCount = 100;
            console.log('Enforcing 100 videos per day limit');
        }

        // Check if videos have been submitted for today
        const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';

        // If videos have been submitted, always show 100 as the count in the UI
        // but set todayVideoCount to 0 for the counter display
        if (videosSubmitted) {
            console.log('Videos already submitted for today, showing 100 as the count in UI but 0 in counter');

            // For UI display purposes, we'll show 0/100 videos
            todayVideoCount = 0;

            // Also update Firebase to ensure consistency - keep todayVideos at 100 to indicate submission
            try {
                const user = auth.currentUser;
                if (user) {
                    const userRef = doc(db, 'users', user.uid);

                    // Check if the submission flag is properly set
                    getDoc(userRef).then(doc => {
                        if (doc.exists()) {
                            const data = doc.data();
                            const stats = data.stats || {};

                            // If the submitted flag isn't set but we have a local flag, update Firebase
                            if (stats.dailyVideos && !stats.dailyVideos.submitted) {
                                console.log('Found inconsistency: local submitted flag is true but Firebase flag is false');

                                // Get the current date in UTC format for consistent comparison
                                const now = new Date();
                                const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format

                                updateDoc(userRef, {
                                    'stats.todayVideos': 100,
                                    'stats.todayTranslations': 100,
                                    'stats.dailyVideos.submitted': true,
                                    'stats.dailyVideos.date': currentDateUTC,
                                    'stats.dailyTranslations.submitted': true,
                                    'stats.dailyTranslations.date': currentDateUTC,
                                    'stats.lastSubmission': {
                                        date: currentDateUTC,
                                        timestamp: serverTimestamp(),
                                        count: 50,
                                        submitted: true
                                    }
                                }).then(() => {
                                    console.log('Updated Firebase with submitted count of 50 and set submitted flag');
                                }).catch(error => {
                                    console.error('Error updating Firebase with submitted count and flag:', error);
                                });
                            } else {
                                console.log('Submission flags are consistent between localStorage and Firebase');
                            }
                        }
                    }).catch(error => {
                        console.error('Error checking submission flags in Firebase:', error);
                    });
                }
            } catch (firebaseError) {
                console.error('Error updating Firebase in updateVideoCountUI:', firebaseError);
            }
        }

        // Update today's videos count
        if (todayVideosElement) {
            // If videos have been submitted, show 100 in the today's videos count
            // but keep the counter at 0/100
            if (videosSubmitted) {
                todayVideosElement.textContent = "100";
                console.log('Videos submitted, showing 100 in today\'s videos count');
            } else {
                todayVideosElement.textContent = todayVideoCount;
            }
        }

        // Update the counter in the progress circle
        if (counterElement) {
            counterElement.textContent = todayVideoCount;
        }

        // Update videos left
        if (videosLeftElement) {
            const videosLeft = Math.max(0, 100 - todayVideoCount);
            videosLeftElement.textContent = videosLeft.toString();
        }

        // Update localStorage for future use
        localStorage.setItem('instra_video_count', todayVideoCount.toString());

        // Enable/disable the submit button based on the count
        if (submitButton) {
            if (videosSubmitted) {
                // If videos have been submitted, disable the submit button
                submitButton.disabled = true;
            } else {
                // Otherwise, enable it only if 100 videos have been watched
                submitButton.disabled = todayVideoCount < 100;
            }
        }

        // If we've reached 100 videos, show a message and disable the next button
        if (todayVideoCount >= 100 && nextVideoBtn) {
            nextVideoBtn.classList.add('hidden');

            // Show the submit button only if videos haven't been submitted yet
            if (submitButton && !videosSubmitted) {
                submitButton.disabled = false;

                // Scroll to the submit button to make it visible
                submitButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    } catch (error) {
        console.error('Error updating video count UI:', error);
    }
}

// Function to handle start button click
async function handleStartVideo() {
    try {
        console.log('Start button clicked...');

        // Direct check for Sunday - as a failsafe
        const today = new Date();
        const isSunday = today.getDay() === 0;

        if (isSunday) {
            console.warn('TODAY IS SUNDAY - DIRECT CHECK IN handleStartVideo');
            // Get tomorrow's date for the message
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const tomorrowFormatted = formatDateForDisplay(tomorrow);

            // Show Sunday message
            Swal.fire({
                icon: 'warning',
                title: 'Sunday Holiday',
                text: 'The platform is on leave today. Please come back tomorrow.',
                confirmButtonText: 'OK',
                customClass: {
                    popup: 'leave-day-popup',
                    title: 'leave-day-title',
                    content: 'leave-day-content',
                    htmlContainer: 'leave-day-html',
                    text: 'leave-day-text'
                }
            });

            // Disable the work interface
            disableWorkInterface(`Sunday Leave. Work will resume on ${tomorrowFormatted}.`);
            return;
        }

        // Check if today is a system-wide leave day
        console.log('DEBUG: handleStartVideo() - Checking for system-wide leave...');
        clearLeaveCache(); // Clear cache to get fresh data
        const isSystemLeaveToday = await isLeaveDay();
        console.log('DEBUG: handleStartVideo() - isLeaveDay() result:', isSystemLeaveToday);

        if (isSystemLeaveToday) {
            console.log('Cannot start videos - today is a system-wide leave day');

            Swal.fire({
                icon: 'warning',
                title: 'Leave Day',
                text: 'Video watching is not allowed on leave days.',
                confirmButtonText: 'OK'
            });

            return;
        }

        // Check if the current user is on individual leave today
        const user = auth.currentUser;
        if (user) {
            const userLeaveStatus = await checkUserLeaveStatus(user.uid);
            if (userLeaveStatus.onLeave) {
                console.log('Cannot start videos - user is on personal leave today');

                Swal.fire({
                    icon: 'warning',
                    title: 'Personal Leave',
                    text: `You are on personal leave today (${userLeaveStatus.reason}). Video watching is not allowed.`,
                    confirmButtonText: 'OK'
                });

                return;
            }
        }

        // Check if user is online
        if (!isUserOnline()) {
            Swal.fire({
                icon: 'warning',
                title: 'You are offline',
                text: 'You need to be online to watch videos. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Enhanced check for videos already submitted today
        // First check Firebase via the checkDailyVideoLimit function
        try {
            const limitStatus = await checkDailyVideoLimit();
            console.log('Checking submission status before starting video:', limitStatus);

            // If videos have been submitted today according to Firebase
            if (limitStatus.alreadySubmitted) {
                console.log('Videos already submitted for today according to Firebase, cannot start video');

                // Ensure localStorage is consistent with Firebase
                const now = new Date();
                const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                localStorage.setItem('instra_videos_submitted', 'true');
                localStorage.setItem('instra_videos_submitted_date', currentDateUTC);

                Swal.fire({
                    icon: 'info',
                    title: 'Videos Submitted',
                    text: 'You have already submitted your videos for today. Please come back tomorrow.',
                    confirmButtonText: 'OK'
                });

                // Disable the start button
                if (startVideoBtn) {
                    startVideoBtn.disabled = true;
                    startVideoBtn.classList.add('disabled-btn');
                    startVideoBtn.classList.add('hidden');
                }

                // Show a message in the video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    // Calculate next reset time (midnight tonight)
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    videoContainer.innerHTML = `
                        <div class="limit-reached-message">
                            <i class="fas fa-check-circle"></i>
                            <h3>Videos Submitted</h3>
                            <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                            <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                        </div>
                    `;
                }

                return;
            }
        } catch (checkError) {
            console.error('Error checking submission status from Firebase:', checkError);

            // Fallback to localStorage check if Firebase check fails
            const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';
            if (videosSubmitted) {
                console.log('Videos already submitted for today according to localStorage, cannot start video');

                Swal.fire({
                    icon: 'info',
                    title: 'Videos Submitted',
                    text: 'You have already submitted your videos for today. Please come back tomorrow.',
                    confirmButtonText: 'OK'
                });

                // Disable the start button
                if (startVideoBtn) {
                    startVideoBtn.disabled = true;
                    startVideoBtn.classList.add('disabled-btn');
                    startVideoBtn.classList.add('hidden');
                }

                // Show a message in the video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    // Calculate next reset time (midnight tonight)
                    const now = new Date();
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    videoContainer.innerHTML = `
                        <div class="limit-reached-message">
                            <i class="fas fa-check-circle"></i>
                            <h3>Videos Submitted</h3>
                            <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                            <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                        </div>
                    `;
                }

                return;
            }
        }

        // Start the timer when the start button is clicked
        // This ensures the timer only starts when explicitly requested by the user
        console.log('Starting timer from start button click');

        // Reset timer state flags
        window.isTimerPaused = false;
        window.youTubeTimerPaused = false;

        // Clear any existing timer to prevent multiple timers
        if (window.currentTimerInterval) {
            clearInterval(window.currentTimerInterval);
            window.currentTimerInterval = null;
            console.log('Cleared existing timer before starting new one');
        }

        // Reset timer display
        const timerElement = document.getElementById('videoTimer');
        if (timerElement) {
            timerElement.style.color = ''; // Reset color
            timerElement.classList.remove('loading');
            delete timerElement.dataset.originalTime;
        }

        // Start a new timer
        startVideoTimer();

        // Make sure the next button is hidden and disabled
        if (nextVideoBtn) {
            nextVideoBtn.classList.add('hidden');
            nextVideoBtn.disabled = true;
        }

        // Show loading overlay
        const loadingOverlay = document.getElementById('videoLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }

        // Check if we're using a YouTube iframe
        const youtubeIframe = document.getElementById('youtubeIframe');
        if (youtubeIframe && youtubeIframe.style.display !== 'none') {
            // For YouTube videos, we need to reload the iframe with autoplay=1
            console.log('Starting YouTube video playback...');

            // Get the current src
            const currentSrc = youtubeIframe.src;

            // Add autoplay=1 parameter to force playback
            if (!currentSrc.includes('autoplay=1')) {
                // Show loading in timer
                const timerElement = document.getElementById('videoTimer');
                if (timerElement) {
                    timerElement.textContent = 'Loading...';
                }

                // Create a YouTube API ready function
                window.onYouTubeIframeAPIReady = function() {
                    console.log('YouTube API ready');
                };

                // Remove any existing onload handler to prevent duplicates
                youtubeIframe.onload = null;

                // Add event listener for iframe load
                youtubeIframe.onload = function() {
                    console.log('YouTube iframe loaded');

                    // Hide loading overlay immediately when iframe loads
                    const loadingOverlay = document.getElementById('videoLoadingOverlay');
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }

                    // We need to wait for the actual video to start playing
                    // This is done by checking if the iframe has loaded and then
                    // monitoring the video state through postMessage API

                    // Remove any existing message event listeners to prevent duplicates
                    if (window.youtubeMessageListener) {
                        window.removeEventListener('message', window.youtubeMessageListener);
                    }

                    // Create a new message listener
                    window.youtubeMessageListener = function(event) {
                        // Make sure the message is from YouTube
                        if (event.origin !== 'https://www.youtube.com') return;

                        try {
                            const data = JSON.parse(event.data);

                            // Check if this is a player state change event
                            if (data.event === 'onStateChange') {
                                // YT.PlayerState.PLAYING = 1
                                if (data.info === 1) {
                                    console.log('YouTube video is now playing, starting timer');
                                    // The timer should ONLY be started by the start button click
                                    // Do not start or resume the timer here
                                    console.log('YouTube video playing, checking timer status');
                                    if (window.currentTimerInterval) {
                                        console.log('Timer is already running');
                                    } else {
                                        console.log('Timer not started yet - waiting for user to click start button');
                                    }

                                    // Update timer display
                                    if (timerElement) {
                                        // Timer will be updated by startVideoTimer
                                        timerElement.classList.remove('loading');
                                    }

                                    // Hide the loading overlay
                                    const loadingOverlay = document.getElementById('videoLoadingOverlay');
                                    if (loadingOverlay) {
                                        loadingOverlay.style.display = 'none';
                                    }
                                }
                                // YT.PlayerState.PAUSED = 2, YT.PlayerState.BUFFERING = 3
                                else if (data.info === 2 || data.info === 3) {
                                    console.log('YouTube video paused or buffering');
                                    // If we have a timer interval, pause it
                                    if (window.currentTimerInterval) {
                                        // We don't have direct access to pauseTimer here
                                        // So we'll just clear the interval and set a flag
                                        window.youTubeTimerPaused = true;

                                        // Update timer display
                                        if (timerElement) {
                                            if (data.info === 3) {
                                                timerElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buffering...';
                                            }
                                        }
                                    }
                                }
                                // YT.PlayerState.PLAYING again after pause/buffer
                                else if (data.info === 1 && window.youTubeTimerPaused) {
                                    console.log('YouTube video resumed after pause/buffer');
                                    // Reset the pause flag
                                    window.youTubeTimerPaused = false;

                                    // We don't have direct access to resumeTimer here
                                    // The timer will continue on the next interval tick
                                }
                            }
                        } catch (e) {
                            console.error('Error parsing YouTube message:', e);
                        }
                    };

                    // Add the message listener
                    window.addEventListener('message', window.youtubeMessageListener);

                    // After a short delay, check if the video has started playing
                    // But DO NOT force start the timer - only update the UI
                    setTimeout(function() {
                        console.log('Checking YouTube video playback status...');

                        // Update timer display - remove loading state but don't start timer
                        if (timerElement) {
                            timerElement.classList.remove('loading');
                        }

                        // Hide the loading overlay
                        const loadingOverlay = document.getElementById('videoLoadingOverlay');
                        if (loadingOverlay) {
                            loadingOverlay.style.display = 'none';
                        }

                        // Log the timer status but don't start it automatically
                        if (window.currentTimerInterval) {
                            console.log('Timer is already running');
                        } else {
                            console.log('Timer not started yet - waiting for user to click start button');
                        }
                    }, 3000);
                };

                // Set the source with autoplay, enablejsapi, and mute=1 (required for autoplay in most browsers)
                youtubeIframe.src = currentSrc + '&autoplay=1&enablejsapi=1&mute=0&playsinline=1';
            }
        } else if (videoElement) {
            // For direct video files, play the video element
            videoElement.play().then(() => {
                // Do NOT start the timer automatically - only check its status
                console.log('Video started playing');

                // Check if timer is already running but don't start it
                if (window.currentTimerInterval) {
                    console.log('Timer already running for direct video');
                } else {
                    console.log('Timer not started yet - waiting for user to click start button');
                }

                // Hide the loading overlay
                const loadingOverlay = document.getElementById('videoLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }).catch(error => {
                console.error('Error starting video:', error);

                // Hide the loading overlay
                const loadingOverlay = document.getElementById('videoLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }

                Swal.fire({
                    title: 'Video Playback Error',
                    text: 'There was an error starting the video. Please try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }

        // Disable the start button (don't hide it)
        if (startVideoBtn) {
            startVideoBtn.disabled = true;
            // Add a visual indication that it's disabled
            startVideoBtn.classList.add('disabled-btn');
        }
    } catch (error) {
        console.error('Error handling start video:', error);
    }
}

// Function to start the video timer
function startVideoTimer() {
    try {
        // Check if user is online before starting timer
        if (!isUserOnline()) {
            console.log('User is offline, cannot start timer');
            const timerElement = document.getElementById('videoTimer');
            if (timerElement) {
                timerElement.innerHTML = '<i class="fas fa-wifi-slash"></i> Offline';
            }

            // Show offline message
            Swal.fire({
                icon: 'warning',
                title: 'You are offline',
                text: 'You need to be online to watch videos. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Clear any existing timer to prevent multiple timers
        if (window.currentTimerInterval) {
            clearInterval(window.currentTimerInterval);
            window.currentTimerInterval = null;
            console.log('Cleared existing timer before starting a new one');
        }

        // Get the video duration based on advantage status and custom user settings
        // First check if user has custom video duration or short video advantage
        const user = auth.currentUser;
        if (user) {
            getUserVideoDuration(user.uid).then(duration => {
                console.log(`Using video duration: ${duration} seconds for user ${user.uid}`);

            console.log(`Starting video timer for ${duration} seconds`);

            // Start tracking time for this video
            startVideoTimeTracking();

            // Create a countdown timer
            let timeLeft = duration;
            const timerElement = document.getElementById('videoTimer');

            if (timerElement) {
                // Format the time nicely (MM:SS format)
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                timerElement.textContent = formattedTime;
                timerElement.classList.remove('hidden');
                timerElement.classList.remove('loading');
            }

            // Flag to track if timer is currently paused due to buffering or network issues
            window.isTimerPaused = false;

            // Clear any existing timer interval
            if (window.currentTimerInterval) {
                clearInterval(window.currentTimerInterval);
                window.currentTimerInterval = null;
            }

            // Start the countdown
            window.currentTimerInterval = setInterval(() => {
                // Only decrement time if the timer is not paused, user is online, and page is visible
                if (!window.isTimerPaused && isUserOnline() && !window.youTubeTimerPaused && !document.hidden) {
                    timeLeft--;

                    if (timerElement) {
                        // Only update the timer display if it's not showing a status message
                        if (!timerElement.innerHTML.includes('fa-')) {
                            // Format the time nicely (MM:SS format)
                            const minutes = Math.floor(timeLeft / 60);
                            const seconds = timeLeft % 60;
                            const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                            timerElement.textContent = formattedTime;
                        }

                        // Update progress bar if it exists
                        const progressBar = document.getElementById('videoProgressBar');
                        if (progressBar) {
                            const progressPercentage = ((duration - timeLeft) / duration) * 100;
                            progressBar.style.width = `${progressPercentage}%`;
                        }
                    }

                    // When timer reaches zero, complete the video
                    if (timeLeft <= 0) {
                        console.log('Timer reached zero, completing video');
                        clearInterval(window.currentTimerInterval);
                        window.currentTimerInterval = null;

                        // Update timer display to show completion
                        if (timerElement) {
                            timerElement.textContent = '0:00';
                            timerElement.style.color = '#4CAF50'; // Green color for completion
                        }

                        // Complete the video
                        handleVideoComplete();
                    }
                }
                // If user is offline, pause the timer and show message
                else if (!isUserOnline()) {
                    if (timerElement && !timerElement.innerHTML.includes('Offline')) {
                        // Save current time display
                        const minutes = Math.floor(timeLeft / 60);
                        const seconds = timeLeft % 60;
                        const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                        timerElement.dataset.originalTime = formattedTime;
                        timerElement.innerHTML = '<i class="fas fa-wifi-slash"></i> Offline';
                    }
                }
                // If page is hidden, show appropriate message
                else if (document.hidden) {
                    if (timerElement && !timerElement.innerHTML.includes('Paused')) {
                        // Save current time display
                        const minutes = Math.floor(timeLeft / 60);
                        const seconds = timeLeft % 60;
                        const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                        timerElement.dataset.originalTime = formattedTime;
                        timerElement.innerHTML = '<i class="fas fa-pause-circle"></i> Paused';
                    }
                }
                // If user comes back online or page becomes visible, restore timer display
                else if (timerElement && timerElement.dataset.originalTime) {
                    timerElement.textContent = timerElement.dataset.originalTime;
                    timerElement.style.color = ''; // Reset color
                    delete timerElement.dataset.originalTime;
                }
            }, 1000);

            // No need to store timer interval again - it's already in window.currentTimerInterval
        }).catch(error => {
            console.error('Error checking video advantage status:', error);

            // Fallback to default duration (2 minutes)
            const duration = 120; // 2 minutes in seconds
            console.log(`Using fallback duration of ${duration} seconds`);

            // Create a countdown timer with fallback duration
            let timeLeft = duration;
            const timerElement = document.getElementById('videoTimer');

            if (timerElement) {
                // Format the time nicely (MM:SS format)
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                timerElement.textContent = formattedTime;
                timerElement.classList.remove('hidden');
            }

            // Clear any existing timer interval
            if (window.currentTimerInterval) {
                clearInterval(window.currentTimerInterval);
                window.currentTimerInterval = null;
            }

            // Flag to track if timer is currently paused due to buffering or network issues
            window.isTimerPaused = false;
            window.youTubeTimerPaused = false;

            // Start the countdown
            window.currentTimerInterval = setInterval(() => {
                // Only decrement time if the timer is not paused, user is online, and page is visible
                if (!window.isTimerPaused && isUserOnline() && !window.youTubeTimerPaused && !document.hidden) {
                    timeLeft--;

                    if (timerElement) {
                        // Only update the timer display if it's not showing a status message
                        if (!timerElement.innerHTML.includes('fa-')) {
                            // Format the time nicely (MM:SS format)
                            const minutes = Math.floor(timeLeft / 60);
                            const seconds = timeLeft % 60;
                            const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                            timerElement.textContent = formattedTime;
                        }

                        // Update progress bar if it exists
                        const progressBar = document.getElementById('videoProgressBar');
                        if (progressBar) {
                            const progressPercentage = ((duration - timeLeft) / duration) * 100;
                            progressBar.style.width = `${progressPercentage}%`;
                        }
                    }

                    // When timer reaches zero, complete the video
                    if (timeLeft <= 0) {
                        console.log('Fallback timer reached zero, completing video');
                        clearInterval(window.currentTimerInterval);
                        window.currentTimerInterval = null;

                        // Update timer display to show completion
                        if (timerElement) {
                            timerElement.textContent = '0:00';
                            timerElement.style.color = '#4CAF50'; // Green color for completion
                        }

                        // Complete the video
                        handleVideoComplete();
                    }
                }
                // If user is offline, pause the timer and show message
                else if (!isUserOnline()) {
                    if (timerElement && !timerElement.innerHTML.includes('Offline')) {
                        // Save current time display
                        const minutes = Math.floor(timeLeft / 60);
                        const seconds = timeLeft % 60;
                        const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                        timerElement.dataset.originalTime = formattedTime;
                        timerElement.innerHTML = '<i class="fas fa-wifi-slash"></i> Offline';
                    }
                }
                // If page is hidden, show appropriate message
                else if (document.hidden) {
                    if (timerElement && !timerElement.innerHTML.includes('Hidden')) {
                        // Save current time display
                        const minutes = Math.floor(timeLeft / 60);
                        const seconds = timeLeft % 60;
                        const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                        timerElement.dataset.originalTime = formattedTime;
                        timerElement.innerHTML = '<i class="fas fa-pause-circle"></i> Paused';
                    }
                }
            }, 1000);

            // Timer interval is already stored in window.currentTimerInterval
        }).catch(error => {
            console.error('Error getting user video duration:', error);
            // Fallback to default duration
            const duration = 120; // 2 minutes in seconds
            console.log(`Using fallback duration of ${duration} seconds`);

            // Create a countdown timer with fallback duration
            let timeLeft = duration;
            const timerElement = document.getElementById('videoTimer');

            if (timerElement) {
                // Format the time nicely (MM:SS format)
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

                timerElement.textContent = formattedTime;
                timerElement.classList.remove('hidden');
                timerElement.classList.remove('loading');
            }

            // Start the countdown with fallback duration
            window.currentTimerInterval = setInterval(() => {
                if (!window.isTimerPaused && isUserOnline() && !window.youTubeTimerPaused && !document.hidden) {
                    timeLeft--;

                    if (timerElement) {
                        if (!timerElement.innerHTML.includes('fa-')) {
                            const minutes = Math.floor(timeLeft / 60);
                            const seconds = timeLeft % 60;
                            const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                            timerElement.textContent = formattedTime;
                        }
                    }

                    if (timeLeft <= 0) {
                        clearInterval(window.currentTimerInterval);
                        window.currentTimerInterval = null;
                        handleVideoComplete();
                    }
                }
            }, 1000);
        });
    } else {
        // Fallback if no user is authenticated
        console.log('No authenticated user, using default duration');
        const duration = 300; // 5 minutes in seconds

        let timeLeft = duration;
        const timerElement = document.getElementById('videoTimer');

        if (timerElement) {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
            timerElement.textContent = formattedTime;
        }

        window.currentTimerInterval = setInterval(() => {
            if (!window.isTimerPaused && isUserOnline() && !window.youTubeTimerPaused && !document.hidden) {
                timeLeft--;
                if (timerElement && !timerElement.innerHTML.includes('fa-')) {
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                    timerElement.textContent = formattedTime;
                }
                if (timeLeft <= 0) {
                    clearInterval(window.currentTimerInterval);
                    window.currentTimerInterval = null;
                    handleVideoComplete();
                }
            }
        }, 1000);
    }
    } catch (error) {
        console.error('Error in startVideoTimer:', error);
    }
}

// Debug utility function to log detailed information about the submission process
async function debugSubmissionState() {
    try {
        const user = auth.currentUser;
        if (!user) {
            console.error('DEBUG: No authenticated user found');
            return;
        }

        // Get user data from Firebase
        const userRef = doc(db, 'users', user.uid);
        const userSnap = await getDoc(userRef);

        if (!userSnap.exists()) {
            console.error('DEBUG: User document not found');
            return;
        }

        const userData = userSnap.data();

        // Log detailed information about the user's state
        console.log('DEBUG: User submission state:', {
            userId: user.uid,
            stats: userData.stats || {},
            wallet: userData.wallet || {},
            plan: userData.plan || {},
            todayVideos: userData.stats?.todayVideos,
            dailyVideos: userData.stats?.dailyVideos,
            totalVideosCompleted: userData.stats?.totalVideosCompleted,
            lastSubmission: userData.stats?.lastSubmission,
            localStorageSubmitted: localStorage.getItem('instra_videos_submitted'),
            localStorageSubmittedDate: localStorage.getItem('instra_videos_submitted_date'),
            localStorageVideoCount: localStorage.getItem('instra_video_count'),
            localStorageVideoDate: localStorage.getItem('instra_daily_video_date')
        });

        // Check for recent transactions
        try {
            const transactionsQuery = query(
                collection(db, 'transactions'),
                where('userId', '==', user.uid),
                where('type', '==', 'earnings'),
                orderBy('timestamp', 'desc'),
                limit(5)
            );

            const transactionsSnap = await getDocs(transactionsQuery);
            const transactions = [];

            transactionsSnap.forEach(doc => {
                transactions.push({
                    id: doc.id,
                    ...doc.data(),
                    timestamp: doc.data().timestamp?.toDate?.() || 'No timestamp'
                });
            });

            console.log('DEBUG: Recent earnings transactions:', transactions);
        } catch (transactionError) {
            console.error('DEBUG: Error fetching transactions:', transactionError);
        }
    } catch (error) {
        console.error('DEBUG: Error in debugSubmissionState:', error);
    }
}

// Function to show processing overlay with detailed status
function showProcessingOverlay(message = 'Processing your submission...') {
    // Create overlay if it doesn't exist
    let overlay = document.getElementById('submissionProcessingOverlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'submissionProcessingOverlay';
        overlay.innerHTML = `
            <div class="processing-overlay-backdrop">
                <div class="processing-overlay-content">
                    <div class="processing-spinner">
                        <div class="spinner-ring"></div>
                    </div>
                    <h3 id="processingTitle">Processing Submission</h3>
                    <p id="processingMessage">${message}</p>
                    <div class="processing-steps">
                        <div class="step" id="step-validation">
                            <i class="fas fa-check-circle step-icon"></i>
                            <span>Validating submission</span>
                        </div>
                        <div class="step" id="step-earnings">
                            <i class="fas fa-clock step-icon"></i>
                            <span>Adding earnings to wallet</span>
                        </div>
                        <div class="step" id="step-transaction">
                            <i class="fas fa-clock step-icon"></i>
                            <span>Creating transaction record</span>
                        </div>
                        <div class="step" id="step-verification">
                            <i class="fas fa-clock step-icon"></i>
                            <span>Verifying completion</span>
                        </div>
                    </div>
                    <div class="processing-note">
                        <i class="fas fa-info-circle"></i>
                        <span>Please wait while we process your submission. Do not close this page.</span>
                    </div>
                </div>
            </div>
        `;

        // Add CSS styles
        const style = document.createElement('style');
        style.textContent = `
            #submissionProcessingOverlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .processing-overlay-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(5px);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .processing-overlay-content {
                background: white;
                border-radius: 15px;
                padding: 30px;
                max-width: 400px;
                width: 90%;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease-out;
            }

            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .processing-spinner {
                margin-bottom: 20px;
            }

            .spinner-ring {
                width: 60px;
                height: 60px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #4caf50;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            #processingTitle {
                color: #333;
                margin-bottom: 10px;
                font-size: 1.5rem;
            }

            #processingMessage {
                color: #666;
                margin-bottom: 20px;
            }

            .processing-steps {
                text-align: left;
                margin: 20px 0;
            }

            .step {
                display: flex;
                align-items: center;
                margin: 10px 0;
                padding: 8px;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .step.completed {
                background: #e8f5e8;
                color: #2e7d32;
            }

            .step.processing {
                background: #fff3e0;
                color: #f57c00;
            }

            .step.error {
                background: #ffebee;
                color: #c62828;
            }

            .step-icon {
                margin-right: 10px;
                width: 16px;
            }

            .step.completed .step-icon {
                color: #4caf50;
            }

            .step.processing .step-icon {
                color: #ff9800;
                animation: pulse 1.5s infinite;
            }

            .step.error .step-icon {
                color: #f44336;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .processing-note {
                background: #e3f2fd;
                padding: 10px;
                border-radius: 8px;
                margin-top: 20px;
                display: flex;
                align-items: center;
                font-size: 0.9rem;
                color: #1976d2;
            }

            .processing-note i {
                margin-right: 8px;
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(overlay);
    }

    overlay.style.display = 'flex';
    document.getElementById('processingMessage').textContent = message;
}

// Function to update processing step status
function updateProcessingStep(stepId, status, message = null) {
    const step = document.getElementById(stepId);
    if (!step) return;

    // Remove all status classes
    step.classList.remove('completed', 'processing', 'error');

    // Add new status class
    step.classList.add(status);

    // Update icon based on status
    const icon = step.querySelector('.step-icon');
    if (status === 'completed') {
        icon.className = 'fas fa-check-circle step-icon';
    } else if (status === 'processing') {
        icon.className = 'fas fa-spinner fa-spin step-icon';
    } else if (status === 'error') {
        icon.className = 'fas fa-times-circle step-icon';
    }

    // Update message if provided
    if (message) {
        const span = step.querySelector('span');
        span.textContent = message;
    }
}

// Function to hide processing overlay
function hideProcessingOverlay() {
    const overlay = document.getElementById('submissionProcessingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// Global flag to prevent concurrent submissions
let isSubmissionInProgress = false;
let lastSubmissionAttempt = 0;

// Function to handle submit button click
async function handleSubmit() {
    try {
        const now = Date.now();
        console.log('Submit button clicked at:', new Date(now).toISOString());

        // Prevent rapid double-clicks (within 2 seconds)
        if (now - lastSubmissionAttempt < 2000) {
            console.log('Ignoring rapid double-click, last attempt was', (now - lastSubmissionAttempt), 'ms ago');
            return;
        }
        lastSubmissionAttempt = now;

        // Check if submission is already in progress
        if (isSubmissionInProgress) {
            console.log('Submission already in progress, ignoring duplicate click');
            return;
        }

        // Set the flag to prevent concurrent submissions
        isSubmissionInProgress = true;

        // Clear any previous session transaction flags to prevent interference
        // But first check if we already have a transaction for today to prevent duplicates
        const existingSessionTransactionId = window.sessionTransactionId;
        const existingSessionTransactionCreated = window.sessionTransactionCreated;

        if (existingSessionTransactionId || existingSessionTransactionCreated) {
            console.log('Found existing session transaction data:', {
                sessionTransactionId: existingSessionTransactionId,
                sessionTransactionCreated: existingSessionTransactionCreated
            });

            // Check if this is from today's session
            const today = new Date().toISOString().split('T')[0];
            const lastSubmissionDate = localStorage.getItem('last_submission_date');

            if (lastSubmissionDate === today) {
                console.log('Transaction already created today in this session - preventing duplicate submission');

                Swal.fire({
                    icon: 'info',
                    title: 'Already Submitted',
                    text: 'You have already submitted your videos for today in this session.',
                    confirmButtonText: 'OK'
                });

                isSubmissionInProgress = false;
                return;
            }
        }

        // Clear session flags for new submission
        window.sessionTransactionCreated = false;
        window.sessionTransactionId = null;

        // Log detailed debug information about the current state
        await debugSubmissionState();

        // Show processing overlay immediately
        showProcessingOverlay('Initializing submission process...');
        updateProcessingStep('step-validation', 'processing', 'Validating your submission...');

        // Immediately disable the submit button to prevent multiple clicks
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';
            submitButton.classList.add('submitting');
        }

        // Direct check for Sunday - as a failsafe
        const today = new Date();
        const isSunday = today.getDay() === 0;

        if (isSunday) {
            console.warn('TODAY IS SUNDAY - DIRECT CHECK IN handleSubmit');
            // Get tomorrow's date for the message
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const tomorrowFormatted = formatDateForDisplay(tomorrow);

            // Show Sunday message
            Swal.fire({
                icon: 'warning',
                title: 'Sunday Holiday',
                text: 'The platform is on leave today. Please come back tomorrow.',
                confirmButtonText: 'OK'
            });

            // Disable the work interface
            disableWorkInterface(`Sunday Leave. Work will resume on ${tomorrowFormatted}.`);
            // Reset the submission flag before returning
            isSubmissionInProgress = false;
            return;
        }

        // Check if today is a system-wide leave day
        console.log('DEBUG: handleSubmit() - Checking for system-wide leave...');
        clearLeaveCache(); // Clear cache to get fresh data
        const isSystemLeaveToday = await isLeaveDay();
        console.log('DEBUG: handleSubmit() - isLeaveDay() result:', isSystemLeaveToday);

        if (isSystemLeaveToday) {
            console.log('Cannot submit videos - today is a system-wide leave day');
            hideProcessingOverlay();

            Swal.fire({
                icon: 'warning',
                title: 'Submission Not Allowed',
                text: 'Video submissions are not allowed on leave days.',
                confirmButtonText: 'OK'
            });

            // Re-enable submit button and reset flag
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Submit';
                submitButton.classList.remove('submitting');
            }
            isSubmissionInProgress = false;
            return;
        }

        // Check if the current user is on individual leave today
        const user = auth.currentUser;
        if (user) {
            const userLeaveStatus = await checkUserLeaveStatus(user.uid);
            if (userLeaveStatus.onLeave) {
                console.log('Cannot submit videos - user is on personal leave today');
                hideProcessingOverlay();

                Swal.fire({
                    icon: 'warning',
                    title: 'Submission Not Allowed',
                    text: `You are on personal leave today (${userLeaveStatus.reason}). Video submissions are not allowed.`,
                    confirmButtonText: 'OK'
                });

                // Re-enable submit button and reset flag
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = 'Submit';
                    submitButton.classList.remove('submitting');
                }
                isSubmissionInProgress = false;
                return;
            }
        }

        // Check if user is online
        if (!isUserOnline()) {
            updateProcessingStep('step-validation', 'error', 'You are offline');
            hideProcessingOverlay();

            Swal.fire({
                icon: 'warning',
                title: 'You are offline',
                text: 'You need to be online to submit your videos. Please check your internet connection and try again.',
                confirmButtonText: 'OK'
            });

            // Re-enable the submit button since we're returning early
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Submit';
                submitButton.classList.remove('submitting');
            }
            // Reset the submission flag before returning
            isSubmissionInProgress = false;
            return;
        }

        // Check if user has watched all 100 videos
        if (todayVideoCount < 100) {
            updateProcessingStep('step-validation', 'error', `Only ${todayVideoCount}/100 videos completed`);
            hideProcessingOverlay();

            Swal.fire({
                icon: 'warning',
                title: 'Not Enough Videos',
                text: `You have only watched ${todayVideoCount} out of 100 videos. Please complete all videos before submitting.`,
                confirmButtonText: 'OK'
            });

            // Re-enable the submit button since we're returning early
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Submit';
                submitButton.classList.remove('submitting');
            }
            // Reset the submission flag before returning
            isSubmissionInProgress = false;
            return;
        }

        // First check if videos have already been submitted today
        try {
            // Check Firebase first via the checkDailyVideoLimit function
            const limitStatus = await checkDailyVideoLimit();
            console.log('Checking if videos already submitted before proceeding:', limitStatus);

            if (limitStatus.alreadySubmitted) {
                console.log('Videos already submitted today, preventing duplicate submission');

                // Reset the submit button state since we're showing an info message
                if (submitButton) {
                    submitButton.disabled = true; // Keep it disabled
                    submitButton.textContent = 'Submitted';
                    submitButton.classList.remove('submitting');
                    submitButton.classList.add('submitted');
                }

                Swal.fire({
                    icon: 'info',
                    title: 'Already Submitted',
                    text: 'You have already submitted your videos for today. Please come back tomorrow.',
                    confirmButtonText: 'OK'
                });

                // Reset the UI to show videos submitted state
                todayVideoCount = 0;
                updateVideoCountUI();

                // Disable all interactive elements
                if (videoElement) videoElement.disabled = true;
                if (startVideoBtn) {
                    startVideoBtn.disabled = true;
                    startVideoBtn.classList.add('disabled-btn');
                    startVideoBtn.classList.add('hidden');
                }
                if (nextVideoBtn) {
                    nextVideoBtn.disabled = true;
                    nextVideoBtn.classList.add('hidden');
                }
                if (submitButton) submitButton.disabled = true;

                // Show a message in the video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    // Calculate next reset time (midnight tonight)
                    const now = new Date();
                    const nextResetTime = new Date(now);
                    nextResetTime.setHours(24, 0, 0, 0);

                    videoContainer.innerHTML = `
                        <div class="limit-reached-message">
                            <i class="fas fa-check-circle"></i>
                            <h3>Videos Submitted</h3>
                            <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                            <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                        </div>
                    `;
                }

                // Reset the submission flag before returning
                isSubmissionInProgress = false;
                return;
            }
        } catch (checkError) {
            console.error('Error checking if videos were already submitted:', checkError);
            // Continue with submission attempt
        }

        // Show loading message
        Swal.fire({
            title: 'Submitting...',
            text: 'Please wait while we process your submission.',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Complete validation step
        updateProcessingStep('step-validation', 'completed', 'Validation successful');

        // First check if videos have already been submitted (double-check)
        try {
            // Check if videos have already been submitted today
            const checkResult = await checkDailyVideoLimit();
            if (checkResult.alreadySubmitted) {
                console.log('Videos already submitted today (double-check), preventing duplicate submission');

                updateProcessingStep('step-validation', 'error', 'Already submitted today');
                hideProcessingOverlay();

                // Reset the submit button state
                if (submitButton) {
                    submitButton.disabled = true; // Keep it disabled
                    submitButton.textContent = 'Submitted';
                    submitButton.classList.remove('submitting');
                    submitButton.classList.add('submitted');
                }

                Swal.fire({
                    icon: 'info',
                    title: 'Already Submitted',
                    text: 'You have already submitted your videos for today. Please come back tomorrow.',
                    confirmButtonText: 'OK'
                });

                // Update UI to show videos submitted state
                updateSubmittedUI();
                // Reset the submission flag before returning
                isSubmissionInProgress = false;
                return;
            }
        } catch (checkError) {
            console.error('Error during double-check of submission status:', checkError);
            // Continue with submission attempt
        }

        // Get user's plan to determine earnings rate
        let earningsAmount = 10; // Default to Trial plan amount (₹10 for 100 videos)

        try {
            // Get current user
            const user = auth.currentUser;
            if (!user) {
                console.error('No authenticated user found for earnings calculation');
                throw new Error('No authenticated user found');
            }

            console.log('Getting plan information for user:', user.uid);

            // STEP 1: Get user data from multiple sources to ensure we have the correct plan
            // We'll try multiple approaches to get the most accurate plan information

            // First, try to get the plan directly from Firebase (most reliable source)
            let firebaseUserData = null;
            let cachedUserData = null;
            let planExpirationData = null;

            try {
                // Get fresh data directly from Firebase
                const userRef = doc(db, 'users', user.uid);
                const userSnap = await getDoc(userRef);
                if (userSnap.exists()) {
                    firebaseUserData = userSnap.data();
                    console.log('Successfully retrieved user data from Firebase');
                } else {
                    console.warn('User document not found in Firebase');
                }
            } catch (firebaseError) {
                console.error('Error getting user data from Firebase:', firebaseError);
            }

            // Also try to get cached data as a backup
            try {
                cachedUserData = await loadUserDataWithCache();
                console.log('Successfully retrieved cached user data');
            } catch (cacheError) {
                console.error('Error getting cached user data:', cacheError);
            }

            // Also get plan information from checkPlanExpiration
            try {
                planExpirationData = await checkPlanExpiration();
                console.log('Successfully retrieved plan expiration data:', planExpirationData);
            } catch (planError) {
                console.error('Error getting plan expiration data:', planError);
            }

            // STEP 2: Determine the most accurate plan information
            // Priority: Firebase > Plan Expiration > Cache > Default

            // Initialize plan variables
            let planName = 'Trial';
            let planRate = 0.2; // Default Trial plan rate
            let planSource = 'default';

            // Log all available plan data for debugging
            console.log('Plan data sources:', {
                firebase: firebaseUserData?.plan,
                cache: cachedUserData?.plan,
                planExpiration: planExpirationData?.planType
            });

            // First try Firebase data (most reliable)
            if (firebaseUserData && firebaseUserData.plan) {
                const fbPlan = firebaseUserData.plan;

                // Handle different plan data structures
                if (typeof fbPlan === 'string') {
                    planName = fbPlan;
                    planSource = 'firebase-string';
                    console.log('Using plan name from Firebase (string):', planName);
                } else if (typeof fbPlan === 'object') {
                    planName = fbPlan.name || 'Trial';

                    // Try to get rate from Firebase
                    const fbRate = parseFloat(fbPlan.rate);
                    if (!isNaN(fbRate) && fbRate > 0) {
                        planRate = fbRate;
                        planSource = 'firebase-object-rate';
                        console.log('Using plan rate from Firebase:', planRate);
                    }

                    planSource = 'firebase-object';
                    console.log('Using plan data from Firebase object:', { name: planName, rate: planRate });
                }
            }
            // If no Firebase plan, try plan expiration data
            else if (planExpirationData && planExpirationData.planType) {
                planName = planExpirationData.planType;
                planSource = 'plan-expiration';
                console.log('Using plan name from plan expiration:', planName);
            }
            // If no plan expiration data, try cached data
            else if (cachedUserData && cachedUserData.plan) {
                const cachePlan = cachedUserData.plan;

                // Handle different plan data structures
                if (typeof cachePlan === 'string') {
                    planName = cachePlan;
                    planSource = 'cache-string';
                    console.log('Using plan name from cache (string):', planName);
                } else if (typeof cachePlan === 'object') {
                    planName = cachePlan.name || 'Trial';

                    // Try to get rate from cache
                    const cacheRate = parseFloat(cachePlan.rate);
                    if (!isNaN(cacheRate) && cacheRate > 0) {
                        planRate = cacheRate;
                        planSource = 'cache-object-rate';
                        console.log('Using plan rate from cache:', planRate);
                    }

                    planSource = 'cache-object';
                    console.log('Using plan data from cache object:', { name: planName, rate: planRate });
                }
            }

            // STEP 3: Normalize the plan name and get the standard rate if needed
            const normalizedPlanName = planName.trim();
            const planKey = normalizedPlanName.toLowerCase();

            console.log('Normalized plan information:', {
                name: normalizedPlanName,
                key: planKey,
                rate: planRate,
                source: planSource
            });

            // If we don't have a valid rate yet, get it from the standard plans
            if (planRate <= 0 || isNaN(planRate)) {
                console.log('No valid plan rate found, checking standard plans');

                // Check if we have this plan in our standard plans
                if (plans[planKey]) {
                    planRate = plans[planKey].rate;
                    planSource += '+standard-rate';
                    console.log(`Found standard rate for ${normalizedPlanName}: ${planRate}`);
                } else {
                    // Try to match the plan name with standard plans
                    if (planKey.includes('trial')) {
                        planRate = plans['trial'].rate;
                        planSource += '+matched-trial';
                    } else if (planKey.includes('starter')) {
                        planRate = plans['starter'].rate;
                        planSource += '+matched-starter';
                    } else if (planKey.includes('premium')) {
                        planRate = plans['premium'].rate;
                        planSource += '+matched-premium';
                    } else if (planKey.includes('elite')) {
                        planRate = plans['elite'].rate;
                        planSource += '+matched-elite';
                    } else if (planKey.includes('ultimate')) {
                        planRate = plans['ultimate'].rate;
                        planSource += '+matched-ultimate';
                    } else {
                        // Default to Trial plan rate if plan not found
                        planRate = plans['trial'].rate;
                        planSource += '+default-trial';
                        console.log(`Plan ${normalizedPlanName} not found in standard plans, using Trial rate: ${planRate}`);
                    }
                }
            }

            // STEP 4: Calculate earnings based on validated plan rate and correct video count
            const videoCount = 100; // All plans now have 100 videos
            earningsAmount = videoCount * planRate;

            // Verify the earnings amount is reasonable
            if (earningsAmount <= 0 || isNaN(earningsAmount)) {
                console.error('Invalid earnings amount calculated:', earningsAmount);
                // Fall back to default Trial plan amount
                earningsAmount = 10;
                planSource += '+fallback-amount';
            }

            console.log(`Final plan determination: ${normalizedPlanName} (${planSource}) with rate ${planRate}`);
            console.log(`Calculated earnings amount: ₹${earningsAmount} for ${videoCount} videos`);

            // STEP 5: Update the user's plan in Firebase if it's inconsistent
            // This ensures future calculations will be correct
            try {
                // Only update if we have reliable plan information and it differs from what's in Firebase
                if ((planSource.includes('firebase') || planSource.includes('plan-expiration')) &&
                    firebaseUserData &&
                    (!firebaseUserData.plan ||
                     typeof firebaseUserData.plan === 'string' ||
                     !firebaseUserData.plan.rate ||
                     parseFloat(firebaseUserData.plan.rate) !== planRate)) {

                    console.log('Updating user plan in Firebase to ensure consistency');

                    const userRef = doc(db, 'users', user.uid);
                    await updateDoc(userRef, {
                        'plan': {
                            name: normalizedPlanName,
                            rate: planRate,
                            dailyLimit: 50
                        }
                    });

                    console.log('Successfully updated user plan in Firebase');
                }
            } catch (updateError) {
                console.error('Error updating user plan in Firebase:', updateError);
                // Continue with the earnings calculation even if the update fails
            }
        } catch (error) {
            console.error('Error getting user plan for earnings calculation:', error);
            console.error('Error details:', {
                userId: auth.currentUser?.uid,
                errorMessage: error.message || 'Unknown error',
                errorStack: error.stack
            });
            // Continue with default Trial plan earnings amount
            earningsAmount = 10; // ₹0.1 per video * 100 videos
            console.log('Using default Trial plan rate due to error: ₹10');
        }

        // Update wallet with earnings based on plan rate
        console.log(`Calling updateWalletWithEarnings with amount: ₹${earningsAmount}`);
        let walletResult;
        let walletUpdateAttempted = false;
        let walletUpdateSuccessful = false;

        // Update the loading message to show we're processing the wallet update
        Swal.update({
            title: 'Processing Earnings...',
            text: `Adding ₹${earningsAmount.toFixed(2)} to your wallet. Please wait...`,
        });
        Swal.showLoading();

        // First, verify the current wallet state and store it for reference
        let initialWalletState = null;
        try {
            const user = auth.currentUser;
            if (user) {
                const userRef = doc(db, 'users', user.uid);
                const userSnap = await getDoc(userRef);

                if (userSnap.exists()) {
                    const userData = userSnap.data();
                    const currentWallet = userData.wallet || {};
                    const currentEarning = parseFloat(currentWallet.earning) || 0;
                    const currentBalance = parseFloat(currentWallet.balance) || 0;
                    const currentBonus = parseFloat(currentWallet.bonus) || 0;

                    // Store initial wallet state for verification and recovery
                    initialWalletState = {
                        earning: currentEarning,
                        balance: currentBalance,
                        bonus: currentBonus,
                        timestamp: new Date().toISOString()
                    };

                    console.log('Current wallet state before update:', initialWalletState);

                    // Ensure wallet object exists
                    if (!userData.wallet) {
                        console.log('Wallet object does not exist, creating it first');
                        try {
                            await updateDoc(userRef, {
                                'wallet': {
                                    earning: 0,
                                    balance: 0,
                                    bonus: 0
                                },
                                'walletCreatedAt': serverTimestamp()
                            });
                            console.log('Created wallet object');

                            // Update initial wallet state
                            initialWalletState = {
                                earning: 0,
                                balance: 0,
                                bonus: 0,
                                timestamp: new Date().toISOString(),
                                walletCreated: true
                            };
                        } catch (createError) {
                            console.error('Error creating wallet object:', createError);
                            // Continue with wallet update anyway
                        }
                    }

                    // Store initial wallet state in localStorage for recovery if needed
                    try {
                        localStorage.setItem('initial_wallet_state', JSON.stringify(initialWalletState));
                    } catch (storageError) {
                        console.warn('Could not store initial wallet state in localStorage:', storageError);
                    }
                }
            }
        } catch (walletCheckError) {
            console.error('Error checking wallet state:', walletCheckError);
            // Continue with wallet update anyway
        }

        // Start earnings processing step
        updateProcessingStep('step-earnings', 'processing', `Adding ₹${earningsAmount} to your wallet...`);

        // Now try to update the wallet with enhanced error handling
        try {
            walletUpdateAttempted = true;
            console.log('Attempting wallet update with enhanced error handling...');

            // Call the improved updateWalletWithEarnings function with retry support
            walletResult = await updateWalletWithEarnings(earningsAmount);
            console.log('Wallet update result:', walletResult);

            // Check if the update was successful
            if (walletResult.success) {
                walletUpdateSuccessful = true;
                console.log('Wallet update successful according to result');

                // Update earnings step as completed
                updateProcessingStep('step-earnings', 'completed', `₹${earningsAmount} added successfully`);

                // Double-check that the wallet was actually updated by getting fresh data
                try {
                    const user = auth.currentUser;
                    if (user) {
                        const userRef = doc(db, 'users', user.uid);
                        const userSnap = await getDoc(userRef);

                        if (userSnap.exists()) {
                            const userData = userSnap.data();
                            const updatedWallet = userData.wallet || {};
                            const updatedEarning = parseFloat(updatedWallet.earning) || 0;

                            // Calculate expected amount
                            const expectedAmount = initialWalletState ?
                                (initialWalletState.earning + earningsAmount) :
                                earningsAmount;

                            console.log('Wallet state after update:', {
                                earning: updatedEarning,
                                expected: expectedAmount,
                                difference: Math.abs(updatedEarning - expectedAmount)
                            });

                            // If the wallet wasn't updated correctly despite success result
                            if (Math.abs(updatedEarning - expectedAmount) > 0.01) {
                                console.warn('Wallet update verification failed despite success result');
                                walletUpdateSuccessful = false;

                                // Store the failed update for recovery
                                try {
                                    const failedUpdate = {
                                        timestamp: new Date().toISOString(),
                                        userId: user.uid,
                                        amount: earningsAmount,
                                        previousAmount: initialWalletState ? initialWalletState.earning : 0,
                                        actualAmount: updatedEarning,
                                        expectedAmount: expectedAmount,
                                        reason: 'verification_failed_after_success'
                                    };

                                    // Get existing failed updates
                                    let failedUpdates = [];
                                    const storedFailures = localStorage.getItem('failed_wallet_updates');
                                    if (storedFailures) {
                                        try {
                                            failedUpdates = JSON.parse(storedFailures);
                                            if (!Array.isArray(failedUpdates)) failedUpdates = [];
                                        } catch (parseError) {
                                            failedUpdates = [];
                                        }
                                    }

                                    // Add this failure and store
                                    failedUpdates.push(failedUpdate);
                                    localStorage.setItem('failed_wallet_updates', JSON.stringify(failedUpdates));
                                    console.log('Stored failed wallet update for recovery:', failedUpdate);
                                } catch (storageError) {
                                    console.warn('Could not store failed wallet update in localStorage:', storageError);
                                }
                            }
                        }
                    }
                } catch (verifyError) {
                    console.error('Error verifying wallet update:', verifyError);
                    // Continue with the process, but mark as potentially unsuccessful
                    walletUpdateSuccessful = false;
                }
            } else {
                console.warn('Wallet update failed according to result:', walletResult.message);
                walletUpdateSuccessful = false;
            }
        } catch (walletError) {
            console.error('Unexpected error in updateWalletWithEarnings:', walletError);
            console.error('Error details:', {
                earningsAmount,
                userId: auth.currentUser?.uid,
                errorMessage: walletError.message || 'Unknown error',
                errorStack: walletError.stack
            });

            updateProcessingStep('step-earnings', 'error', 'Unexpected error updating wallet');
            walletUpdateAttempted = true;
            walletUpdateSuccessful = false;

            walletResult = {
                success: false,
                message: `Unexpected error: ${walletError.message || 'Unknown error'}`
            };
        }

        // If wallet update was not successful, store the failed update for recovery
        if (walletUpdateAttempted && !walletUpdateSuccessful) {
            console.log('Wallet update was not successful, storing for recovery');

            try {
                const failedUpdate = {
                    timestamp: new Date().toISOString(),
                    userId: auth.currentUser?.uid,
                    amount: earningsAmount,
                    previousAmount: initialWalletState ? initialWalletState.earning : 0,
                    reason: walletResult ? walletResult.message : 'unknown_error'
                };

                // Get existing failed updates
                let failedUpdates = [];
                const storedFailures = localStorage.getItem('failed_wallet_updates');
                if (storedFailures) {
                    try {
                        failedUpdates = JSON.parse(storedFailures);
                        if (!Array.isArray(failedUpdates)) failedUpdates = [];
                    } catch (parseError) {
                        failedUpdates = [];
                    }
                }

                // Add this failure and store
                failedUpdates.push(failedUpdate);
                localStorage.setItem('failed_wallet_updates', JSON.stringify(failedUpdates));
                console.log('Stored failed wallet update for recovery:', failedUpdate);
            } catch (storageError) {
                console.warn('Could not store failed wallet update in localStorage:', storageError);
            }
        }

        if (walletResult.success) {
            // Update the loading message to show we're marking videos as submitted
            Swal.update({
                title: 'Marking Videos as Submitted...',
                text: 'Finalizing your submission. Please wait...',
            });
            Swal.showLoading();

            // Now that wallet update was successful, mark videos as submitted
            const submitResult = await markVideosAsSubmitted();
            if (!submitResult.success) {
                // If marking videos as submitted fails, show error but don't revert wallet update
                console.error('Failed to mark videos as submitted:', submitResult.message);

                Swal.fire({
                    icon: 'warning',
                    title: 'Partial Success',
                    html: `
                        <p>Your earnings of ₹${earningsAmount.toFixed(2)} have been added to your wallet successfully.</p>
                        <p>However, there was an issue marking your videos as submitted: ${submitResult.message}</p>
                        <p>You may need to refresh the page to see your updated wallet balance.</p>
                    `,
                    confirmButtonText: 'OK'
                });

                // Re-enable the submit button since videos weren't marked as submitted
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = 'Submit';
                    submitButton.classList.remove('submitting');
                }

                return;
            }

            // Update the wallet display
            const walletData = await getWalletDataWithCache();
            updateWalletDisplays(walletData);

            // Reset the video count to 0
            todayVideoCount = 0;

            // Clear all video-related data from local storage
            clearVideoLocalStorage();

            // Update the UI to show 0/100 videos
            updateVideoCountUI();

            // IMPORTANT: Do NOT reset todayVideos to 0 after submission
            // This was causing the issue where users could submit multiple times
            // The todayVideos count should remain at 100 to indicate completion
            console.log('Keeping todayVideos count at 100 to indicate submission completed');

            // Get updated total videos count from Firebase
            try {
                const userData = await loadUserDataWithCache();
                if (userData && totalVideosElement) {
                    // Use both stats.totalVideosCompleted (primary) and videosCount (legacy)
                    const totalVideos = userData.stats?.totalVideosCompleted || userData.videosCount || 0;
                    totalVideosElement.textContent = totalVideos;
                    console.log('Updated total videos count in UI after submission:', totalVideos);
                }
            } catch (error) {
                console.error('Error updating total videos count after submission:', error);
            }

            // Create a transaction record in the transactions collection
            try {
                const now = new Date();
                const currentDate = now.toLocaleDateString('en-US');

                // Get validated plan details for the transaction record
                // We'll use the same comprehensive approach as for earnings calculation
                let planDetails = { name: 'Trial', rate: 0.2 };

                try {
                    // STEP 1: Get plan information from multiple sources
                    let firebaseUserData = null;
                    let cachedUserData = null;
                    let planExpirationData = null;

                    // Get fresh data from Firebase (most reliable)
                    try {
                        const userRef = doc(db, 'users', user.uid);
                        const userSnap = await getDoc(userRef);
                        if (userSnap.exists()) {
                            firebaseUserData = userSnap.data();
                            console.log('Retrieved user data from Firebase for transaction record');
                        }
                    } catch (fbError) {
                        console.error('Error getting Firebase data for transaction record:', fbError);
                    }

                    // Get cached data as backup
                    try {
                        cachedUserData = await loadUserDataWithCache();
                        console.log('Retrieved cached user data for transaction record');
                    } catch (cacheError) {
                        console.error('Error getting cached data for transaction record:', cacheError);
                    }

                    // Get plan expiration data
                    try {
                        planExpirationData = await checkPlanExpiration();
                        console.log('Retrieved plan expiration data for transaction record');
                    } catch (planError) {
                        console.error('Error getting plan expiration data for transaction record:', planError);
                    }

                    // STEP 2: Determine the most accurate plan information
                    // Priority: Firebase > Plan Expiration > Cache > Default

                    // Initialize plan variables
                    let planName = 'Trial';
                    let planRate = 0.2; // Default Trial plan rate

                    // Log all available plan data for debugging
                    console.log('Transaction record - Plan data sources:', {
                        firebase: firebaseUserData?.plan,
                        cache: cachedUserData?.plan,
                        planExpiration: planExpirationData?.planType
                    });

                    // First try Firebase data (most reliable)
                    if (firebaseUserData && firebaseUserData.plan) {
                        const fbPlan = firebaseUserData.plan;

                        // Handle different plan data structures
                        if (typeof fbPlan === 'string') {
                            planName = fbPlan;
                            console.log('Transaction record - Using plan name from Firebase (string):', planName);
                        } else if (typeof fbPlan === 'object') {
                            planName = fbPlan.name || 'Trial';

                            // Try to get rate from Firebase
                            const fbRate = parseFloat(fbPlan.rate);
                            if (!isNaN(fbRate) && fbRate > 0) {
                                planRate = fbRate;
                                console.log('Transaction record - Using plan rate from Firebase:', planRate);
                            }
                        }
                    }
                    // If no Firebase plan, try plan expiration data
                    else if (planExpirationData && planExpirationData.planType) {
                        planName = planExpirationData.planType;
                        console.log('Transaction record - Using plan name from plan expiration:', planName);
                    }
                    // If no plan expiration data, try cached data
                    else if (cachedUserData && cachedUserData.plan) {
                        const cachePlan = cachedUserData.plan;

                        // Handle different plan data structures
                        if (typeof cachePlan === 'string') {
                            planName = cachePlan;
                            console.log('Transaction record - Using plan name from cache (string):', planName);
                        } else if (typeof cachePlan === 'object') {
                            planName = cachePlan.name || 'Trial';

                            // Try to get rate from cache
                            const cacheRate = parseFloat(cachePlan.rate);
                            if (!isNaN(cacheRate) && cacheRate > 0) {
                                planRate = cacheRate;
                                console.log('Transaction record - Using plan rate from cache:', planRate);
                            }
                        }
                    }

                    // STEP 3: Normalize the plan name and get the standard rate if needed
                    const normalizedPlanName = planName.trim();
                    const planKey = normalizedPlanName.toLowerCase();

                    // If we don't have a valid rate yet, get it from the standard plans
                    if (planRate <= 0 || isNaN(planRate)) {
                        console.log('Transaction record - No valid plan rate found, checking standard plans');

                        // Check if we have this plan in our standard plans
                        if (plans[planKey]) {
                            planRate = plans[planKey].rate;
                            console.log(`Transaction record - Found standard rate for ${normalizedPlanName}: ${planRate}`);
                        } else {
                            // Try to match the plan name with standard plans
                            if (planKey.includes('trial')) {
                                planRate = plans['trial'].rate;
                            } else if (planKey.includes('starter')) {
                                planRate = plans['starter'].rate;
                            } else if (planKey.includes('premium')) {
                                planRate = plans['premium'].rate;
                            } else if (planKey.includes('elite')) {
                                planRate = plans['elite'].rate;
                            } else if (planKey.includes('ultimate')) {
                                planRate = plans['ultimate'].rate;
                            } else {
                                // Default to Trial plan rate if plan not found
                                planRate = plans['trial'].rate;
                                console.log(`Transaction record - Plan ${normalizedPlanName} not found in standard plans, using Trial rate: ${planRate}`);
                            }
                        }
                    }

                    // STEP 4: Verify the rate is reasonable
                    if (planRate <= 0 || isNaN(planRate)) {
                        console.error('Transaction record - Invalid plan rate:', planRate);
                        // Fall back to default Trial plan rate
                        planRate = 0.2;
                    }

                    // STEP 5: Create the plan details object for the transaction
                    planDetails = {
                        name: normalizedPlanName,
                        rate: planRate
                    };

                    // Ensure the earnings amount in the transaction matches what was actually added to the wallet
                    // This is critical for accurate transaction records
                    const ratePerVideo = planRate;
                    const videosCount = 100; // All plans now have 100 videos
                    const calculatedAmount = ratePerVideo * videosCount;

                    // If there's a significant difference between the calculated amount and the actual earnings amount,
                    // use the actual earnings amount for the transaction to ensure consistency
                    if (Math.abs(calculatedAmount - earningsAmount) > 0.01) {
                        console.warn('Transaction record - Mismatch between calculated amount and actual earnings amount:', {
                            calculated: calculatedAmount,
                            actual: earningsAmount
                        });

                        // Adjust the rate to match the actual earnings amount
                        const adjustedRate = earningsAmount / videosCount;
                        planDetails.rate = adjustedRate;
                        planDetails.adjustedRate = true;
                        planDetails.originalRate = planRate;

                        console.log('Transaction record - Adjusted rate to match actual earnings:', adjustedRate);
                    }

                    console.log('Transaction record - Using validated plan details:', planDetails);
                } catch (planError) {
                    console.error('Error getting plan details for transaction record:', planError);

                    // Use the earnings amount to determine the plan rate as a fallback
                    const fallbackRate = earningsAmount / 50;
                    planDetails = {
                        name: fallbackRate >= 3 ? 'Premium' : (fallbackRate >= 0.5 ? 'Starter' : 'Trial'),
                        rate: fallbackRate,
                        fallback: true
                    };

                    console.log('Transaction record - Using fallback plan details based on earnings amount:', planDetails);
                }

                // Start transaction creation step
                updateProcessingStep('step-transaction', 'processing', 'Creating transaction record...');

                console.log('Creating transaction record for video earnings with plan details:', planDetails);

                // Check if a transaction for today already exists to prevent duplicates
                let transactionExists = false;
                let existingTransactionId = null;
                try {
                    const user = auth.currentUser;
                    if (user) {
                        // Get today's date in UTC format for consistent comparison
                        const todayUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format

                        console.log('Checking for existing transactions for today:', todayUTC);

                        // Query for earnings transactions from today with broader search
                        const transactionsQuery = query(
                            collection(db, 'transactions'),
                            where('userId', '==', user.uid),
                            where('type', '==', 'earnings'),
                            orderBy('timestamp', 'desc'),
                            limit(10) // Increased limit to catch more transactions
                        );

                        const transactionsSnap = await getDocs(transactionsQuery);
                        console.log(`Found ${transactionsSnap.size} total earnings transactions for user`);

                        // Check if any of the transactions are from today
                        transactionsSnap.forEach(doc => {
                            const data = doc.data();
                            if (data.timestamp) {
                                const transactionDate = data.timestamp.toDate?.();
                                if (transactionDate) {
                                    const transactionDateUTC = transactionDate.toISOString().split('T')[0];
                                    console.log(`Transaction ${doc.id}: date=${transactionDateUTC}, amount=${data.amount}`);

                                    if (transactionDateUTC === todayUTC) {
                                        console.log('Found existing earnings transaction for today:', doc.id, 'amount:', data.amount);
                                        transactionExists = true;
                                        existingTransactionId = doc.id;
                                    }
                                }
                            }
                        });

                        console.log('Transaction existence check result:', {
                            transactionExists,
                            existingTransactionId,
                            todayUTC
                        });
                    }
                } catch (checkError) {
                    console.error('Error checking for existing transactions:', checkError);
                    // Continue with transaction creation
                }

                // Track transaction creation status
                let transactionCreatedInThisSession = false;
                let createdTransactionId = null;

                // Only create a new transaction if one doesn't already exist for today
                if (!transactionExists) {
                    console.log('Creating new transaction for today\'s video submission');
                    console.log('Transaction creation context:', {
                        transactionExists,
                        existingTransactionId,
                        earningsAmount,
                        currentDate
                    });

                    // Get time tracking summary
                    const timeSpentSummary = getTimeSpentSummary();

                    // All plans now have 100 videos
                    const videoCount = 100;

                    // Prepare transaction data with time tracking
                    const transactionData = {
                        type: 'earnings',  // Use 'earnings' to match the filter in transactions.js
                        amount: earningsAmount,
                        status: 'completed',
                        description: `Completed ${videoCount} videos on ${currentDate}`,
                        planDetails: {
                            name: planDetails.name,
                            rate: planDetails.rate,
                            videosCount: videoCount,
                            ratePerVideo: planDetails.rate
                        },
                        timeTracking: {
                            totalTimeSpent: timeSpentSummary.totalTimeSpent,
                            videoWatchingTime: timeSpentSummary.videoWatchingTime,
                            totalTimeFormatted: timeSpentSummary.totalTimeFormatted,
                            videoTimeFormatted: timeSpentSummary.videoTimeFormatted,
                            sessionStartTime: timeSpentSummary.sessionStartTime,
                            averageTimePerVideo: timeSpentSummary.averageTimePerVideo,
                            videosCompleted: todayVideoCount
                        }
                    };

                    // Use ONLY data-service.js addTransaction function to prevent duplicates
                    try {
                        console.log('Creating transaction using data-service.js addTransaction function');
                        console.log('Transaction data:', transactionData);

                        // Import and use the addTransaction function from data-service.js
                        createdTransactionId = await addTransaction(transactionData);

                        if (createdTransactionId && createdTransactionId !== null && createdTransactionId !== undefined) {
                            console.log('Transaction record created successfully, ID:', createdTransactionId);
                            transactionCreatedInThisSession = true;
                            updateProcessingStep('step-transaction', 'completed', 'Transaction record created successfully');

                            // Store the transaction ID to prevent duplicate creation during verification
                            window.sessionTransactionId = createdTransactionId;
                            window.sessionTransactionCreated = true;

                            // Store today's date to prevent duplicate submissions in the same session
                            const today = new Date().toISOString().split('T')[0];
                            localStorage.setItem('last_submission_date', today);
                        } else {
                            console.error('addTransaction returned invalid ID:', createdTransactionId);
                            updateProcessingStep('step-transaction', 'error', 'Failed to create transaction record');
                        }
                    } catch (serviceError) {
                        console.error('Error creating transaction:', serviceError);
                        updateProcessingStep('step-transaction', 'error', 'Failed to create transaction record');
                        // Continue with the process even if transaction creation fails
                    }
                } else {
                    console.log('Skipping transaction creation as one already exists for today');
                    createdTransactionId = existingTransactionId;
                    transactionCreatedInThisSession = true; // Mark as created since we found an existing one
                    updateProcessingStep('step-transaction', 'completed', 'Transaction already exists');

                    // Store the existing transaction ID to prevent duplicate creation during verification
                    window.sessionTransactionId = existingTransactionId;
                    window.sessionTransactionCreated = true;

                    // Store today's date to prevent duplicate submissions in the same session
                    const today = new Date().toISOString().split('T')[0];
                    localStorage.setItem('last_submission_date', today);
                }
            } catch (transactionError) {
                console.error('Error creating transaction record:', transactionError);
                console.error('Transaction error details:', JSON.stringify(transactionError));
                updateProcessingStep('step-transaction', 'error', 'Failed to create transaction record');
                // Continue even if transaction creation fails - the wallet has already been updated
            }

            // Start verification step
            updateProcessingStep('step-verification', 'processing', 'Verifying all operations completed...');

            // Verify that all steps completed successfully
            console.log('Performing final verification of submission process...');
            let verificationResults = {
                walletUpdated: walletUpdateSuccessful, // Use the actual wallet update result
                transactionCreated: transactionCreatedInThisSession || transactionExists,
                submissionMarked: false,
                earningsAdded: walletUpdateSuccessful, // If wallet was updated, earnings were added
                earningsAmount: earningsAmount,
                walletBefore: null,
                walletAfter: null,
                existingTransactionId: createdTransactionId,
                walletUpdateAttempted: walletUpdateAttempted,
                walletUpdateFailed: false,
                emergencyFix: false
            };

            try {
                const user = auth.currentUser;
                if (user) {
                    // Check wallet update
                    const userRef = doc(db, 'users', user.uid);
                    const userSnap = await getDoc(userRef);

                    if (userSnap.exists()) {
                        const userData = userSnap.data();
                        const wallet = userData.wallet || {};
                        const currentEarning = parseFloat(wallet.earning) || 0;

                        verificationResults.walletAfter = {
                            earning: currentEarning,
                            balance: parseFloat(wallet.balance) || 0,
                            bonus: parseFloat(wallet.bonus) || 0
                        };

                        console.log('Verification: Current wallet state:', verificationResults.walletAfter);

                        // Check if wallet exists and has earnings
                        if (wallet && currentEarning > 0) {
                            verificationResults.walletUpdated = true;
                            console.log('Verification: Wallet has been updated successfully');

                            // Try to determine if the earnings were actually added
                            // This is challenging because we don't know the previous wallet state for sure
                            // We'll use a few heuristics to make an educated guess

                            // 1. Check if the current earning is at least the amount we tried to add
                            if (currentEarning >= earningsAmount) {
                                console.log('Verification: Wallet earning is at least the amount we tried to add');
                                verificationResults.earningsAdded = true;
                            }
                            // 2. If we have the previous wallet state, check if the difference matches
                            else if (walletResult && walletResult.previousAmount !== undefined) {
                                const expectedEarning = parseFloat(walletResult.previousAmount) + earningsAmount;
                                const difference = Math.abs(currentEarning - expectedEarning);

                                if (difference < 0.01) {
                                    console.log('Verification: Wallet earning matches expected amount based on previous state');
                                    verificationResults.earningsAdded = true;
                                } else {
                                    console.warn('Verification: Wallet earning does not match expected amount', {
                                        current: currentEarning,
                                        expected: expectedEarning,
                                        difference: difference
                                    });
                                }
                            }
                            // 3. If all else fails, assume it worked if the wallet has any earnings
                            else {
                                console.log('Verification: Cannot determine for sure if earnings were added, but wallet has earnings');
                                verificationResults.earningsAdded = true;
                            }
                        } else {
                            console.warn('Verification: Wallet may not have been updated correctly');

                            // IMPORTANT: Only attempt emergency wallet fix if we didn't already attempt a wallet update
                            // This prevents duplicate earnings from being added to the wallet
                            if (!verificationResults.walletUpdateAttempted) {
                                // If wallet doesn't exist or has no earnings, try to create/update it now
                                try {
                                    console.log('Verification: Attempting to fix wallet as it appears to be missing or empty (no previous wallet update attempted)');
                                    await updateDoc(userRef, {
                                        'wallet.earning': increment(earningsAmount)
                                    });
                                    console.log('Verification: Emergency wallet update completed');

                                    // Check if the update worked
                                    const updatedSnap = await getDoc(userRef);
                                    if (updatedSnap.exists()) {
                                        const updatedData = updatedSnap.data();
                                        const updatedWallet = updatedData.wallet || {};
                                        const updatedEarning = parseFloat(updatedWallet.earning) || 0;

                                        if (updatedEarning > 0) {
                                            console.log('Verification: Emergency wallet update successful, earnings now:', updatedEarning);
                                            verificationResults.walletUpdated = true;
                                            verificationResults.earningsAdded = true;
                                            verificationResults.emergencyFix = true;
                                        }
                                    }
                                } catch (fixError) {
                                    console.error('Verification: Failed to fix wallet:', fixError);
                                }
                            } else {
                                console.log('Verification: Skipping emergency wallet fix - wallet update was already attempted in this session');
                                // If we attempted a wallet update but verification shows it didn't work,
                                // we should NOT try again as this could cause duplicate earnings
                                verificationResults.walletUpdateFailed = true;
                            }
                        }

                        // Check if submission was marked
                        if (userData.stats &&
                            userData.stats.dailyVideos &&
                            userData.stats.dailyVideos.submitted === true) {
                            verificationResults.submissionMarked = true;
                            console.log('Verification: Submission has been marked successfully');
                        } else {
                            console.warn('Verification: Submission may not have been marked correctly');

                            // Try to fix the submission status
                            try {
                                console.log('Verification: Attempting to fix submission status');
                                const now = new Date();
                                const currentDateUTC = now.toISOString().split('T')[0];

                                await updateDoc(userRef, {
                                    'stats.dailyVideos.submitted': true,
                                    'stats.dailyVideos.date': currentDateUTC,
                                    'stats.dailyVideos.count': 50,
                                    'stats.todayVideos': 50
                                });

                                console.log('Verification: Emergency submission status update completed');
                                verificationResults.submissionMarked = true;
                                verificationResults.emergencyFix = true;
                            } catch (fixError) {
                                console.error('Verification: Failed to fix submission status:', fixError);
                            }
                        }
                    }

                    // Check if transaction was created with retry for eventual consistency
                    const now = new Date();
                    const todayUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format

                    let foundTransaction = null;
                    let transactionCheckAttempts = 0;
                    const maxTransactionCheckAttempts = 2;

                    // Retry transaction check to account for Firebase eventual consistency
                    while (!foundTransaction && transactionCheckAttempts < maxTransactionCheckAttempts) {
                        transactionCheckAttempts++;
                        console.log(`Verification: Checking for transaction (attempt ${transactionCheckAttempts}/${maxTransactionCheckAttempts})`);

                        // Add a small delay for subsequent attempts
                        if (transactionCheckAttempts > 1) {
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }

                        const transactionsQuery = query(
                            collection(db, 'transactions'),
                            where('userId', '==', user.uid),
                            where('type', '==', 'earnings'),
                            orderBy('timestamp', 'desc'),
                            limit(10) // Increased limit to catch more recent transactions
                        );

                        const transactionsSnap = await getDocs(transactionsQuery);

                        // Check if any of the transactions are from today
                        transactionsSnap.forEach(doc => {
                            const data = doc.data();
                            if (data.timestamp) {
                                const transactionDate = data.timestamp.toDate?.();
                                if (transactionDate) {
                                    const transactionDateUTC = transactionDate.toISOString().split('T')[0];
                                    if (transactionDateUTC === todayUTC) {
                                        verificationResults.transactionCreated = true;
                                        foundTransaction = {
                                            id: doc.id,
                                            ...data
                                        };
                                        console.log('Verification: Transaction has been created successfully:', foundTransaction.id);
                                    }
                                }
                            }
                        });

                        if (foundTransaction) {
                            console.log(`Verification: Transaction found on attempt ${transactionCheckAttempts}`);
                            break;
                        } else {
                            console.log(`Verification: No transaction found on attempt ${transactionCheckAttempts}`);
                        }
                    }

                    if (!verificationResults.transactionCreated) {
                        console.warn('Verification: Transaction may not have been created correctly');
                        console.log('Verification: Transaction creation debug info:', {
                            transactionCreatedInThisSession,
                            transactionExists,
                            createdTransactionId,
                            existingTransactionId
                        });

                        // IMPORTANT: Do not create duplicate transactions!
                        // Enhanced logic to prevent duplicate transaction creation
                        const shouldSkipTransactionCreation =
                            transactionCreatedInThisSession ||  // We already attempted to create a transaction in this session
                            transactionExists ||                // An existing transaction was found
                            createdTransactionId ||             // We have a transaction ID from this session
                            existingTransactionId ||            // We have an existing transaction ID
                            window.sessionTransactionCreated || // Session flag indicates transaction was created
                            window.sessionTransactionId;       // Session has a transaction ID

                        if (!shouldSkipTransactionCreation) {
                            console.log('Verification: No transaction creation attempted - this should not happen in normal flow');
                            console.log('Verification: Transaction creation should have been handled in the main submission process');
                        } else {
                            console.log('Verification: Transaction creation was already handled - skipping duplicate creation');
                            console.log('Verification: Skip reasons:', {
                                transactionCreatedInThisSession: transactionCreatedInThisSession ? 'YES' : 'NO',
                                transactionExists: transactionExists ? 'YES' : 'NO',
                                createdTransactionId: createdTransactionId ? 'YES' : 'NO',
                                existingTransactionId: existingTransactionId ? 'YES' : 'NO',
                                sessionTransactionCreated: window.sessionTransactionCreated ? 'YES' : 'NO',
                                sessionTransactionId: window.sessionTransactionId ? 'YES' : 'NO'
                            });
                            // Mark as created since we know a transaction was attempted/exists
                            verificationResults.transactionCreated = true;
                        }
                    } else if (foundTransaction) {
                        // Check if the transaction amount matches the earnings amount
                        const transactionAmount = parseFloat(foundTransaction.amount) || 0;
                        if (Math.abs(transactionAmount - earningsAmount) > 0.01) {
                            console.warn('Verification: Transaction amount does not match earnings amount', {
                                transaction: transactionAmount,
                                earnings: earningsAmount
                            });

                            // We won't try to fix this as it could cause duplicate transactions
                            verificationResults.transactionAmountMismatch = true;
                        }
                    }

                    // Final verification summary
                    console.log('Verification complete. Results:', verificationResults);
                }
            } catch (verifyError) {
                console.error('Error during final verification:', verifyError);
                // Continue with success message anyway
            }

            // Complete verification step
            updateProcessingStep('step-verification', 'completed', 'Verification completed successfully');

            // Show success message with plan-based earnings
            // Get validated plan name for the message
            let planName = 'Trial';
            try {
                const userData = await loadUserDataWithCache();
                if (userData && userData.plan) {
                    planName = userData.plan.name || 'Trial';
                }
            } catch (error) {
                console.error('Error getting plan name for success message:', error);
            }

            // Calculate rate per video
            const ratePerVideo = (earningsAmount / 50).toFixed(2);

            // Create success message with verification results
            let verificationMessage = '';

            // Check for any verification issues
            const hasVerificationIssues = !verificationResults.walletUpdated ||
                                         !verificationResults.transactionCreated ||
                                         !verificationResults.submissionMarked ||
                                         !verificationResults.earningsAdded ||
                                         verificationResults.transactionAmountMismatch;

            // If there were any issues, show a warning message
            if (hasVerificationIssues) {
                // Create a detailed message based on the specific issues
                let issueDetails = '';

                if (!verificationResults.walletUpdated) {
                    issueDetails += '<p>• Your wallet may not have been updated correctly.</p>';
                }

                if (!verificationResults.earningsAdded) {
                    issueDetails += '<p>• Your earnings may not have been added to your wallet.</p>';
                }

                if (!verificationResults.transactionCreated) {
                    issueDetails += '<p>• The transaction record may not have been created.</p>';
                }

                if (verificationResults.transactionAmountMismatch) {
                    issueDetails += '<p>• The transaction amount may not match your earnings.</p>';
                }

                if (!verificationResults.submissionMarked) {
                    issueDetails += '<p>• Your submission may not have been marked correctly.</p>';
                }

                // Add information about any emergency fixes
                if (verificationResults.emergencyFix) {
                    issueDetails += '<p>• Some issues were automatically fixed during verification.</p>';
                }

                verificationMessage = `
                    <div style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border-radius: 5px; color: #856404;">
                        <p><strong>Note:</strong> Some parts of the submission process may not have completed correctly:</p>
                        ${issueDetails}
                        <p>Please check your wallet and transactions page to verify your earnings were added.</p>
                        <p>If you don't see your earnings, please contact support.</p>
                    </div>
                `;
            }
            // If everything was successful but emergency fixes were applied, show an info message
            else if (verificationResults.emergencyFix) {
                verificationMessage = `
                    <div style="margin-top: 15px; padding: 10px; background-color: #d4edda; border-radius: 5px; color: #155724;">
                        <p><strong>Note:</strong> Some minor issues were automatically fixed during verification.</p>
                        <p>Your earnings have been added successfully.</p>
                    </div>
                `;
            }

            // Wait a moment to show the completed verification, then hide overlay and show success
            setTimeout(() => {
                hideProcessingOverlay();

                // Get final time tracking summary for display
                const finalTimeSpentSummary = getTimeSpentSummary();
                const videoCount = 100; // All plans now have 100 videos

                Swal.fire({
                    icon: 'success',
                    title: 'Submission Successful!',
                    html: `
                        <p>You have successfully submitted all ${videoCount} videos for today.</p>
                        <p><strong>Plan: ${planName}</strong></p>
                        <p><strong>Rate: ₹${ratePerVideo} per video</strong></p>
                        <p><strong>Total: ₹${earningsAmount.toFixed(2)}</strong> has been added to your earning wallet.</p>
                        <hr style="margin: 15px 0;">
                        <p><strong>Time Tracking Summary:</strong></p>
                        <p>📊 Total Time Spent: <strong>${finalTimeSpentSummary.totalTimeFormatted}</strong></p>
                        <p>🎥 Video Watching Time: <strong>${finalTimeSpentSummary.videoTimeFormatted}</strong></p>
                        <p>⏱️ Average Time per Video: <strong>${formatTimeSpent(finalTimeSpentSummary.averageTimePerVideo)}</strong></p>
                        <hr style="margin: 15px 0;">
                        <p>Your wallet has been updated and a transaction record has been created.</p>
                        <p>Your total videos count has been updated.</p>
                        <p>Come back tomorrow for more videos!</p>
                        ${verificationMessage}
                    `,
                    confirmButtonText: 'OK'
                });
            }, 1500); // Show completed steps for 1.5 seconds before showing success

            // Disable all interactive elements
            videoElement.disabled = true;
            nextVideoBtn.disabled = true;
            submitButton.disabled = true;

            // Also disable the start button
            if (startVideoBtn) {
                startVideoBtn.disabled = true;
                startVideoBtn.classList.add('disabled-btn');
                startVideoBtn.classList.add('hidden');
            }

            // Show a message in the video container
            const videoContainer = document.querySelector('.video-container');
            if (videoContainer) {
                // Calculate next reset time (midnight tonight)
                const now = new Date();
                const nextResetTime = new Date(now);
                nextResetTime.setHours(24, 0, 0, 0);

                videoContainer.innerHTML = `
                    <div class="limit-reached-message">
                        <i class="fas fa-check-circle"></i>
                        <h3>Videos Submitted</h3>
                        <p>You have already submitted your videos for today. Please come back tomorrow.</p>
                        <p>Next reset: ${formatNextResetTime(nextResetTime)}</p>
                    </div>
                `;
            }

            // Log final debug information after submission
            console.log('Submission process completed. Final state:');
            await debugSubmissionState();
        } else {
            // Update earnings step as failed
            updateProcessingStep('step-earnings', 'error', 'Failed to add earnings to wallet');
            hideProcessingOverlay();

            // Show error message and allow user to try again
            Swal.fire({
                icon: 'error',
                title: 'Wallet Update Failed',
                html: `
                    <p>There was an issue adding ₹${earningsAmount.toFixed(2)} to your wallet: ${walletResult.message}</p>
                    <p>Your videos have NOT been marked as submitted, so you can try again.</p>
                    <p>If this issue persists, please contact support.</p>
                `,
                confirmButtonText: 'Try Again'
            });

            // Re-enable the submit button to allow the user to try again
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Submit';
                submitButton.classList.remove('submitting');
            }

            // Reset the submission flag to allow retry
            isSubmissionInProgress = false;

            // Store the failed attempt in localStorage for recovery
            try {
                const failedSubmission = {
                    timestamp: new Date().toISOString(),
                    userId: auth.currentUser?.uid,
                    amount: earningsAmount,
                    error: walletResult.message || 'Unknown error',
                    planRate: planRate,
                    planName: normalizedPlanName
                };

                // Get existing failed submissions
                let failedSubmissions = [];
                const storedFailures = localStorage.getItem('failed_video_submissions');
                if (storedFailures) {
                    try {
                        failedSubmissions = JSON.parse(storedFailures);
                        if (!Array.isArray(failedSubmissions)) failedSubmissions = [];
                    } catch (parseError) {
                        failedSubmissions = [];
                    }
                }

                // Add this failure and store
                failedSubmissions.push(failedSubmission);
                localStorage.setItem('failed_video_submissions', JSON.stringify(failedSubmissions));
                console.log('Stored failed video submission for recovery:', failedSubmission);
            } catch (storageError) {
                console.warn('Could not store failed video submission in localStorage:', storageError);
            }

            // Log final debug information after failed submission
            console.log('Submission process failed. Final state:');
            await debugSubmissionState();
            return;
        }
    } catch (error) {
        console.error('Error handling submit:', error);

        // Hide processing overlay on error
        hideProcessingOverlay();

        // Re-enable the submit button in case of error
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = 'Submit';
            submitButton.classList.remove('submitting');
        }

        // Reset the submission flag to allow retry
        isSubmissionInProgress = false;

        Swal.fire({
            icon: 'error',
            title: 'Submission Error',
            text: 'An unexpected error occurred. Please try again later.',
            confirmButtonText: 'OK'
        });
    } finally {
        // Always reset the submission flag when the function completes
        isSubmissionInProgress = false;
    }
}

// Function to format next reset time
function formatNextResetTime(nextResetTime) {
    if (!nextResetTime) return 'Midnight tonight';

    try {
        // Format hours and minutes with leading zeros
        const hours = nextResetTime.getHours().toString().padStart(2, '0');
        const minutes = nextResetTime.getMinutes().toString().padStart(2, '0');

        return `${hours}:${minutes} tonight`;
    } catch (error) {
        console.error('Error formatting next reset time:', error);
        return 'Midnight tonight';
    }
}

// Check if the user has reached their daily video limit
async function checkDailyLimit() {
    try {
        const limitStatus = await checkDailyVideoLimit();
        console.log('Daily limit status:', limitStatus);

        if (limitStatus.limitReached) {
            // User has reached their daily limit
            console.log('Daily limit reached:', limitStatus.message);

            // Check if videos were already submitted today
            const alreadySubmitted = limitStatus.alreadySubmitted || false;

            // If videos were already submitted, ensure localStorage is consistent
            if (alreadySubmitted) {
                const now = new Date();
                const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                localStorage.setItem('instra_videos_submitted', 'true');
                localStorage.setItem('instra_videos_submitted_date', currentDateUTC);
                console.log('Updated localStorage to reflect videos already submitted status');

                // Also ensure Firebase has the correct flags set
                try {
                    const user = auth.currentUser;
                    if (user) {
                        const userRef = doc(db, 'users', user.uid);
                        await updateDoc(userRef, {
                            'stats.todayVideos': 50,
                            'stats.todayTranslations': 50,
                            'stats.dailyVideos.submitted': true,
                            'stats.dailyTranslations.submitted': true,
                            'stats.dailyVideos.count': 50,
                            'stats.dailyTranslations.count': 50,
                            'stats.lastSubmission.submitted': true
                        });
                        console.log('Updated Firebase to ensure submission flags are consistent');
                    }
                } catch (updateError) {
                    console.error('Error updating Firebase submission flags:', updateError);
                }
            }

            // Show a message to the user
            Swal.fire({
                icon: 'info',
                title: alreadySubmitted ? 'Videos Submitted' : 'Daily Limit Reached',
                text: limitStatus.message,
                confirmButtonText: 'OK'
            });

            // Disable the video player and buttons
            if (videoElement) videoElement.disabled = true;
            if (startVideoBtn) startVideoBtn.disabled = true;
            if (nextVideoBtn) nextVideoBtn.disabled = true;
            if (submitButton) submitButton.disabled = true;

            // Show a message in the video container
            const videoContainer = document.querySelector('.video-container');
            if (videoContainer) {
                videoContainer.innerHTML = `
                    <div class="limit-reached-message">
                        <i class="fas fa-check-circle"></i>
                        <h3>${alreadySubmitted ? 'Videos Submitted' : 'Daily Limit Reached'}</h3>
                        <p>${limitStatus.message}</p>
                        <p>Next reset: ${formatNextResetTime(limitStatus.nextResetTime)}</p>
                    </div>
                `;
            }

            // If videos were already submitted, set the UI count to 0/50 but keep Firebase at 50
            if (alreadySubmitted) {
                // For UI display, set to 0
                todayVideoCount = 0;
                updateVideoCountUI();

                // Check if it's a new day - only reset if it's a new day
                try {
                    const user = auth.currentUser;
                    if (user) {
                        const userRef = doc(db, 'users', user.uid);

                        // Get the current date in both formats for consistency
                        const now = new Date();
                        const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                        const currentDateLocal = now.toLocaleDateString('en-US');

                        // Get the submission date from localStorage
                        const submissionDateUTC = localStorage.getItem('instra_videos_submitted_date');

                        console.log('Date check in checkDailyLimit:', {
                            currentDateUTC,
                            currentDateLocal,
                            submissionDateUTC,
                            isNewDay: submissionDateUTC !== currentDateUTC
                        });

                        // Only reset if it's a new day compared to the submission date
                        if (submissionDateUTC && submissionDateUTC !== currentDateUTC) {
                            console.log('New day detected, resetting submission status');

                            // Create a new video data object for the new day
                            const newVideoData = {
                                date: currentDateLocal,
                                count: 0,
                                lastUpdated: serverTimestamp(),
                                submitted: false // Reset the submitted flag for the new day
                            };

                            // Comprehensive update to reset all relevant fields
                            await updateDoc(userRef, {
                                // Reset today's video counts
                                'stats.todayVideos': 0,
                                'stats.todayTranslations': 0,

                                // Reset daily videos tracking
                                'stats.dailyVideos': newVideoData,
                                'stats.dailyVideos.count': 0,
                                'stats.dailyVideos.submitted': false,

                                // Reset daily translations tracking (legacy)
                                'stats.dailyTranslations': newVideoData,
                                'stats.dailyTranslations.count': 0,
                                'stats.dailyTranslations.submitted': false
                            });

                            console.log('Reset all video count fields in Firebase to 0 for new day in checkDailyLimit');

                            // Also reset localStorage
                            localStorage.setItem('instra_video_count', '0');
                            localStorage.setItem('instra_daily_video_date', currentDateLocal);
                            localStorage.removeItem('instra_videos_submitted');
                            localStorage.removeItem('instra_videos_submitted_date');
                            console.log('Reset all video count fields in localStorage for new day');
                        } else {
                            console.log('Same day submission, keeping submitted status');

                            // Ensure Firebase has the correct submitted status
                            getDoc(userRef).then(doc => {
                                if (doc.exists()) {
                                    const data = doc.data();
                                    const stats = data.stats || {};

                                    // If the submitted flag isn't set but we have a local flag, update Firebase
                                    if (stats.dailyVideos && !stats.dailyVideos.submitted) {
                                        console.log('Found inconsistency: local submitted flag is true but Firebase flag is false');

                                        updateDoc(userRef, {
                                            'stats.todayVideos': 50,
                                            'stats.todayTranslations': 50,
                                            'stats.dailyVideos.submitted': true,
                                            'stats.dailyVideos.date': currentDateUTC,
                                            'stats.dailyTranslations.submitted': true,
                                            'stats.dailyTranslations.date': currentDateUTC,
                                            'stats.lastSubmission': {
                                                date: currentDateUTC,
                                                timestamp: serverTimestamp(),
                                                count: 50,
                                                submitted: true
                                            }
                                        }).then(() => {
                                            console.log('Updated Firebase with submitted count of 50 and set submitted flag');
                                        }).catch(error => {
                                            console.error('Error updating Firebase with submitted count and flag:', error);
                                        });
                                    }
                                }
                            }).catch(error => {
                                console.error('Error checking submission flags in Firebase:', error);
                            });
                        }
                    }
                } catch (updateError) {
                    console.error('Error handling submission status in checkDailyLimit:', updateError);
                }
            }

            return true;
        } else {
            // User has not reached their daily limit
            console.log('Daily limit not reached:', limitStatus.message);

            // Update the video count
            todayVideoCount = limitStatus.currentCount || 0;
            updateVideoCountUI();

            return false;
        }
    } catch (error) {
        console.error('Error checking daily limit:', error);
        return false;
    }
}

// Initial load
async function initializeWork() {
    try {
        console.log('Initializing work page...');

        // Check for and recover any failed wallet updates
        try {
            console.log('Checking for failed wallet updates to recover...');
            const recoveryResult = await recoverFailedWalletUpdates();

            if (recoveryResult.recovered) {
                console.log('Successfully recovered failed wallet updates:', recoveryResult);

                // Show notification to user
                Swal.fire({
                    icon: 'success',
                    title: 'Earnings Recovery',
                    html: `
                        <p>We've recovered ₹${recoveryResult.totalRecovered.toFixed(2)} in earnings that were not properly credited to your wallet.</p>
                        <p>Your wallet has been updated with these recovered earnings.</p>
                    `,
                    timer: 5000,
                    timerProgressBar: true
                });
            } else {
                console.log('No failed wallet updates to recover:', recoveryResult.message);
            }
        } catch (recoveryError) {
            console.error('Error recovering failed wallet updates:', recoveryError);
            // Continue with normal work page initialization even if recovery fails
        }

        // Check and fix any inconsistencies in the user's plan data
        try {
            const user = auth.currentUser;
            if (user) {
                console.log('Checking for plan data inconsistencies...');
                const planCheckResult = await checkAndFixPlanData(user.uid);

                if (planCheckResult.success && planCheckResult.fixesApplied && planCheckResult.fixesApplied.length > 0) {
                    console.log('Successfully fixed plan data inconsistencies:', planCheckResult);

                    // Show notification to user
                    Swal.fire({
                        icon: 'info',
                        title: 'Plan Data Updated',
                        html: `
                            <p>We've fixed some inconsistencies in your plan data to ensure you receive the correct earnings.</p>
                            <p>Your plan has been updated with the correct information.</p>
                        `,
                        timer: 5000,
                        timerProgressBar: true
                    });
                } else {
                    console.log('No plan data inconsistencies found or fixed:', planCheckResult.message);
                }
            }
        } catch (planCheckError) {
            console.error('Error checking plan data:', planCheckError);
            // Continue with normal work page initialization even if plan check fails
        }

        // Direct check for Sunday - as a failsafe
        const today = new Date();
        const isSunday = today.getDay() === 0;

        // Show Sunday message if it's Sunday
        if (isSunday) {
            console.warn('TODAY IS SUNDAY - DIRECT CHECK IN initializeWork');
            // Get tomorrow's date for the message
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const tomorrowFormatted = formatDateForDisplay(tomorrow);

            // Disable work interface
            disableWorkInterface(`Sunday Leave. Work will resume on ${tomorrowFormatted}.`);

            // Show a message at the top of the page
            const container = document.querySelector('.container');
            if (container) {
                const sundayAlert = document.createElement('div');
                sundayAlert.className = 'sunday-alert';
                sundayAlert.innerHTML = `
                    <div class="alert alert-warning">
                        <strong>Sunday Holiday!</strong> The platform is on leave today. Please come back tomorrow to continue your work.
                    </div>
                `;
                container.insertBefore(sundayAlert, container.firstChild);
            }

            // Still fetch basic user data for display purposes
            console.log('Sunday detected, but still loading basic user data for display purposes');

            try {
                // Check authentication before proceeding
                const isAuthorized = await checkAuthAndReferrer();
                if (!isAuthorized) {
                    console.log('Authorization check failed, stopping initialization');
                    hideLoading(); // Hide loading overlay before stopping
                    return;
                }

                // Get wallet data with caching
                const walletData = await getWalletDataWithCache();

                // Update wallet displays
                updateWalletDisplays(walletData);

                // Load full user data in the background
                const userData = await loadUserDataWithCache();

                if (userData) {
                    // Get plan status to update plan info in UI
                    const planStatus = await checkPlanExpiration();
                    console.log('Plan status for UI update on Sunday:', planStatus);

                    // Log the raw plan data for debugging
                    console.log('PLAN DEBUG (Sunday): Raw plan data:', JSON.stringify(planStatus));

                    // Force the plan type to be the one from Firebase (not hardcoded)
                    if (planStatus.planType) {
                        console.log('PLAN OVERRIDE (Sunday): Setting plan type to:', planStatus.planType);
                        // Make sure we're not accidentally using a hardcoded value
                        const actualPlanType = planStatus.planType;
                        planStatus.planType = actualPlanType;
                    }

                    // Update the UI with plan information
                    updatePlanInfo(planStatus);

                    // Update total videos count in the UI
                    if (totalVideosElement) {
                        // Use stats.totalVideosCompleted as the primary field
                        const totalVideos = userData.stats?.totalVideosCompleted ||
                                           userData.stats?.totalTranslationsCompleted ||
                                           userData.videosCount || 0;
                        totalVideosElement.textContent = totalVideos;
                        console.log('Updated total videos count in UI on Sunday:', totalVideos);
                    }

                    // Update today's video count (should be 0 on Sunday)
                    if (todayVideosElement) {
                        todayVideosElement.textContent = '0';
                    }

                    console.log('Basic user data loaded and displayed for Sunday view');
                }
            } catch (error) {
                console.error('Error loading basic user data on Sunday:', error);
            }

            // Hide loading overlay
            hideLoading();
            return;
        }

        // Check if user is online before initializing work page
        if (!isUserOnline()) {
            // Show offline message
            Swal.fire({
                icon: 'warning',
                title: 'You are offline',
                text: 'The work page requires an internet connection to function properly. Please check your connection and try again.',
                confirmButtonText: 'OK'
            });
            // Still hide loading overlay even if offline
            hideLoading();
            return;
        }

        // Show loading message while checking authentication
        if (loadingOverlay) {
            const loadingText = loadingOverlay.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = 'Checking authentication...';
            }
        }

        // Check authentication and referrer before proceeding
        const isAuthorized = await checkAuthAndReferrer();
        if (!isAuthorized) {
            console.log('Authorization check failed, stopping initialization');
            hideLoading(); // Hide loading overlay before stopping
            return;
        }

        // Fetch user data to update wallet displays and plan info using cache
        try {
            const currentUser = auth.currentUser;
            if (currentUser) {
                console.log('Fetching user data to update wallet displays and plan info with cache...');

                // Get wallet data with caching
                const walletData = await getWalletDataWithCache();

                // Update wallet displays using wallet.earning field
                updateWalletDisplays(walletData);

                // Load full user data in the background
                const userData = await loadUserDataWithCache();

                // Start work session time tracking
                startWorkSession();

                // Get plan status to update plan info in UI
                const planStatus = await checkPlanExpiration();
                console.log('Plan status for UI update:', planStatus);

                // Log the raw plan data for debugging
                console.log('PLAN DEBUG (initialization): Raw plan data:', JSON.stringify(planStatus));

                // Force the plan type to be the one from Firebase (not hardcoded)
                if (planStatus.planType) {
                    console.log('PLAN OVERRIDE (initialization): Setting plan type to:', planStatus.planType);
                    // Make sure we're not accidentally using a hardcoded value
                    const actualPlanType = planStatus.planType;
                    planStatus.planType = actualPlanType;
                }

                // Update the UI with plan information
                updatePlanInfo(planStatus);
                console.log('Full user data loaded in background');

                // Check short video advantage
                const advantageResult = await checkShortVideoAdvantage();

                // Double-check the result against plan activation date and active days
                // This ensures we're consistent with the logic in short-video-advantage.js
                if (advantageResult.hasAdvantage && (planStatus.planType === 'Starter' || planStatus.planType === 'Premium')) {
                    // Get the effective days (either from diffDays or activeDays, whichever is larger)
                    const activeDays = planStatus.activeDays || 0;
                    const diffDays = planStatus.diffDays || 0;
                    const effectiveDays = Math.max(activeDays, diffDays);

                    console.log(`Double-checking short video advantage: plan=${planStatus.planType}, activeDays=${activeDays}, diffDays=${diffDays}, effectiveDays=${effectiveDays}`);

                    // If plan is active for more than 7 days and advantage is not from referrals, override it
                    if (effectiveDays > 7 && advantageResult.reason !== 'referral_permanent') {
                        console.log(`Overriding short video advantage: plan ${planStatus.planType} is active for ${effectiveDays} days (>7)`);
                        userHasShortVideoAdvantage = false;
                        videoDuration = VIDEO_DURATIONS.REGULAR; // 300 seconds
                    } else {
                        userHasShortVideoAdvantage = advantageResult.hasAdvantage;
                        videoDuration = advantageResult.videoDuration;
                    }
                } else {
                    userHasShortVideoAdvantage = advantageResult.hasAdvantage;
                    videoDuration = advantageResult.videoDuration;
                }

                console.log('Short video advantage check result:', advantageResult);
                console.log('Final video duration decision:', { userHasShortVideoAdvantage, videoDuration });

                // Update the referral info text
                if (referralInfoElement) {
                    if (userHasShortVideoAdvantage) {
                        referralInfoElement.textContent = `You have short video advantage! Videos only need to be watched for ${videoDuration} seconds.`;
                        referralInfoElement.style.color = '#4CAF50';
                    } else {
                        referralInfoElement.textContent = 'Users with 3 successful referrals only need to watch for 30 seconds.';
                    }
                }

                // Update short video advantage status in the UI
                updateShortVideoStatus(advantageResult);

                // Update total videos count in the UI
                if (userData && totalVideosElement) {
                    // Use stats.totalVideosCompleted as the primary field
                    const totalVideos = userData.stats?.totalVideosCompleted || userData.stats?.totalTranslationsCompleted || userData.videosCount || 0;
                    totalVideosElement.textContent = totalVideos;
                    console.log('Updated total videos count in UI:', totalVideos);
                }
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
        }

        // Try to get fresh data from Firebase when user logs in
        try {
            // Check if it's a new day compared to the last stored date
            const now = new Date();
            const currentDateLocal = now.toLocaleDateString('en-US');
            const storedDate = localStorage.getItem('instra_daily_video_date');
            const submissionDateUTC = localStorage.getItem('instra_videos_submitted_date');
            const currentDateUTC = now.toISOString().split('T')[0]; // YYYY-MM-DD format

            console.log('Date check on initialization:', {
                storedDate,
                currentDateLocal,
                submissionDateUTC,
                currentDateUTC,
                isNewDay: storedDate !== currentDateLocal
            });

            // Get the current video count from Firebase
            const limitStatus = await checkDailyVideoLimit();
            todayVideoCount = limitStatus.currentCount || 0;
            console.log('Loaded fresh video count from Firebase:', todayVideoCount);

            // Check if it's a new day compared to the stored date or submission date
            const isNewDayByStoredDate = storedDate !== currentDateLocal;
            const isNewDayBySubmission = submissionDateUTC && submissionDateUTC !== currentDateUTC;

            if (isNewDayByStoredDate || isNewDayBySubmission) {
                console.log('New day detected, resetting video counts and submission status');
                console.log('Date comparison details:', {
                    storedDate,
                    currentDateLocal,
                    submissionDateUTC,
                    currentDateUTC,
                    isNewDayByStoredDate,
                    isNewDayBySubmission
                });

                // Reset the count to 0 for the new day
                todayVideoCount = 0;

                // Force an update to Firebase
                const user = auth.currentUser;
                if (user) {
                    const userRef = doc(db, 'users', user.uid);

                    // Create a new video data object for the new day
                    const newVideoData = {
                        date: currentDateLocal,
                        count: 0,
                        lastUpdated: serverTimestamp(),
                        submitted: false // Reset the submitted flag for the new day
                    };

                    // Comprehensive update to reset all relevant fields
                    await updateDoc(userRef, {
                        // Reset today's video counts
                        'stats.todayVideos': 0,
                        'stats.todayTranslations': 0,

                        // Reset daily videos tracking
                        'stats.dailyVideos': newVideoData,
                        'stats.dailyVideos.count': 0,
                        'stats.dailyVideos.submitted': false,

                        // Reset daily translations tracking (legacy)
                        'stats.dailyTranslations': newVideoData,
                        'stats.dailyTranslations.count': 0,
                        'stats.dailyTranslations.submitted': false
                    });

                    console.log('Reset all video count fields in Firebase to 0 for new day in initializeWork');
                }

                // Reset localStorage for the new day
                localStorage.setItem('instra_video_count', '0');
                localStorage.setItem('instra_daily_video_date', currentDateLocal);
                localStorage.removeItem('instra_videos_submitted');
                localStorage.removeItem('instra_videos_submitted_date');
                console.log('Reset all video count fields in localStorage for new day');
            } else if (limitStatus.alreadySubmitted) {
                // If videos were already submitted today, ensure UI shows correctly
                console.log('Videos already submitted today, updating UI accordingly');
                todayVideoCount = 0; // For UI display purposes
            }

            // Store in localStorage for future use
            localStorage.setItem('instra_video_count', todayVideoCount.toString());
            localStorage.setItem('instra_daily_video_date', currentDateLocal);

            // Update the UI with the current count
            updateVideoCountUI();

            // Check daily limit and handle accordingly
            const limitReached = await checkDailyLimit();

            // If limit is reached, stop initialization
            if (limitReached) {
                console.log('Daily limit reached, stopping initialization');
                hideLoading();
                return;
            }
        } catch (error) {
            console.error('Error loading video count from Firebase:', error);

            // Fallback to localStorage only if Firebase fails
            const localVideoCount = parseInt(localStorage.getItem('instra_video_count'), 10) || 0;
            console.log('Falling back to local video count:', localVideoCount);

            todayVideoCount = localVideoCount;
            updateVideoCountUI();
        }

        console.log('Checking plan status...');
        const canWork = await checkPlanStatus();

        // Force a second update of plan info after a short delay
        // This ensures the UI is updated even if there were timing issues
        setTimeout(async () => {
            try {
                console.log('Performing delayed second update of plan info');
                const planStatus = await checkPlanExpiration();
                updatePlanInfo(planStatus);
            } catch (error) {
                console.error('Error in delayed plan update:', error);
            }
        }, 2000);

        if (canWork) {
            console.log('Initializing video player...');

            // Initialize the VideoManager
            try {
                console.log('Initializing VideoManager...');
                await videoManager.initialize();
                console.log('VideoManager initialized successfully');
            } catch (error) {
                console.error('Error initializing VideoManager:', error);
                // Continue anyway, we'll use fallback videos if needed
            }

            // Preload videos
            await preloadVideos();

            // Initialize the video player with custom or advantage-based duration
            const user = auth.currentUser;
            const videoDuration = user ? await getUserVideoDuration(user.uid) : VIDEO_DURATIONS.REGULAR;
            videoPlayer = initializeVideoPlayer('videoPlayer', videoDuration, handleVideoComplete);

            // Set up the timer display
            videoPlayer.setupTimerDisplay('videoTimer');

            // Load the first video but don't start playing
            await loadNewVideo();

            // Make sure the start button is visible, enabled, and properly styled
            if (startVideoBtn) {
                startVideoBtn.classList.remove('hidden');
                startVideoBtn.disabled = false;
                startVideoBtn.classList.remove('disabled-btn');
            }

            // Make sure the next button is hidden and disabled
            if (nextVideoBtn) {
                nextVideoBtn.classList.add('hidden');
                nextVideoBtn.disabled = true;
            }

            // Set submit button state based on current video count and submission status
            if (submitButton) {
                const videosSubmitted = localStorage.getItem('instra_videos_submitted') === 'true';
                if (videosSubmitted) {
                    // If videos have been submitted, disable the submit button
                    submitButton.disabled = true;
                } else {
                    // Otherwise, enable it only if 100 videos have been watched
                    submitButton.disabled = todayVideoCount < 100;

                    // If user has 100 videos, highlight the submit button
                    if (todayVideoCount >= 100) {
                        console.log('User has 100 videos on page load, enabling submit button');
                        submitButton.classList.add('highlight-button');

                        // Remove the highlight after 3 seconds
                        setTimeout(() => {
                            submitButton.classList.remove('highlight-button');
                        }, 3000);

                        // Scroll to the submit button to make it visible
                        submitButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            }

            // Final update to ensure UI consistency after all initialization
            updateVideoCountUI();
        }

        console.log('Work initialization complete, hiding loading overlay...');
        // Hide loading overlay and show ready message
        hideLoading();
    } catch (error) {
        console.error('Error initializing work:', error);
        console.log('Error occurred, still hiding loading overlay...');
        hideLoading(); // Still hide loading overlay even if there's an error
    }
}

// Function to show debug information for plan and active days
async function showPlanDebugInfo() {
    try {
        // Get current user
        const currentUser = auth.currentUser;
        if (!currentUser) {
            console.error('No authenticated user found');
            return;
        }

        // Get user data with cache
        const userData = await loadUserDataWithCache();
        if (!userData) {
            console.error('Failed to load user data');
            return;
        }

        // Get plan status
        const planStatus = await checkPlanExpiration();

        // Show debug information in a dialog
        Swal.fire({
            title: 'Plan Debug Info',
            html: `
                <div style="text-align: left; font-family: monospace; background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">
                    <p><strong>Plan Type:</strong> ${planStatus.planType}</p>
                    <p><strong>Active Days:</strong> ${planStatus.activeDays}</p>
                    <p><strong>Valid Days:</strong> ${planStatus.validDays || (planStatus.planType === 'Trial' ? 2 : 30)}</p>
                    <p><strong>Days Left:</strong> ${planStatus.daysLeft}</p>
                    <p><strong>Plan Activated:</strong> ${planStatus.planActivationDate || 'Unknown'}</p>
                    <p><strong>Expired:</strong> ${planStatus.expired ? 'Yes' : 'No'}</p>
                    <hr>
                    <p><strong>Raw User Data:</strong></p>
                    <pre style="white-space: pre-wrap; word-break: break-all;">${JSON.stringify(userData, null, 2)}</pre>
                </div>
            `,
            width: '600px',
            confirmButtonText: 'Close'
        });
    } catch (error) {
        console.error('Error showing plan debug info:', error);
        Swal.fire({
            icon: 'error',
            title: 'Debug Error',
            text: 'Failed to load plan debug information.'
        });
    }
}

// Add event listeners for the buttons
if (startVideoBtn) {
    startVideoBtn.addEventListener('click', handleStartVideo);
}

if (nextVideoBtn) {
    nextVideoBtn.addEventListener('click', handleNextVideo);
}

if (submitButton) {
    // Remove any existing event listeners to prevent duplicates
    submitButton.removeEventListener('click', handleSubmit);

    // Create a wrapper function to add additional safeguards
    const handleSubmitWithSafeguards = async (event) => {
        // Prevent default behavior and stop propagation
        event.preventDefault();
        event.stopPropagation();

        // Additional check to prevent rapid clicks
        if (submitButton.disabled || submitButton.classList.contains('submitting')) {
            console.log('Submit button is disabled or already submitting, ignoring click');
            return;
        }

        // Call the actual submit handler
        await handleSubmit();
    };

    // Add the event listener with the wrapper
    submitButton.addEventListener('click', handleSubmitWithSafeguards);

    // Mark that we've added the listener to prevent duplicates
    submitButton.dataset.listenerAdded = 'true';

    console.log('Submit button event listener attached with safeguards');
}

// Add event listener for debug button
const debugActiveDaysBtn = document.getElementById('debugActiveDaysBtn');
if (debugActiveDaysBtn) {
    debugActiveDaysBtn.addEventListener('click', showPlanDebugInfo);
    // Make the debug button visible in development mode or if URL has debug parameter
    const isDevMode = window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1' ||
                     window.location.search.includes('debug=true');
    debugActiveDaysBtn.style.display = isDevMode ? 'inline-block' : 'none';
}

// Add a visibility change event listener to pause/resume the timer
document.addEventListener('visibilitychange', () => {
    console.log('Page visibility changed:', document.hidden ? 'hidden' : 'visible');

    // If page is hidden, pause the video and timer
    if (document.hidden) {
        console.log('Page hidden, pausing video and timer');

        // Pause the video if it's playing
        if (videoElement && !videoElement.paused) {
            videoElement.pause();
            console.log('Video paused due to page hidden');
        }

        // Set the timer pause flag
        window.isTimerPaused = true;

        // Update timer display if needed
        const timerElement = document.getElementById('videoTimer');
        if (timerElement && !timerElement.innerHTML.includes('Paused')) {
            // Save current time display
            if (!timerElement.dataset.originalTime) {
                timerElement.dataset.originalTime = timerElement.textContent;
            }
            timerElement.innerHTML = '<i class="fas fa-pause-circle"></i> Paused';
        }
    } else {
        console.log('Page visible again, resuming timer if it was running');

        // Resume the timer if it was running
        window.isTimerPaused = false;

        // Update the UI to restore the timer display
        const timerElement = document.getElementById('videoTimer');
        if (timerElement && timerElement.innerHTML.includes('Paused')) {
            if (timerElement.dataset.originalTime) {
                timerElement.textContent = timerElement.dataset.originalTime;
                timerElement.style.color = ''; // Reset color
                delete timerElement.dataset.originalTime;
            }
        }

        // Resume video playback if it was paused due to page visibility
        if (videoElement && videoElement.paused && window.currentTimerInterval) {
            videoElement.play().catch(error => {
                console.log('Could not resume video playback:', error);
            });
        }
    }
});

// Function to initialize debug buttons
function initializeDebugButtons() {
    // Make debug buttons visible in development mode or if URL has debug parameter
    const isDevMode = window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1' ||
                     window.location.search.includes('debug=true');

    // Debug button in plan header
    const headerDebugBtn = document.getElementById('debugActiveDaysBtn');
    if (headerDebugBtn) {
        headerDebugBtn.style.display = isDevMode ? 'inline-block' : 'none';
        headerDebugBtn.addEventListener('click', showPlanDebugInfo);
    }

    // Debug button in stats section
    const statsDebugBtn = document.querySelector('.stat-item .debug-btn');
    if (statsDebugBtn) {
        statsDebugBtn.style.display = isDevMode ? 'inline-block' : 'none';
        statsDebugBtn.addEventListener('click', showPlanDebugInfo);
    }

    // Debug info section
    const debugInfoElement = document.getElementById('planDebugInfo');
    if (debugInfoElement) {
        debugInfoElement.style.display = isDevMode ? 'block' : 'none';
    }
}

// Initialize the work page when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeWork();
    initializeDebugButtons();
});

// Add debug function for testing leave functionality
window.testLeaveFunction = async function() {
    console.log('=== TESTING LEAVE FUNCTIONALITY ===');

    // Clear cache first
    clearLeaveCache();
    console.log('Cache cleared');

    // Test isLeaveDay
    const isLeave = await isLeaveDay();
    console.log('isLeaveDay() result:', isLeave);

    // Test getLeaveReason
    const reason = await getLeaveReason();
    console.log('getLeaveReason() result:', reason);

    // Test getLeaveSettings directly
    const { getLeaveSettings } = await import('./leave-system.js');
    const settings = await getLeaveSettings();
    console.log('getLeaveSettings() result:', settings);

    console.log('=== END TEST ===');
    return { isLeave, reason, settings };
};

// Export functions for testing
export {
    initializeWork,
    preloadVideos,
    loadNewVideo,
    handleVideoComplete,
    handleStartVideo,
    handleNextVideo,
    updateVideoCountUI,
    handleSubmit,
    checkDailyLimit,
    clearVideoLocalStorage,
    updateSubmittedUI
};
