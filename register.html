<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - MyTube</title>
    <link rel="icon" href="img/mytube-favicon.svg" type="image/svg+xml">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/pages.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/youtube-theme.css">

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Global Functions -->
    <script>
        // Close error modal
        window.closeErrorModal = function() {
            const errorModal = document.getElementById('errorModal');
            errorModal.classList.remove('show');
        }

        // Show error modal
        window.showErrorModal = function(message) {
            const errorModal = document.getElementById('errorModal');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorModal.classList.add('show');
        }

        // Password visibility toggle
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.querySelector(`[data-input="${inputId}"] i`);

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.replace('fa-eye', 'fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.replace('fa-eye-slash', 'fa-eye');
            }
        }
    </script>
</head>
<body>
    <!-- Animated Background -->
    <div class="animated-bg"></div>

    <!-- Back Button -->
    <a href="index.html" class="back-button">
        <i class="fas fa-chevron-left"></i> Back to Home
    </a>



    <!-- Main Content -->
    <main class="container">
        <div class="glass-card auth-container">
            <h1 class="gradient-text">Create Account</h1>

            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label for="registerName"><i class="fas fa-user"></i> Full Name</label>
                    <input type="text" id="registerName" required placeholder="Enter your full name">
                </div>
                <div class="form-group">
                    <label for="registerEmail"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" id="registerEmail" required placeholder="Enter your email">
                </div>
                <div class="form-group">
                    <label for="registerPhone"><i class="fas fa-phone"></i> Phone Number</label>
                    <input type="tel" id="registerPhone" required placeholder="Enter your phone number">
                </div>
                <div class="form-group">
                    <label for="registerCity"><i class="fas fa-city"></i> City</label>
                    <input type="text" id="registerCity" required placeholder="Enter your city">
                </div>
                <div class="form-group">
                    <label for="registerReferredBy"><i class="fas fa-city"></i> Referral Code (optional)</label>
                    <input type="text" id="registerReferredBy" placeholder="Enter referral code">
                </div>
                <div class="form-group">
                    <label for="registerPassword"><i class="fas fa-lock"></i> Password</label>
                    <div class="password-input-group">
                        <input type="password" id="registerPassword" required placeholder="Create a password">
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility('registerPassword')" data-input="registerPassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="password-hint">Minimum 8 characters (letters/numbers/special characters)</small>
                    <div class="password-strength-meter">
                        <div class="strength-bar" id="passwordStrengthBar"></div>
                    </div>
                    <small id="passwordStrengthText" class="password-strength-text">Password strength</small>
                </div>
                <div class="form-group">
                    <label for="registerConfirmPassword"><i class="fas fa-lock"></i> Confirm Password</label>
                    <div class="password-input-group">
                        <input type="password" id="registerConfirmPassword" required placeholder="Confirm your password">
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility('registerConfirmPassword')" data-input="registerConfirmPassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="form-group terms">
                    <p class="terms-text">By registering, you agree to our <a href="terms.html">Terms of Service</a> and <a href="privacy.html">Privacy Policy</a></p>
                </div>
                <button id="registerBtn" type="submit" class="btn-primary">
                    <i class="fas fa-user-plus"></i> Register
                </button>
            </form>

            <div class="auth-options">
                <p>Already have an account? <a href="login.html">Login here</a></p>
                <div class="help-links">
                    <a href="how-it-works.html" class="help-link"><i class="fas fa-info-circle"></i> How It Works</a>
                    <a href="faq.html" class="help-link"><i class="fas fa-question-circle"></i> FAQ</a>
                </div>
            </div>
        </div>
    </main>

    <!-- Error Modal -->
    <div id="errorModal" class="modal">
        <div class="modal-content glass-card">
            <div class="modal-header">
                <h3 class="gradient-text">Error</h3>
                <button class="close-btn" onclick="closeErrorModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="closeErrorModal()">OK</button>
            </div>
        </div>
    </div>

    <!-- SweetAlert2 (again, before your script) -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Console logger for debugging -->
    <script>
        console.log('Register page loaded');
        window.addEventListener('error', function(e) {
            console.error('Global error caught:', e.error || e.message);
        });
    </script>

    <!-- New standalone registration logic -->
    <script type="module" src="js/register.js"></script>
</body>
</html>
