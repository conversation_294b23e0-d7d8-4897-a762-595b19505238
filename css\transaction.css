.page-container {
    min-height: 100vh;
    background-color: #ffffff;
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.back-button {
    background: rgba(34, 197, 94, 0.1);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #22c55e;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(34, 197, 94, 0.1);
    transition: all 0.3s ease;
}

.back-button:hover {
    transform: translateX(-5px);
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.15);
}

.page-header h1 {
    font-size: 1.8rem;
    color: #16a34a;
    margin: 0;
}

.transaction-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-section {
    background: rgba(34, 197, 94, 0.05);
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-end;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 12px;
    font-size: 1rem;
    color: #333;
    background: #f8f9fa;
}

.date-inputs {
    display: flex;
    gap: 10px;
}

.date-inputs input {
    flex: 1;
}

.btn-filter {
    padding: 12px 24px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-filter:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.transactions-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.transaction-item {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.transaction-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.credit .transaction-icon {
    background: #e8f5e9;
    color: #4CAF50;
}

.debit .transaction-icon {
    background: #ffebee;
    color: #f44336;
}

.transfer .transaction-icon {
    background: #e3f2fd;
    color: #2196F3;
}

.transaction-details h3 {
    font-size: 1.1rem;
    color: #333;
    margin: 0 0 5px 0;
}

.transaction-details p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.transaction-amount {
    font-size: 1.2rem;
    font-weight: 600;
}

.credit .transaction-amount {
    color: #000000; /* Changed to black */
}

.debit .transaction-amount {
    color: #000000; /* Changed to black */
}

.transfer .transaction-amount {
    color: #000000; /* Changed to black */
}

.btn-load-more {
    padding: 15px;
    background: transparent;
    border: 2px solid #4CAF50;
    color: #4CAF50;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.btn-load-more:hover {
    background: #4CAF50;
    color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 15px;
    }

    .page-header h1 {
        font-size: 1.5rem;
    }

    .filter-section {
        padding: 15px;
    }

    .filter-group {
        min-width: 100%;
    }

    .transaction-item {
        padding: 15px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .transaction-info {
        flex-direction: column;
    }

    .transaction-amount {
        font-size: 1.1rem;
    }
}

/* Filters Section */
.filters {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.filter-select {
    width: 100%;
    padding: 0.75rem;
    background: var(--bg-light);
    border: var(--glass-border);
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--accent-pink);
}

/* Summary Cards */
.summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    font-size: 1.2rem;
    color: var(--text-light);
}

.summary-info {
    flex: 1;
}

.summary-info h3 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-gray);
}

.summary-amount {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-light);
}

/* Transactions List */
.transactions-list {
    margin-top: 2rem;
}

.transactions-list h2 {
    margin-bottom: 1.5rem;
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: var(--glass-border);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.transaction-icon.success {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.transaction-icon.error {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}

.transaction-icon.pending {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.transaction-info {
    flex: 1;
}

.transaction-info h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-light);
}

.transaction-info p {
    margin: 0.25rem 0 0;
    font-size: 0.85rem;
    color: var(--text-gray);
}

.transaction-status {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background: var(--bg-light);
}

.transaction-amount {
    font-size: 1.1rem;
    font-weight: 600;
    min-width: 100px;
    text-align: right;
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.page-numbers {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-numbers .btn-secondary {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-numbers .btn-secondary.active {
    background: var(--primary-gradient);
    border: none;
}

.page-numbers span {
    color: var(--text-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
    .filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .transaction-item {
        flex-wrap: wrap;
    }

    .transaction-status {
        order: 3;
        width: 100%;
        text-align: center;
        margin-top: 0.5rem;
    }

    .transaction-amount {
        order: 2;
    }

    .pagination {
        flex-direction: column;
        gap: 1rem;
    }
}