.page-container {
    min-height: 100vh;
    background-color: var(--bg-dark);
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px 30px;
    border-radius: 20px;
    background: rgba(34, 197, 94, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(34, 197, 94, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.back-button {
    background: rgba(34, 197, 94, 0.1);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-green);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.back-button:hover {
    transform: translateX(-5px);
    background: var(--primary-gradient);
    box-shadow: var(--shadow-md);
}

.page-header h1 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin: 0;
}

.wallet-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Wallet Specific Styles */
.wallet-card {
    display: flex;
    flex-direction: column;
}

.wallet-card .stat-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
}

.wallet-balance {
    font-size: 1.8rem !important;
    font-weight: 600 !important;
    margin: 0.5rem 0 !important;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent !important;
}

.wallet-info {
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.4;
}

.btn-transfer,
.btn-withdraw {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
    font-size: 0.9rem;
}

.btn-transfer {
    background: var(--secondary-gradient);
    color: var(--text-light);
}

.btn-withdraw {
    background: var(--primary-gradient);
    color: var(--text-light);
}

.btn-transfer:hover,
.btn-withdraw:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

/* Button styles moved above */

.btn-transfer {
    background: var(--secondary-gradient);
    color: var(--text-light);
}

.btn-withdraw {
    background: var(--primary-gradient);
    color: var(--text-light);
}

.btn-transfer:hover,
.btn-withdraw:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

/* Activity List Styles */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item i {
    font-size: 1.2rem;
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
}

.activity-details {
    flex: 1;
}

.activity-details h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-light);
}

.activity-details p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-gray);
}

.activity-amount {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-light);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: var(--bg-card);
    width: 90%;
    max-width: 500px;
    padding: 2rem;
    border-radius: 20px;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
    backdrop-filter: blur(10px);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
    backdrop-filter: blur(10px);
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.close:hover {
    color: var(--text-light);
    transform: rotate(90deg);
}

.modal h2 {
    font-size: 1.4rem;
    color: var(--text-light);
    margin-bottom: 25px;
    text-align: center;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    font-size: 0.95rem;
}

.amount-input {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0 15px;
}

.amount-input span {
    color: var(--text-light);
    font-weight: 500;
}

.amount-input input {
    border: none;
    background: transparent;
    padding: 12px;
    width: 100%;
    font-size: 1rem;
    color: var(--text-light);
    outline: none;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-light);
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.btn-submit {
    width: 100%;
    padding: 12px;
    background: var(--primary-gradient);
    color: var(--text-light);
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

/* Withdrawal Status Colors */
.status-paid {
    color: #10b981; /* Green */
    font-weight: 600;
    background: rgba(16, 185, 129, 0.1);
    padding: 4px 10px;
    border-radius: 20px;
    display: inline-block;
    font-size: 0.85rem;
}

.status-rejected {
    color: #ef4444; /* Red */
    font-weight: 600;
    background: rgba(239, 68, 68, 0.1);
    padding: 4px 10px;
    border-radius: 20px;
    display: inline-block;
    font-size: 0.85rem;
}

.status-pending {
    color: #f59e0b; /* Orange */
    font-weight: 500;
    background: rgba(245, 158, 11, 0.1);
    padding: 4px 10px;
    border-radius: 20px;
    display: inline-block;
    font-size: 0.85rem;
}

/* Withdrawal Time Indicator */
.withdrawal-time-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    border-radius: 10px;
    margin: 15px 0;
    font-size: 0.9rem;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.withdrawal-time-indicator i {
    font-size: 1.1rem;
}

.withdrawal-time-indicator.available {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.withdrawal-time-indicator.time-restricted {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.withdrawal-time-indicator.leave-day {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.withdrawal-time-indicator.error,
.withdrawal-time-indicator.plan-restricted {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Time-restricted withdraw button */
.btn-withdraw.time-restricted {
    position: relative;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    overflow: visible;
}

.btn-withdraw.time-restricted:hover {
    box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
}

.btn-withdraw .time-indicator {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(245, 158, 11, 0.9);
    color: white;
    padding: 3px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    white-space: nowrap;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(245, 158, 11, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
    }
}

/* Leave-disabled and plan-disabled buttons */
.btn-withdraw.leave-disabled,
.btn-transfer.leave-disabled,
.btn-withdraw.plan-disabled,
.btn-transfer.plan-disabled {
    background: #6b7280;
    cursor: not-allowed;
    opacity: 0.7;
}

.btn-withdraw.leave-disabled:hover,
.btn-transfer.leave-disabled:hover,
.btn-withdraw.plan-disabled:hover,
.btn-transfer.plan-disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Mobile Responsive */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .wallet-card .stat-info {
        width: 100%;
    }

    .wallet-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-bottom: 20px;
    }

    .wallet-card {
        padding: 20px;
    }

    .wallet-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .wallet-balance {
        font-size: 1.5rem !important;
    }

    .recent-activity {
        padding: 20px;
    }

    .recent-activity h2 {
        font-size: 1.2rem;
    }

    .activity-item {
        padding: 12px;
        flex-wrap: wrap;
        gap: 8px;
    }

    .activity-item i {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .activity-details h4 {
        font-size: 0.9rem;
    }

    .activity-details p {
        font-size: 0.8rem;
    }

    .activity-amount {
        font-size: 0.9rem;
    }

    .modal-content {
        width: 95%;
        padding: 20px;
        margin: 10% auto;
    }

    .btn-transfer,
    .btn-withdraw,
    .btn-submit {
        padding: 10px;
        font-size: 0.9rem;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .amount-input input {
        font-size: 1rem;
        padding: 10px;
    }

    .withdraw-amount {
        font-weight: bold;
        font-size: 0.9rem;
        color: var(--primary-purple);
    }

    .withdraw-status {
        text-transform: uppercase;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .withdraw-date {
        font-size: 0.8rem;
        color: var(--text-gray);
        width: 100%;
        margin-top: 5px;
    }
}

/* Extra Small Screens */
@media (max-width: 480px) {
    .page-header {
        padding: 10px;
    }

    .back-button {
        width: 35px;
        height: 35px;
    }

    .page-header h1 {
        font-size: 1.3rem;
    }

    .wallet-card {
        padding: 15px;
    }

    .wallet-icon {
        width: 45px;
        height: 45px;
    }

    .wallet-card h3 {
        font-size: 1rem;
    }

    .wallet-balance {
        font-size: 1.3rem !important;
    }

    .wallet-info small {
        font-size: 0.8rem;
    }

    .btn-transfer,
    .btn-withdraw {
        padding: 8px;
        font-size: 0.8rem;
    }

    .recent-activity {
        padding: 15px;
    }

    .recent-activity h2 {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }

    .activity-item {
        padding: 10px;
    }

    .modal-content {
        padding: 15px;
        margin: 15% auto;
    }

    .modal h2 {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .btn-submit {
        padding: 8px;
    }
}

/* Content Header Styles */
.content-header .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.content-header .back-button {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.content-header .back-button:hover {
    transform: translateX(-5px);
    background: var(--primary-gradient);
    box-shadow: var(--shadow-md);
}

/* Mobile Responsive for Content Header */
@media (max-width: 768px) {
    .content-header {
        padding: 15px;
    }

    .content-header h1 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: 10px;
    }

    .content-header .back-button {
        width: 35px;
        height: 35px;
    }

    .content-header h1 {
        font-size: 1.3rem;
    }
}