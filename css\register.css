/* Inherit base styles from login.css and pages.css */
@import url('pages.css');

/* Register Page Specific Overrides */
.auth-container.register {
    max-width: 450px;
}

.auth-container.register h1 {
    font-size: 1.6rem;
    color: #16a34a;
    margin-bottom: 1rem;
}

/* Extra input spacing for full registration form */
.auth-form.register-form .form-group {
    margin-bottom: 0.5rem;
}

/* Two-column layout for name and phone (optional) */
.form-row {
    display: flex;
    flex-direction: row;
    gap: 1rem;
}

.form-row .form-group {
    flex: 1;
}

/* Custom styles for dropdowns if needed */
.form-group select {
    padding: 0.8rem 1rem;
    border-radius: 10px;
    border: 1px solid rgba(34, 197, 94, 0.2);
    background: rgba(34, 197, 94, 0.05);
    color: #16a34a;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group select:focus {
    outline: none;
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.1);
}

/* Register Button */
.btn-primary.register-btn {
    background: linear-gradient(135deg, #10b981, #3b82f6);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    margin-top: 20px;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.btn-primary.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.5);
}

.btn-primary.register-btn i {
    font-size: 1.2rem;
    color: white;
}

/* Adjust password visibility toggle for registration */
.password-input-group.register input {
    padding-right: 40px;
}

.password-input-group.register .toggle-password {
    right: 12px;
}

/* Mobile tweaks for register */
@media (max-width: 480px) {
    .auth-container.register {
        padding: 1.5rem 1rem;
    }

    .form-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-primary.register-btn {
        padding: 0.8rem;
        font-size: 0.9rem;
    }
}
