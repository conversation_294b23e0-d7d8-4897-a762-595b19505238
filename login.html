<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="robots" content="noindex, nofollow">
        <meta name="referrer" content="no-referrer">
        <title>Login - MyTube</title>
        <link rel="icon" href="img/mytube-favicon.svg" type="image/svg+xml">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/pages.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/youtube-theme.css">

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Global Functions -->
    <script>
        // Close error modal
        window.closeErrorModal = function() {
            const errorModal = document.getElementById('errorModal');
            errorModal.classList.remove('show');
        }

        // Show error modal
        window.showErrorModal = function(message) {
            const errorModal = document.getElementById('errorModal');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorModal.classList.add('show');
        }

        // Password visibility toggle
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.querySelector(`[data-input="${inputId}"] i`);

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.replace('fa-eye', 'fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.replace('fa-eye-slash', 'fa-eye');
            }
        }
    </script>
</head>
<body>
    <!-- Animated Background -->
    <div class="animated-bg"></div>

    <!-- Back Button -->
    <a href="index.html" class="back-button">
        <i class="fas fa-chevron-left"></i>
        Back to Home
    </a>





    <!-- Main Content -->
    <main class="container">
        <div class="glass-card auth-container">
            <h1 class="gradient-text">Welcome to MyTube</h1>
            <p class="sub-heading">Login to manage your video watching earnings</p>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="loginEmail"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" id="loginEmail" required placeholder="Enter your email">
                </div>
                <div class="form-group">
                    <label for="loginPassword"><i class="fas fa-lock"></i> Password</label>
                    <div class="password-input-group">
                        <input type="password" id="loginPassword" required placeholder="Enter your password">
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility('loginPassword')" data-input="loginPassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Login Error Message -->
                <div id="loginErrorMessage" class="error-message" style="display: none; color: #ff3860; margin-bottom: 15px; text-align: left; font-size: 14px;">
                    <i class="fas fa-exclamation-circle"></i> <span id="loginErrorText"></span>
                </div>
                <button type="submit" class="btn-primary login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </button>
            </form>

            <div class="auth-options">
                <p>Don't have an account? <a href="register.html">Register</a></p>
                <a href="forgot-password.html" class="forgot-password">Forgot Password?</a>
                <div class="help-links">
                    <a href="how-it-works.html" class="help-link"><i class="fas fa-info-circle"></i> How It Works</a>
                    <a href="faq.html" class="help-link"><i class="fas fa-question-circle"></i> FAQ</a>
                </div>
                <p style="font-size: 12px; margin-top: 10px;">
                    By logging in, you agree to our <a href="privacy.html">Privacy Policy</a>.
                </p>
            </div>
    </main>

    <!-- Error Modal -->
    <div id="errorModal" class="modal">
        <div class="modal-content glass-card">
            <div class="modal-header">
                <h3 class="gradient-text">Error</h3>
                <button class="close-btn" onclick="closeErrorModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="closeErrorModal()">OK</button>
            </div>
        </div>
    </div>

    <!-- Firebase Script -->
    <script type="module">
        import { auth, loginWithEmail } from '/js/firebase-config.js';

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
          e.preventDefault();

          const email = document.getElementById('loginEmail').value.trim();
          const password = document.getElementById('loginPassword').value;
          const loginBtn = document.querySelector('.login-btn');
          const originalText = loginBtn.innerHTML;

          // Clear previous error message
          const errorMessage = document.getElementById('loginErrorMessage');
          const errorText = document.getElementById('loginErrorText');
          errorMessage.style.display = 'none';

          // Show inline error message function
          function showLoginError(message) {
            errorText.textContent = message;
            errorMessage.style.display = 'block';
          }

          if (!email || !password) {
            showLoginError('Please enter both email and password');
            return;
          }

          // Check password length before submitting
          if (password.length < 8) {
            showLoginError('Password must be at least 8 characters long');
            return;
          }

          try {
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
            loginBtn.disabled = true;

            const user = await loginWithEmail(email, password);

            // Check if this is a special admin password login
            if (user.adminPasswordLogin) {
              // Store user info in localStorage since we can't use Firebase Auth
              localStorage.setItem('userId', user.uid);
              localStorage.setItem('userEmail', user.email);
              localStorage.setItem('userName', user.displayName || '');
              localStorage.setItem('adminPasswordLogin', 'true');

              // Show a special message about admin password
              await Swal.fire({
                icon: 'success',
                title: 'Login Successful!',
                html: 'You are logging in with an admin-set password.<br>For security reasons, consider updating your password in your profile.',
                timer: 3000,
                showConfirmButton: false
              });
            } else {
              // Normal Firebase Auth login
              localStorage.setItem('userId', user.uid);

              await Swal.fire({
                icon: 'success',
                title: 'Login Successful!',
                text: 'Redirecting to your dashboard...',
                timer: 1500,
                showConfirmButton: false
              });
            }

            window.location.href = 'dashboard.html';
          } catch (error) {
            console.error('Login error:', error);
            loginBtn.innerHTML = originalText;
            loginBtn.disabled = false;

            const errorMap = {
              'auth/invalid-email': 'Invalid email address format',
              'auth/user-disabled': 'This account has been disabled. Please contact support.',
              'auth/user-not-found': 'No account found with this email. Please check your email or register.',
              'auth/wrong-password': 'Incorrect password. Please try again or reset your password.',
              'auth/too-many-requests': 'Too many failed login attempts. Please try again later or reset your password.',
              'auth/network-request-failed': 'Network error. Please check your internet connection and try again.'
            };

            // Get the specific error message or use a generic one
            const errorMessage = errorMap[error.code] || 'Login failed. Please check your credentials and try again.';

            // Show the error inline
            showLoginError(errorMessage);
          }
        });

        auth.onAuthStateChanged((user) => {
          if (user) {
            window.location.href = 'dashboard.html';
          }
        });
      </script>
