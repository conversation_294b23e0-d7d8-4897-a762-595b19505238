/**
 * Active Days Utilities
 *
 * This module provides utility functions for working with active days
 * to ensure consistent calculation and display across the application.
 */

import {
    db,
    doc,
    getDoc,
    updateDoc,
    serverTimestamp,
    collection,
    query,
    where,
    getDocs
} from "./firebase-config.js";

import { Timestamp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

import { isLeaveDay } from "./leave-system.js";

// Log that we're using the shared Firebase instance
console.log('Active days utils using shared Firebase instance from firebase-config.js');

/**
 * Get admin-applied platform-wide leave days from system settings
 * @param {Date} startDate - Start date to check from
 * @param {Date} endDate - End date to check until
 * @returns {Promise<Array>} Array of admin leave dates in YYYY-MM-DD format
 */
async function getAdminLeaveDays(startDate, endDate) {
    try {
        const settingsRef = doc(db, 'systemSettings', 'leaveSettings');
        const settingsSnap = await getDoc(settingsRef);

        if (!settingsSnap.exists()) {
            console.log('No leave settings found, no admin leave days');
            return [];
        }

        const settings = settingsSnap.data();
        const additionalLeaveDays = settings.additionalLeaveDays || [];

        // Filter leave days within the date range
        const adminLeaveDates = [];
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = endDate.toISOString().split('T')[0];

        additionalLeaveDays.forEach(leaveDay => {
            let leaveDateStr;

            // Handle different date formats
            if (leaveDay.date instanceof Timestamp) {
                leaveDateStr = leaveDay.date.toDate().toISOString().split('T')[0];
            } else if (typeof leaveDay.date === 'string') {
                leaveDateStr = leaveDay.date;
            } else if (leaveDay.date instanceof Date) {
                leaveDateStr = leaveDay.date.toISOString().split('T')[0];
            } else {
                console.warn('Unknown date format for admin leave day:', leaveDay);
                return;
            }

            // Check if the leave date is within our range
            if (leaveDateStr >= startDateStr && leaveDateStr <= endDateStr) {
                adminLeaveDates.push(leaveDateStr);
            }
        });

        console.log(`Found ${adminLeaveDates.length} admin leave days between ${startDateStr} and ${endDateStr}:`, adminLeaveDates);
        return adminLeaveDates;

    } catch (error) {
        console.error('Error getting admin leave days:', error);
        return [];
    }
}

/**
 * Get the active days for a user with validation and proper calculation
 * This function now calculates active days properly by excluding leave days
 * WARNING: This is a synchronous function that may not reflect current leave data
 * For accurate calculations, use calculateActiveDays() which is async and queries current data
 * @param {Object} userData - User data object from Firestore
 * @returns {number} - Active days count (may not be current)
 */
export function getValidatedActiveDays(userData) {
    if (!userData) {
        console.warn('No user data provided to getValidatedActiveDays');
        return 1;
    }

    console.log('Getting validated active days (sync version - may not be current)');

    // Use stored active days if available and recent
    if (userData.activeDays && userData.lastActiveDayUpdate) {
        const lastUpdate = userData.lastActiveDayUpdate.toDate ? userData.lastActiveDayUpdate.toDate() : new Date(userData.lastActiveDayUpdate);
        const hoursSinceUpdate = (new Date() - lastUpdate) / (1000 * 60 * 60);

        // If updated within last 24 hours, use stored value
        if (hoursSinceUpdate < 24) {
            console.log(`Using stored active days (${userData.activeDays}) - updated ${hoursSinceUpdate.toFixed(1)} hours ago`);
            return userData.activeDays;
        }
    }

    // Try to calculate active days using sync method (may not be current)
    try {
        const calculatedActiveDays = calculateActiveDaysSync(userData);
        console.log('Calculated active days (sync - may not be current):', calculatedActiveDays);
        return calculatedActiveDays;
    } catch (error) {
        console.warn('Error calculating active days, falling back to stored value:', error);

        // Fallback to stored value if calculation fails
        let activeDays = 1; // Default to 1 if not set

        // Check different possible locations for active days
        if (userData.activeDays !== undefined && userData.activeDays !== null) {
            // Parse as integer and ensure it's at least 1
            activeDays = Math.max(1, parseInt(userData.activeDays, 10) || 1);
            console.log('Retrieved active days from userData.activeDays (fallback):', activeDays);
        } else if (userData.stats && userData.stats.activeDays !== undefined && userData.stats.activeDays !== null) {
            // Some implementations might store activeDays in the stats object
            activeDays = Math.max(1, parseInt(userData.stats.activeDays, 10) || 1);
            console.log('Retrieved active days from userData.stats.activeDays (fallback):', activeDays);
        } else if (userData.plan && userData.plan.activeDays !== undefined && userData.plan.activeDays !== null) {
            // Some implementations might store activeDays in the plan object
            activeDays = Math.max(1, parseInt(userData.plan.activeDays, 10) || 1);
            console.log('Retrieved active days from userData.plan.activeDays (fallback):', activeDays);
        } else {
            console.log('No active days found in user data, using default (fallback):', activeDays);
        }

        // Log the final fallback value for debugging
        console.log('Final validated active days value (fallback):', activeDays);
        return activeDays;
    }
}

/**
 * Get the raw stored active days value from the database (without calculation)
 * This function returns the stored activeDays value for comparison purposes
 * @param {Object} userData - User data object from Firestore
 * @returns {number} - Raw stored active days count
 */
export function getRawStoredActiveDays(userData) {
    if (!userData) {
        console.warn('No user data provided to getRawStoredActiveDays');
        return 1;
    }

    console.log('Getting raw stored active days from userData:', userData);

    // Ensure it's a number and at least 1
    let activeDays = 1; // Default to 1 if not set

    // Check different possible locations for active days
    if (userData.activeDays !== undefined && userData.activeDays !== null) {
        // Parse as integer and ensure it's at least 1
        activeDays = Math.max(1, parseInt(userData.activeDays, 10) || 1);
        console.log('Retrieved raw active days from userData.activeDays:', activeDays);
    } else if (userData.stats && userData.stats.activeDays !== undefined && userData.stats.activeDays !== null) {
        // Some implementations might store activeDays in the stats object
        activeDays = Math.max(1, parseInt(userData.stats.activeDays, 10) || 1);
        console.log('Retrieved raw active days from userData.stats.activeDays:', activeDays);
    } else if (userData.plan && userData.plan.activeDays !== undefined && userData.plan.activeDays !== null) {
        // Some implementations might store activeDays in the plan object
        activeDays = Math.max(1, parseInt(userData.plan.activeDays, 10) || 1);
        console.log('Retrieved raw active days from userData.plan.activeDays:', activeDays);
    } else {
        console.log('No raw active days found in user data, using default:', activeDays);
    }

    // Log the final value for debugging
    console.log('Final raw stored active days value:', activeDays);

    return activeDays;
}

/**
 * Calculate days left in a plan with validation
 * @param {number} activeDays - Current active days count
 * @param {string} planType - Plan type (Trial, Starter, Premium, etc.)
 * @returns {number} - Days left in the plan
 */
export function calculateDaysLeft(activeDays, planType) {
    // Get plan validity days based on plan type
    const validDays = planType === 'Trial' ? 2 : 30;

    // Calculate days left with validation to prevent negative values
    return Math.max(0, validDays - activeDays + 1);
}

/**
 * Safely convert various date formats to a JavaScript Date object
 * @param {any} dateField - The date field to convert
 * @param {string} fieldName - Name of the field for logging purposes
 * @returns {Date|null} - JavaScript Date object or null if conversion fails
 */
export function safelyGetDate(dateField, fieldName = 'date field') {
    if (!dateField) return null;

    try {
        // Case 1: It's a Firestore Timestamp with toDate() method
        if (typeof dateField.toDate === 'function') {
            return dateField.toDate();
        }

        // Case 2: It's already a JavaScript Date object
        if (dateField instanceof Date) {
            return dateField;
        }

        // Case 3: It's a timestamp number (milliseconds since epoch)
        if (typeof dateField === 'number') {
            return new Date(dateField);
        }

        // Case 4: It's an ISO string or other date string format
        if (typeof dateField === 'string') {
            const parsedDate = new Date(dateField);
            if (!isNaN(parsedDate.getTime())) {
                return parsedDate;
            }
        }

        // Case 5: It's an object with seconds and nanoseconds (Firestore Timestamp format)
        if (typeof dateField === 'object' && dateField.seconds !== undefined) {
            return new Date(dateField.seconds * 1000);
        }

        console.warn(`Could not parse ${fieldName}, unrecognized format:`, dateField);
        return null;
    } catch (error) {
        console.warn(`Error converting ${fieldName}:`, error);
        return null;
    }
}

/**
 * Format plan activation date for display
 * @param {Object} userData - User data object from Firestore
 * @returns {string} - Formatted activation date or empty string
 */
export function formatPlanActivationDate(userData) {
    if (!userData) return '';

    const dateFormat = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    };

    // Try planActivatedAt first (primary field)
    if (userData.planActivatedAt) {
        const activatedDate = safelyGetDate(userData.planActivatedAt, 'planActivatedAt');
        if (activatedDate) {
            return activatedDate.toLocaleDateString('en-US', dateFormat);
        }
    }

    // Fall back to planUpdatedAt if available
    if (userData.planUpdatedAt) {
        const updatedDate = safelyGetDate(userData.planUpdatedAt, 'planUpdatedAt');
        if (updatedDate) {
            return updatedDate.toLocaleDateString('en-US', dateFormat);
        }
    }

    return '';
}

/**
 * Update active days for a user based on plan activation date and leave days
 * This function properly calculates active days by excluding all types of leave days:
 * - User approved leave days from userLeaves collection
 * - Sundays (if Sunday leave is enabled)
 * - Admin-applied platform-wide leave days
 *
 * THIS IS THE CORRECT FUNCTION TO USE FOR ACCURATE ACTIVE DAYS CALCULATION
 * @param {string} userId - User ID
 * @param {Object} userData - User data object from Firestore
 * @returns {Promise<Object>} - Updated user data with new active days
 */
export async function updateActiveDaysIfNeeded(userId, userData) {
    if (!userId || !userData) {
        console.error('Missing userId or userData in updateActiveDaysIfNeeded');
        return userData;
    }

    try {
        console.log('Calculating active days from plan activation date regardless of login status');

        // Use the shared db instance from firebase-config.js
        const userRef = doc(db, 'users', userId);

        // Get the plan activation date
        let planActivationDate;
        if (userData.planActivatedAt) {
            planActivationDate = safelyGetDate(userData.planActivatedAt, 'planActivatedAt');
        } else if (userData.planUpdatedAt) {
            planActivationDate = safelyGetDate(userData.planUpdatedAt, 'planUpdatedAt');
        }

        // If we can't determine the activation date, use the current active days value
        if (!planActivationDate) {
            console.warn('Could not determine plan activation date, using current active days value');
            return userData;
        }

        console.log('Plan activation date:', planActivationDate);

        // Current date for calculations
        const currentDate = new Date();

        // Reset hours to compare just the dates
        currentDate.setHours(0, 0, 0, 0);
        const activationDay = new Date(planActivationDate);
        activationDay.setHours(0, 0, 0, 0);

        // Calculate total days since activation (including today)
        const totalDaysSinceActivation = Math.floor((currentDate - activationDay) / (1000 * 60 * 60 * 24)) + 1;
        console.log(`Total days since activation: ${totalDaysSinceActivation}`);

        // Count approved leave days from userLeaves collection
        const leaveDaysTaken = await countApprovedLeaveDays(userId);
        console.log(`Leave days taken (from userLeaves collection): ${leaveDaysTaken}`);

        // For today, check if it's a leave day
        let todayIsLeaveDay = false;
        try {
            todayIsLeaveDay = await isLeaveDay();
            console.log('Is today a leave day?', todayIsLeaveDay);
        } catch (error) {
            console.error('Error checking if today is a leave day:', error);
            // Default to not a leave day to avoid blocking user progress
            todayIsLeaveDay = false;
        }

        // Calculate active days as total days since activation minus leave days
        // If today is a leave day, don't count it in active days
        let activeDays = totalDaysSinceActivation - leaveDaysTaken;
        if (todayIsLeaveDay) {
            activeDays--;
        }

        // Ensure active days is at least 1
        activeDays = Math.max(1, activeDays);
        console.log(`Calculated active days: ${activeDays}`);

        // Only update if the active days value has changed
        const currentActiveDays = userData.activeDays || 1;
        if (activeDays !== currentActiveDays) {
            console.log(`Active days changed from ${currentActiveDays} to ${activeDays}, updating...`);

            try {
                await updateDoc(userRef, {
                    activeDays: activeDays,
                    lastActiveDayUpdate: serverTimestamp()
                });
                console.log('Updated active days to', activeDays);

                // Update the local userData object
                userData.activeDays = activeDays;
                userData.lastActiveDayUpdate = new Date();
            } catch (error) {
                console.error('Could not update active days:', error);
            }
        } else {
            console.log(`Active days unchanged at ${activeDays}, only updating timestamp`);

            // Only update the timestamp to record this check
            try {
                await updateDoc(userRef, {
                    lastActiveDayUpdate: serverTimestamp()
                });
                console.log('Updated lastActiveDayUpdate timestamp');

                // Update the local userData object
                userData.lastActiveDayUpdate = new Date();
            } catch (error) {
                console.error('Could not update lastActiveDayUpdate:', error);
            }
        }

        return userData;
    } catch (error) {
        console.error('Error in updateActiveDaysIfNeeded:', error);
        return userData;
    }
}

/**
 * Count approved leave days for a user from the userLeaves collection and count Sundays
 * @param {string} userId - User ID
 * @returns {Promise<number>} - Number of approved leave days including Sundays
 */
async function countApprovedLeaveDays(userId) {
    if (!userId) {
        console.warn('No userId provided to countApprovedLeaveDays');
        return 0;
    }

    try {
        // Query the userLeaves collection for approved leave days
        const leavesRef = collection(db, 'userLeaves');
        const leavesQuery = query(
            leavesRef,
            where('userId', '==', userId),
            where('status', '==', 'approved')
        );

        const leavesSnapshot = await getDocs(leavesQuery);

        // Use a Set to track unique leave dates
        const uniqueLeaveDates = new Set();
        leavesSnapshot.forEach(doc => {
            const leave = doc.data();
            if (leave.leaveDate) {
                uniqueLeaveDates.add(leave.leaveDate);
            }
        });

        // Count unique leave days from user leave applications
        const userLeaveDaysCount = uniqueLeaveDates.size;
        console.log(`User ${userId}: Found ${userLeaveDaysCount} unique approved leave days from user applications`);

        // Now count Sundays between activation date and today
        let sundayCount = 0;
        let activationDate = null; // Declare at function scope

        // Get user data to find activation date
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);

        if (userSnap.exists()) {
            const userData = userSnap.data();

            // Get activation date
            if (userData.planActivatedAt) {
                activationDate = safelyGetDate(userData.planActivatedAt, 'planActivatedAt');
            } else if (userData.planUpdatedAt) {
                activationDate = safelyGetDate(userData.planUpdatedAt, 'planUpdatedAt');
            }

            if (activationDate) {
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                // Start from activation date and count each Sunday
                const startDate = new Date(activationDate);
                startDate.setHours(0, 0, 0, 0);

                // Adjust to start from the first day (activation date)
                let currentDate = new Date(startDate);

                // More efficient way to count Sundays between two dates
                const msPerDay = 24 * 60 * 60 * 1000;
                const daysBetween = Math.round((today - currentDate) / msPerDay) + 1;

                // Get the day of week for the start date (0 = Sunday, 1 = Monday, etc.)
                const startDay = currentDate.getDay();

                // Calculate how many Sundays
                // First, count complete weeks
                const completeWeeks = Math.floor(daysBetween / 7);
                sundayCount = completeWeeks;

                // Then check remaining days
                const remainingDays = daysBetween % 7;
                for (let i = 0; i < remainingDays; i++) {
                    const checkDay = (startDay + i) % 7;
                    if (checkDay === 0) {
                        sundayCount++;
                    }
                }

                console.log(`User ${userId}: Counted ${sundayCount} Sundays in ${daysBetween} days since activation`);
            } else {
                console.warn(`Could not determine activation date for user ${userId}, not counting Sundays`);
            }
        } else {
            console.warn(`User document not found for ${userId}, not counting Sundays`);
        }

        // Count admin-applied platform-wide leave days
        let adminLeaveDaysCount = 0;
        if (activationDate) {
            const startDate = new Date(activationDate);
            startDate.setHours(0, 0, 0, 0);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            try {
                const adminLeaveDays = await getAdminLeaveDays(startDate, today);
                adminLeaveDaysCount = adminLeaveDays.length;
                console.log(`User ${userId}: Found ${adminLeaveDaysCount} admin leave days`);
            } catch (error) {
                console.error(`Error getting admin leave days for user ${userId}:`, error);
                adminLeaveDaysCount = 0;
            }
        }

        // Total leave days = user approved leaves + Sundays + admin leave days
        const totalLeaveDays = userLeaveDaysCount + sundayCount + adminLeaveDaysCount;
        console.log(`User ${userId}: Total leave days (${userLeaveDaysCount} user + ${sundayCount} Sundays + ${adminLeaveDaysCount} admin) = ${totalLeaveDays}`);

        return totalLeaveDays;
    } catch (error) {
        console.error('Error counting approved leave days:', error);
        return 0;
    }
}

/**
 * Calculate active days based on plan activation date and leave days
 * This function calculates active days from plan activation date regardless of user login status
 * @param {Object} userData - User data object from Firestore
 * @returns {Promise<number>} - Calculated active days
 */
export async function calculateActiveDays(userData) {
    if (!userData) {
        console.warn('No user data provided to calculateActiveDays');
        return 1;
    }

    console.log('Calculating active days from plan activation date regardless of login status');

    // First try to get the plan activation date
    let planActivationDate;
    if (userData.planActivatedAt) {
        planActivationDate = safelyGetDate(userData.planActivatedAt, 'planActivatedAt');
    } else if (userData.planUpdatedAt) {
        planActivationDate = safelyGetDate(userData.planUpdatedAt, 'planUpdatedAt');
    }

    // If we can't determine the activation date, use the stored active days value
    if (!planActivationDate) {
        console.warn('Could not determine plan activation date, using stored active days value');
        return getValidatedActiveDays(userData);
    }

    console.log('Plan activation date:', planActivationDate);

    // Current date for calculations
    const currentDate = new Date();

    // Reset hours to compare just the dates
    currentDate.setHours(0, 0, 0, 0);
    const activationDay = new Date(planActivationDate);
    activationDay.setHours(0, 0, 0, 0);

    // Calculate total days since activation (including today)
    const totalDaysSinceActivation = Math.floor((currentDate - activationDay) / (1000 * 60 * 60 * 24)) + 1;
    console.log(`Total days since activation: ${totalDaysSinceActivation}`);

    // Count approved leave days from userLeaves collection
    const leaveDaysTaken = await countApprovedLeaveDays(userData.uid || userData.id);
    console.log(`Leave days taken (from userLeaves collection): ${leaveDaysTaken}`);

    // Check if today is a leave day
    let todayIsLeaveDay = false;
    try {
        todayIsLeaveDay = await isLeaveDay();
        console.log('Is today a leave day?', todayIsLeaveDay);
    } catch (error) {
        console.error('Error checking if today is a leave day:', error);
        // Default to not a leave day to avoid blocking user progress
        todayIsLeaveDay = false;
    }

    // Calculate active days as total days since activation minus leave days
    // If today is a leave day, don't count it in active days
    let activeDays = totalDaysSinceActivation - leaveDaysTaken;
    if (todayIsLeaveDay) {
        activeDays--;
    }

    // Ensure active days is at least 1
    activeDays = Math.max(1, activeDays);
    console.log(`Calculated active days: ${activeDays}`);

    return activeDays;
}

/**
 * Calculate active days synchronously (for backward compatibility)
 * This is a fallback that doesn't query the userLeaves collection
 * This function calculates active days from plan activation date regardless of user login status
 * @param {Object} userData - User data object from Firestore
 * @returns {number} - Calculated active days
 */
export function calculateActiveDaysSync(userData) {
    if (!userData) {
        console.warn('No user data provided to calculateActiveDaysSync');
        return 1;
    }

    console.log('Calculating active days from plan activation date (sync) regardless of login status');

    // First try to get the plan activation date
    let planActivationDate;
    if (userData.planActivatedAt) {
        planActivationDate = safelyGetDate(userData.planActivatedAt, 'planActivatedAt');
    } else if (userData.planUpdatedAt) {
        planActivationDate = safelyGetDate(userData.planUpdatedAt, 'planUpdatedAt');
    }

    // If we can't determine the activation date, use the stored active days value
    if (!planActivationDate) {
        console.warn('Could not determine plan activation date, using stored active days value');
        return getValidatedActiveDays(userData);
    }

    console.log('Plan activation date:', planActivationDate);

    // Current date for calculations
    const currentDate = new Date();

    // Reset hours to compare just the dates
    currentDate.setHours(0, 0, 0, 0);
    const activationDay = new Date(planActivationDate);
    activationDay.setHours(0, 0, 0, 0);

    // Calculate total days since activation (including today)
    const totalDaysSinceActivation = Math.floor((currentDate - activationDay) / (1000 * 60 * 60 * 24)) + 1;
    console.log(`Total days since activation: ${totalDaysSinceActivation}`);

    // Get leave days from user data if available (fallback)
    const leaveDaysTaken = userData.leaveDaysTaken || 0;
    console.log(`Leave days taken (from userData): ${leaveDaysTaken}`);

    // Calculate active days as total days since activation minus leave days
    // Note: This sync function uses stored leaveDaysTaken which may not be current
    // For accurate calculations, use calculateActiveDays() which queries current leave data
    let activeDays = totalDaysSinceActivation - leaveDaysTaken;

    // Ensure active days is at least 1
    activeDays = Math.max(1, activeDays);
    console.log(`Calculated active days: ${activeDays}`);

    return activeDays;
}

/**
 * Get complete plan information for a user (synchronous version)
 * @param {Object} userData - User data object from Firestore
 * @returns {Object} - Complete plan information object
 */
export function getCompletePlanInfoSync(userData) {
    console.log('Getting complete plan info (sync) for userData:', userData);

    if (!userData) {
        console.warn('No user data provided to getCompletePlanInfoSync');
        return {
            planType: 'Trial',
            activeDays: 1,
            validDays: 2,
            daysLeft: 2,
            expired: false,
            planActivationDate: ''
        };
    }

    // Log the structure of userData to help diagnose issues
    console.log('userData structure check:', {
        hasUserData: !!userData,
        hasPlan: !!userData.plan,
        planType: typeof userData.plan,
        planValue: userData.plan,
        hasPlanName: userData.plan && !!userData.plan.name,
        planName: userData.plan && userData.plan.name,
        hasPlanType: !!userData.planType,
        planTypeValue: userData.planType,
        hasActiveDays: userData.activeDays !== undefined,
        activeDaysValue: userData.activeDays
    });

    // Get plan type with fallbacks for different data structures
    let planType = 'Trial'; // Default to Trial

    // Check different possible locations for plan name
    if (userData.plan && userData.plan.name) {
        planType = userData.plan.name;
        console.log('Found plan name in userData.plan.name:', planType);
    } else if (userData.planName) {
        planType = userData.planName;
        console.log('Found plan name in userData.planName:', planType);
    } else if (userData.plan && typeof userData.plan === 'string') {
        planType = userData.plan;
        console.log('Found plan name in userData.plan (string):', planType);
    } else {
        console.log('No plan name found in user data, using default:', planType);
    }

    // Normalize plan names for consistency
    if (planType.toLowerCase() === 'starter' || planType.toLowerCase() === 'premium') {
        // These are valid plan names, keep as is
        // Convert to proper case for display
        planType = planType.charAt(0).toUpperCase() + planType.slice(1).toLowerCase();
    } else if (planType.toLowerCase() === 'trial' || planType.toLowerCase() === 'free') {
        planType = 'Trial';
    } else {
        // For any other unexpected plan name, default to Trial
        console.warn('Unexpected plan name:', planType, 'defaulting to Trial');
        planType = 'Trial';
    }

    // Calculate active days based on plan activation date and leave days (sync version)
    const activeDays = calculateActiveDaysSync(userData);
    console.log('Active days calculated (sync):', activeDays);

    // Get valid days based on plan type
    const validDays = planType === 'Trial' ? 2 : 30;
    console.log('Valid days for plan type', planType, ':', validDays);

    // Calculate days left
    const daysLeft = calculateDaysLeft(activeDays, planType);
    console.log('Days left calculated:', daysLeft);

    // Check if plan has expired
    const expired = activeDays > validDays;
    console.log('Plan expired?', expired);

    // Get plan activation date
    const planActivationDate = formatPlanActivationDate(userData);
    console.log('Plan activation date:', planActivationDate);

    // Create the complete plan info object
    const planInfo = {
        planType,
        activeDays,
        validDays,
        daysLeft,
        expired,
        planActivationDate
    };

    // Final validation to ensure we have valid values
    if (typeof planInfo.activeDays !== 'number' || isNaN(planInfo.activeDays)) {
        console.warn('Invalid activeDays value detected, setting to default:', planInfo.activeDays);
        planInfo.activeDays = 1;
    }

    if (typeof planInfo.validDays !== 'number' || isNaN(planInfo.validDays)) {
        console.warn('Invalid validDays value detected, setting to default:', planInfo.validDays);
        planInfo.validDays = planInfo.planType === 'Trial' ? 2 : 30;
    }

    if (typeof planInfo.daysLeft !== 'number' || isNaN(planInfo.daysLeft)) {
        console.warn('Invalid daysLeft value detected, setting to default:', planInfo.daysLeft);
        planInfo.daysLeft = Math.max(0, planInfo.validDays - planInfo.activeDays + 1);
    }

    console.log('Complete plan info (sync) after validation:', planInfo);

    return planInfo;
}

/**
 * Get complete plan information for a user (asynchronous version)
 * This version queries the userLeaves collection to count leave days
 * @param {Object} userData - User data object from Firestore
 * @returns {Promise<Object>} - Complete plan information object
 */
export async function getCompletePlanInfo(userData) {
    console.log('Getting complete plan info (async) for userData:', userData);

    if (!userData) {
        console.warn('No user data provided to getCompletePlanInfo');
        return {
            planType: 'Trial',
            activeDays: 1,
            validDays: 2,
            daysLeft: 2,
            expired: false,
            planActivationDate: ''
        };
    }

    // Get plan type with fallbacks for different data structures
    let planType = 'Trial'; // Default to Trial

    // Check different possible locations for plan name
    if (userData.plan && userData.plan.name) {
        planType = userData.plan.name;
        console.log('Found plan name in userData.plan.name:', planType);
    } else if (userData.planName) {
        planType = userData.planName;
        console.log('Found plan name in userData.planName:', planType);
    } else if (userData.plan && typeof userData.plan === 'string') {
        planType = userData.plan;
        console.log('Found plan name in userData.plan (string):', planType);
    } else {
        console.log('No plan name found in user data, using default:', planType);
    }

    // Normalize plan names for consistency
    if (planType.toLowerCase() === 'starter' || planType.toLowerCase() === 'premium') {
        // These are valid plan names, keep as is
        // Convert to proper case for display
        planType = planType.charAt(0).toUpperCase() + planType.slice(1).toLowerCase();
    } else if (planType.toLowerCase() === 'trial' || planType.toLowerCase() === 'free') {
        planType = 'Trial';
    } else {
        // For any other unexpected plan name, default to Trial
        console.warn('Unexpected plan name:', planType, 'defaulting to Trial');
        planType = 'Trial';
    }

    // Calculate active days based on plan activation date and leave days (async version)
    const activeDays = await calculateActiveDays(userData);
    console.log('Active days calculated (async):', activeDays);

    // Get valid days based on plan type
    const validDays = planType === 'Trial' ? 2 : 30;
    console.log('Valid days for plan type', planType, ':', validDays);

    // Calculate days left
    const daysLeft = calculateDaysLeft(activeDays, planType);
    console.log('Days left calculated:', daysLeft);

    // Check if plan has expired
    const expired = activeDays > validDays;
    console.log('Plan expired?', expired);

    // Get plan activation date
    const planActivationDate = formatPlanActivationDate(userData);
    console.log('Plan activation date:', planActivationDate);

    // Create the complete plan info object
    const planInfo = {
        planType,
        activeDays,
        validDays,
        daysLeft,
        expired,
        planActivationDate
    };

    // Final validation to ensure we have valid values
    if (typeof planInfo.activeDays !== 'number' || isNaN(planInfo.activeDays)) {
        console.warn('Invalid activeDays value detected in async function, setting to default:', planInfo.activeDays);
        planInfo.activeDays = 1;
    }

    if (typeof planInfo.validDays !== 'number' || isNaN(planInfo.validDays)) {
        console.warn('Invalid validDays value detected in async function, setting to default:', planInfo.validDays);
        planInfo.validDays = planInfo.planType === 'Trial' ? 2 : 30;
    }

    if (typeof planInfo.daysLeft !== 'number' || isNaN(planInfo.daysLeft)) {
        console.warn('Invalid daysLeft value detected in async function, setting to default:', planInfo.daysLeft);
        planInfo.daysLeft = Math.max(0, planInfo.validDays - planInfo.activeDays + 1);
    }

    console.log('Complete plan info (async) after validation:', planInfo);

    return planInfo;
}

// Make functions available globally
window.getValidatedActiveDays = getValidatedActiveDays;
window.calculateDaysLeft = calculateDaysLeft;
window.formatPlanActivationDate = formatPlanActivationDate;
window.calculateActiveDays = calculateActiveDaysSync; // Use sync version for global access
window.calculateActiveDaysSync = calculateActiveDaysSync;
window.getCompletePlanInfo = getCompletePlanInfoSync; // Use sync version for global access
window.getCompletePlanInfoSync = getCompletePlanInfoSync;
window.safelyGetDate = safelyGetDate;
