/* Global Variables */
:root {
    /* Main Gradients - <PERSON> and White Theme */
    --primary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --secondary-gradient: linear-gradient(135deg, #15803d 0%, #22c55e 100%);
    --accent-gradient: linear-gradient(135deg, #16a34a 0%, #059669 100%);

    /* Solid Colors - Green Theme */
    --primary-green: #22c55e;
    --secondary-green: #16a34a;
    --accent-green: #15803d;

    /* Background Colors - White and Light Green */
    --bg-dark: #ffffff;
    --bg-card: #f8fffe;
    --bg-light: #f0fdf4;

    /* Text Colors */
    --text-light: #1f2937;
    --text-gray: #4b5563;
    --text-dark: #111827;

    /* Glass Effects */
    --glass-bg: rgba(34, 197, 94, 0.1);
    --glass-border: 1px solid rgba(34, 197, 94, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(34, 197, 94, 0.15);
}

/* Common Page Styles */
body {
    background: var(--bg-dark);
    color: var(--text-dark);
    font-family: 'Poppins', sans-serif;
    margin: 0;
    min-height: 100vh;
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1; /* Keep this negative to stay behind content */
    background: var(--bg-dark);
    overflow: hidden;
}

.animated-bg::before,
.animated-bg::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: var(--primary-gradient);
    opacity: 0.3;
    filter: blur(100px);
}

.animated-bg::before {
    top: -150px;
    left: -150px;
    animation: float 8s infinite;
}

.animated-bg::after {
    bottom: -150px;
    right: -150px;
    animation: float 8s infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(100px, 100px); }
}

/* Work Page Styles */
.work-container, .container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1; /* Above background, below header */
}

/* Timer Section */
.timer-section {
    margin-bottom: 15px;
    background: transparent;
}

/* Ensure timer section has no background */
.form-group.timer-section {
    background: transparent !important;
}

.timer-container {
    width: 100%;
    margin-bottom: 5px;
    background: transparent;
}

.timer-display-container {
    text-align: center;
    margin-bottom: 8px;
    background: transparent;
}

.timer-display {
    font-size: 1.5rem;
    font-weight: 700;
    color: #FF0000;
    margin-left: 10px;
    display: inline-block;
}

.timer-label {
    font-size: 1rem;
    color: #FF0000;
    font-weight: bold;
    display: inline-block;
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    background: var(--bg-dark);
    aspect-ratio: 16/9;
}

/* Video Loading Overlay */
.video-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none; /* Hidden by default */
    justify-content: center;
    align-items: center;
    z-index: 5;
}

.spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-pink);
    animation: spin 1s ease-in-out infinite;
}

.loading-text {
    margin-top: 15px;
    color: white;
    font-size: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Video Player */
#videoPlayer {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: var(--bg-dark);
    object-fit: cover;
}

/* Timer Progress Bar Container */
.timer-progress {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 5px;
}

/* Timer Progress Bar */
.progress-bar {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #FF0000, #FF6600);
    border-radius: 2px;
    transition: width 1s linear;
}

/* Video Buttons */
.video-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.video-btn {
    padding: 12px 25px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.start-btn {
    background: linear-gradient(135deg, #FF0000, #CC0000);
    color: white;
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 0, 0, 0.3);
}

.next-btn {
    background: linear-gradient(135deg, #8A2BE2, #4B0082);
    color: white;
    animation: pulse-button 2s infinite;
}

.next-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(138, 43, 226, 0.3);
    animation: none;
}

.next-btn.hidden {
    display: none;
}

/* Video Info */
.video-info {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.video-instruction {
    font-size: 0.95rem;
    color: var(--text-gray);
    margin-bottom: 12px;
    line-height: 1.5;
}

.short-video-advantage {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
    border-left: 4px solid #FFD700;
}

.advantage-icon {
    font-size: 1.8rem;
    color: #FFD700;
    margin-right: 15px;
}

.advantage-text p {
    font-size: 0.95rem;
    color: #FFD700;
    margin: 0;
    font-weight: 500;
}

.video-referral-info {
    font-size: 0.9rem;
    color: var(--accent-pink);
    font-style: italic;
}

.work-form {
    position: relative;
    z-index: 2;
    margin-top: 2rem;
    padding: 1.25rem;
    border-radius: 20px;
    background: transparent;
    border: var(--glass-border);
    animation: fadeIn 0.5s ease-out;
}

.work-form h2 {
    font-size: 1.3rem;
    margin: 0 0 20px 0;
    color: var(--text-light);
    text-align: center;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.form-group {
    margin-bottom: 20px;
    background: transparent;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-gray);
    font-size: 0.95rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--accent-pink);
    box-shadow: 0 0 0 2px rgba(217, 70, 239, 0.2);
}

.btn-primary {
    width: 100%;
    padding: 12px;
    background: var(--primary-gradient);
    color: var(--text-light);
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.btn-primary:disabled,
.btn-primary.disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
    transform: none;
    box-shadow: none;
}

.btn-primary:disabled:hover,
.btn-primary.disabled:hover {
    transform: none;
    box-shadow: none;
}



/* Back button style removed - using the one from header */

/* Responsive Design */
@media (max-width: 768px) {
    body {
        overflow-x: hidden;
    }

    .work-container, .container {
        grid-template-columns: 1fr;
        padding: 10px;
    }

    .work-form {
        padding: 15px;
        width: 95%;
        margin: 0.5rem auto;
        box-sizing: border-box;
    }

    .form-group input {
        padding: 10px;
        font-size: 0.9rem;
    }

    /* Adjust icon buttons for mobile */
    .icon-btn {
        padding: 0.3rem 0.4rem;
        font-size: 0.9rem;
    }

    /* Glass card consistency */
    .glass-card {
        width: 95%;
        margin: 0.5rem auto;
        padding: 0.8rem;
        border-radius: 15px;
        box-sizing: border-box;
    }

    /* Mobile-specific styles for sticky text */
    .sticky-english-text {
        position: sticky;
        top: 0;
        z-index: 100;
        background: var(--bg-card);
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        max-height: 25vh;
        overflow-y: auto;
    }

    /* When the text area is focused, ensure the sticky container is visible */
    #typedText:focus + .sticky-english-text {
        display: block !important;
        opacity: 1 !important;
    }

    /* Ensure all form elements fit within the container */
    textarea, select, input {
        width: 100%;
        box-sizing: border-box;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Icon Button Styles */
.icon-btn {
    background: none;
    border: none;
    color: var(--accent-pink);
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem 0.5rem;
    margin-left: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.icon-btn:active {
    transform: scale(0.95);
}

/* Permission Badge Styles */
.permission-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.permission-badge i {
    margin-right: 0.3rem;
    color: var(--accent-pink);
}

.permission-badge:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.permission-badge.success {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.permission-badge.success i {
    color: #4CAF50;
}

.permission-badge.success:hover {
    background-color: rgba(76, 175, 80, 0.3);
}

/* Highlight animation for newly granted permission */
.permission-badge.highlight-permission {
    animation: pulse-permission 2s infinite;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.7);
}

@keyframes pulse-permission {
    0% {
        background-color: rgba(76, 175, 80, 0.2);
        transform: scale(1);
    }
    50% {
        background-color: rgba(76, 175, 80, 0.4);
        transform: scale(1.05);
    }
    100% {
        background-color: rgba(76, 175, 80, 0.2);
        transform: scale(1);
    }
}

/* Support Link Style */
.support-link {
    color: var(--accent-pink);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin-top: 0.5rem;
    transition: all 0.2s ease;
}

/* Prevent text selection for users without permission */
.no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: not-allowed;
    position: relative;
}

.no-select::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 10;
}

/* Styles for paste-only mode (users with copy/paste permission) */
.paste-only-mode {
    background-color: rgba(76, 175, 80, 0.05) !important;
    border: 1px solid rgba(76, 175, 80, 0.3) !important;
    cursor: default;
}

.paste-only-mode::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.paste-only-mode:focus {
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2) !important;
    border-color: rgba(76, 175, 80, 0.5) !important;
}

.support-link i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.support-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Input Validation Styles */
.form-group input:invalid {
    border-color: #ff6b6b;
}

.form-group input:valid {
    border-color: #4CAF50;
}

.page-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.back-button {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.back-button:hover {
    transform: translateX(-5px);
    background: var(--primary-gradient);
    box-shadow: var(--shadow-md);
}

.page-header h1 {
    font-size: 1.8rem;
    color: var(--text-light);
    margin: 0;
}

/* Plan Info */
.plan-info {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
}

/* Upgrade Button */
.upgrade-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(139, 92, 246, 0.3);
}

.upgrade-btn:hover {
    transform: scale(1.1) rotate(15deg);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.5);
}

.upgrade-btn:active {
    transform: scale(0.95);
}

/* Plan Selection in Modal */
.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.plan-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-purple);
}

.plan-card h4 {
    color: var(--primary-purple);
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.2rem;
    text-align: center;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.plan-features li i {
    color: #4CAF50;
}

.select-plan-btn {
    width: 100%;
    padding: 8px;
    margin-top: 15px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.select-plan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.current-plan {
    margin-bottom: 0.5rem;
}

.current-plan h2 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.plan-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--bg-light);
    border-radius: 6px;
    border: var(--glass-border);
}

.stat-item i {
    font-size: 1rem;
    color: var(--accent-pink);
}

.stat-details h3 {
    margin: 0;
    font-size: 0.75rem;
    color: var(--text-gray);
}

.stat-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-light);
}

.btn-upgrade {
    padding: 0.4rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    gap: 4px;
}

/* Batch Progress */
.batch-progress {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
}

.progress-info {
    margin-bottom: 0.5rem;
}

.progress-info h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.progress-info p {
    color: var(--text-gray);
    margin-top: 0.25rem;
    font-size: 0.8rem;
}

.progress-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.progress-bar {
    width: 300px;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

/* Update existing progress bar styles to avoid conflicts */
.batch-progress .progress-bar {
    height: 6px;
    background: var(--bg-light);
    border-radius: 3px;
    margin: 0.5rem 0;
    overflow: hidden;
}

.batch-progress .progress {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width 0.3s ease;
}

.btn-submit-batch {
    width: 100%;
    padding: 15px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit-batch:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

.btn-submit-batch:not(:disabled):hover {
    background: #45a049;
    transform: translateY(-2px);
}

/* Submit button states */
.submitting {
    background: #FFA500 !important;
    cursor: wait !important;
    opacity: 0.8;
    position: relative;
}

.submitting::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: loading-shimmer 1.5s infinite;
}

.submitted {
    background: #4CAF50 !important;
    cursor: default !important;
}

@keyframes loading-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Business ID */
.business-id {
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    background: var(--bg-light);
    border: var(--glass-border);
    border-radius: 8px;
    color: var(--text-light);
    text-align: center;
}



/* Buttons */
.btn-primary {
    width: 100%;
    padding: 0.75rem;
    background: var(--primary-gradient);
    color: var(--text-light);
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-sizing: border-box;
}

.btn-primary:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .work-form,
    .qr-display {
        padding: 1.5rem;
        margin: 1rem;
    }

    .zip-input {
        width: 40px !important;
        height: 40px;
    }

    .progress-container {
        flex-direction: column;
        gap: 15px;
    }

    .progress-bar {
        width: 100%;
        height: 15px;
    }

    .progress {
        font-size: 10px;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: var(--glass-bg);
    border: var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    /* Removed blur effect that was making text hard to read */
}

.modal-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.modal-body {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Plan Card Styles in Modal */
.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1rem 0;
}

.plan-selection-text {
    color: var(--text-light);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.plan-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    color: var(--text-light);
    /* Removed blur effect that was making text hard to read */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-gradient);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.plan-card-hover,
.plan-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-pink);
    box-shadow: 0 8px 25px rgba(217, 70, 239, 0.3);
}

.plan-card-hover::before,
.plan-card:hover::before {
    opacity: 1;
    height: 7px;
}

.plan-header {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--text-light);
    font-size: 1.5rem;
    font-weight: 600;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.plan-price {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-light);
    margin-top: 5px;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 15px 0;
    text-align: left;
}

.plan-features ul li {
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    color: var(--text-light);
    font-size: 0.95rem;
}

.plan-features ul li i {
    margin-right: 10px;
    color: var(--accent-pink);
    font-size: 1rem;
    margin-top: 3px;
}

.plan-features ul li i.fa-check {
    color: var(--success);
}

.plan-features ul li i.fa-certificate {
    color: #FFD700; /* Gold color for certificates */
}

.plan-features ul li i.fa-info-circle {
    color: var(--info);
}

.plan-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-5px);
}

.plan-card h4 {
    color: var(--text-light);
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.plan-price {
    font-size: 2rem;
    font-weight: 600;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
}

.plan-features {
    margin-bottom: 1.5rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.plan-features i {
    color: var(--accent-pink);
}

.upgrade-plan-btn, .select-plan-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    margin-top: 15px;
    box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
}

.upgrade-plan-btn:hover, .select-plan-btn:hover {
    background: var(--accent-gradient);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(217, 70, 239, 0.4);
}

.upgrade-plan-btn i, .select-plan-btn i {
    font-size: 1.2rem;
}

/* SweetAlert2 custom styling */
.swal2-cancel-button-themed {
    background: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-light) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.swal2-cancel-button-themed:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.swal2-title.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

.swal2-popup.glass-card {
    background: var(--bg-card) !important;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    /* Removed blur effect that was making text hard to read */
}

/* Wallets Section */
.wallets-section {
    position: relative;
    width: 90%;
    min-width: 280px;
    padding: 0.8rem;
    border-radius: 15px;
    background: var(--glass-bg);
    border: var(--glass-border);
    /* Removed blur effect that was making text hard to read */
    margin: 0.5rem auto;
    box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.25);
    animation: fadeIn 0.5s ease-out;
}

/* Trial Earnings Section */
.trial-earnings {
    position: relative;
    width: 90%;
    min-width: 280px;
    padding: 1rem;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    /* Removed blur effect that was making text hard to read */
    margin: 1rem auto;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    animation: fadeIn 0.5s ease-out;
}

.earnings-header {
    display: block;
    margin-bottom: 1rem;
    text-align: center;
}

.earnings-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 0;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stats-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.progress-circle {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6A11CB 0%, #2575FC 100%); /* Theme color gradient */
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(106, 17, 203, 0.3);
}

.counter-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    color: white; /* White text color */
    font-weight: 600;
    font-size: 1.8rem; /* Larger font size */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Text shadow for better readability */
}

.day-number, .target-count {
    display: inline-block;
}

.stats-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.stat-item {
    display: block;
    text-align: center;
    padding: 0.3rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: px;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wallets-section {
        width: 95%;
        min-width: 250px;
        padding: 0.5rem;
        margin: 0.5rem auto;
        border-radius: 10px;
    }

    .trial-earnings {
        width: 95%;
        min-width: 250px;
        padding: 0.8rem;
        margin: 0.5rem auto;
        border-radius: 15px;
    }

    .earnings-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.3rem;
        padding: 0;
    }

    .earnings-card {
        padding: 0.3rem;
    }

    .earnings-card h3 {
        font-size: 0.7rem;
        margin: 0 0 0.1rem 0;
    }

    .amount {
        font-size: 0.9rem;
        margin: 0.1rem 0;
    }

    /* Make wallet section look like dashboard quick action buttons */
    .wallets-section .earnings-card {
        border-radius: 6px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .stats-container {
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }

    .stats-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.2rem;
    }

    .progress-circle {
        width: 80px;
        height: 80px;
    }

    .counter-wrapper {
        font-size: 1.5rem;
    }

    .stat-item {
        font-size: 0.7rem;
        padding: 0.5rem;
    }

    .work-form {
        width: 95%;
        margin: 0.5rem auto;
        padding: 0.8rem;
        border-radius: 15px;
    }
}

/* Overlay for when trial section is shown */
.trial-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 0; /* Keep at 0 to stay below header but above background */
    /* Removed blur effect that was making text hard to read */
}

.gradient-text {
    font-size: 1.8rem;
    margin: 0;
}

.amount {
    font-size: 1rem;
    font-weight: 600;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0.2rem 0;
    text-align: center;
    display: block;
    width: 100%;
}

.earnings-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.earnings-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.earnings-card h3 {
    color: var(--text-gray);
    font-size: 0.9rem;
    margin: 0 0 0.2rem 0;
    text-align: center;
}

.active-plan, .plan-info {
    color: var(--accent-pink);
    font-size: 0.9rem;
    padding: 0.3rem 0.8rem;
    background: rgba(217, 70, 239, 0.1);
    border-radius: 20px;
    margin-right: 0.5rem;
}

.plan-info {
    background: rgba(16, 185, 129, 0.1);
    color: #10B981;
}

.support-link {
    color: #4CAF50;
    text-decoration: underline;
    font-weight: 500;
}

.support-link:hover {
    color: #45a049;
}

/* Plan Upgrade Modal Styles */
.plan-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
}

.current-plan-text {
    color: var(--accent-pink);
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.8rem;
    border: 1px solid var(--accent-pink);
    border-radius: 8px;
    width: 100%;
    text-align: center;
}

/* Input Styles */
.input-wrapper {
    position: relative;
    width: 100%;
}

.validation-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: none;
}

.validation-indicator.valid {
    display: block;
    background: rgba(34, 197, 94, 0.2);
    border: 2px solid #22c55e;
}

.validation-indicator.invalid {
    display: block;
    background: rgba(239, 68, 68, 0.2);
    border: 2px solid #ef4444;
}

.suggestion-box {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background: rgba(30, 31, 48, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-top: 4px;
    z-index: 100;
    display: none;
}

.suggestion-box.show {
    display: block;
}

.suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    color: var(--text-light);
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    background: rgba(139, 92, 246, 0.1);
}

.suggestion-item.selected {
    background: rgba(139, 92, 246, 0.2);
}

/* Timer Modal Styles */
.timer-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid var(--accent-pink);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    background: rgba(139, 92, 246, 0.1);
}

#browseTimerCount {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-light);
}

.modal-body p {
    text-align: center;
    color: var(--text-gray);
}

/* Input Focus Styles */
.input-wrapper input:focus {
    border-color: var(--accent-pink);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* Scrollbar Styles for Suggestion Box */
.suggestion-box::-webkit-scrollbar {
    width: 6px;
}

.suggestion-box::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.suggestion-box::-webkit-scrollbar-thumb {
    background: var(--accent-pink);
    border-radius: 3px;
}

.label-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    width: 100%;
}

.label-container label {
    margin-bottom: 0;
    color: var(--text-light);
    font-size: 0.95rem;
}

.correct-data, .input-form {
    padding: 20px;
}

.correct-data h3, .input-form h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.5rem;
}

/* Update existing correct-data class to avoid conflicts */
.correct-data-label {
    color: #b794f4;
    font-size: 0.8rem;
    font-weight: 400;
    padding: 0.2rem 0.5rem;
    background: rgba(139, 92, 246, 0.08);
    border-radius: 4px;
    border: 1px solid rgba(139, 92, 246, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .correct-data, .input-form {
        padding: 15px;
    }

    .correct-data h3, .input-form h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }
}

.input-wrapper input.error {
    border-color: #ff4444;
    background-color: rgba(255, 68, 68, 0.1);
}

.input-wrapper input.correct {
    border-color: #00C851;
    background-color: rgba(0, 200, 81, 0.1);
}

.input-wrapper .validation-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-wrapper input.error + .validation-indicator::after {
    content: '✕';
    color: #ff4444;
}

.input-wrapper input.correct + .validation-indicator::after {
    content: '✓';
    color: #00C851;
}

h2 {
    text-align: center;
    margin-bottom: 20px;
}

form label {
    display: block;
    margin-top: 10px;
    font-weight: bold;
}

form input[type="text"],
form select {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

button[type="submit"] {
    margin-top: 15px;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button[type="submit"]:hover {
    background-color: #45a049;
}

.error-message {
    color: red;
    font-size: 14px;
    margin-top: 5px;
}

.data-entry {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Form side styling */
.form-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Display side styling */
.display-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .data-entry {
        grid-template-columns: 1fr;
        padding: 20px;
    }
}

.data-fields, .form-fields {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.field {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.field label {
    font-weight: bold;
    color: #555;
}

.field input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.field input:focus {
    outline: none;
    border-color: #4CAF50;
}

.field span {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    min-height: 40px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .data-fields, .form-fields {
        gap: 12px;
    }

    .field input, .form-group input {
        padding: 8px;
        font-size: 0.95rem;
    }
}

.generate-btn {
    margin-top: 20px;
    padding: 12px 24px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.generate-btn:hover {
    background-color: #45a049;
}

.generate-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Container adjustments */
.container {
    position: relative;
    z-index: 2;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    width: 100%;
    box-sizing: border-box;
}

/* Plan Badge Styles */
.plan-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.upgrade-btn {
    background: none;
    border: none;
    color: var(--accent-pink);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.upgrade-btn:hover {
    background: rgba(217, 70, 239, 0.1);
    transform: scale(1.1);
}

.upgrade-btn i {
    font-size: 1.2rem;
    pointer-events: none;
}

select, select option {
    color: black;
}

/* Next Video Button */
#nextVideoBtn {
    width: 100%;
    padding: 12px;
    font-size: 1.1rem;
    background: var(--accent-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: pulse-button 2s infinite;
}

#nextVideoBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    animation: none;
}

@keyframes pulse-button {
    0% {
        box-shadow: 0 0 0 0 rgba(217, 70, 239, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(217, 70, 239, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(217, 70, 239, 0);
    }
}

/* Plan Selection Dialog Styles */
.plans-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .plans-grid {
        grid-template-columns: 1fr;
    }
}

.plan-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.25rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.plan-card:hover, .plan-card.selected {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
    border-color: #4CAF50;
}

.plan-card h4 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    font-size: 1.2rem;
    color: #fff;
}

.plan-price {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #4CAF50;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
    text-align: left;
    height: 150px; /* Fixed height for consistent alignment */
    display: flex;
    flex-direction: column;
}

.plan-features li {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #fff;
    font-size: 0.9rem;
    min-height: 40px; /* Ensure consistent height for each item */
    line-height: 1.4;
}

.plan-features li span {
    flex: 1;
}

.plan-features i {
    color: #4CAF50;
    width: 12px; /* Fixed width for consistent alignment */
    text-align: center;
    margin-top: 4px;
    flex-shrink: 0;
}

.upgrade-plan-btn {
    width: 100%;
    padding: 0.75rem;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.upgrade-plan-btn:hover {
    background: #45a049;
}
