/* YouTube Theme Override */
:root {
    /* Main Gradients */
    --primary-gradient: linear-gradient(135deg, #FF0000 0%, #CC0000 100%);
    --secondary-gradient: linear-gradient(135deg, #FF0000 0%, #FF5555 100%);
    --accent-gradient: linear-gradient(135deg, #3EA6FF 0%, #2196F3 100%);

    /* Solid Colors */
    --primary-purple: #FF0000; /* YouTube Red */
    --secondary-purple: #CC0000; /* Darker Red */
    --accent-pink: #3EA6FF; /* YouTube Blue */

    /* Background Colors */
    --bg-dark: #0F0F0F; /* YouTube Dark Background */
    --bg-card: #212121; /* YouTube Card Background */
    --bg-light: #181818; /* YouTube Slightly Lighter Background */

    /* Text Colors */
    --text-light: #FFFFFF; /* White text */
    --text-gray: #AAAAAA; /* YouTube Secondary Text */
    --text-dark: #FFFFFF; /* White text for dark backgrounds */

    /* Glass Effects - keeping the same effect but with YouTube colors */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: 1px solid rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(33, 33, 33, 0.37);
    --glass-backdrop: blur(10px);

    /* Shadows with YouTube red tint */
    --shadow-sm: 0 2px 4px rgba(255, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(255, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(255, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(255, 0, 0, 0.15);

    /* Status Colors - keeping functional colors the same */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3EA6FF; /* Changed to YouTube blue */
}

/* Additional YouTube-specific styles */

/* Button styles */
.cta-button,
.glass-button,
.btn-primary,
.submit-button,
.action-button {
    color: white !important;
    background: var(--primary-gradient) !important;
}

/* Button hover effects */
.cta-button:hover,
.glass-button:hover,
.nav-item:hover,
.mobile-nav-item:hover {
    background-color: rgba(255, 0, 0, 0.1) !important;
}

/* Active navigation items */
.nav-item.active,
.mobile-nav-item.active {
    background-color: rgba(255, 0, 0, 0.2) !important;
}

/* Logo color override */
.logo {
    background: var(--primary-gradient) !important;
}

/* Gradient text override */
.gradient-text {
    background: var(--primary-gradient) !important;
}

/* Ensure regular text is white except for transaction elements */
p:not(.no-transactions p),
span:not(.transaction-status):not(.transaction-status *),
div:not(.transaction-date):not(.transaction-type):not(.transaction-status):not(.transaction-amount):not(.th-date):not(.th-type):not(.th-status):not(.th-amount),
li,
label,
td,
th {
    color: var(--text-light) !important;
}

/* Transaction history text should be black */
.transaction-date,
.transaction-type,
.transaction-status,
.transaction-amount,
.th-date,
.th-type,
.th-status,
.th-amount,
.transaction-item div,
.transaction-header div,
.no-transactions p,
.no-transactions i,
#transactionsList div,
.transactions-list div {
    color: #000000 !important;
}

/* Additional specific rule for transaction items */
.transaction-item {
    color: #000000 !important;
}
.transaction-item * {
    color: #000000 !important;
}

/* Specific rules for transaction status */
.transaction-status.completed,
.transaction-status.pending,
.transaction-status.failed,
.transaction-status.unknown {
    color: #000000 !important;
}

/* Force all text in transaction list to be black */
/* Using :has() for modern browsers */
.glass-card:has(#transactionsList) * {
    color: #000000 !important;
}

/* Direct selector for broader compatibility */
.glass-card h2.gradient-text + .transaction-header + #transactionsList * {
    color: #000000 !important;
}

/* Exception for gradient text */
.glass-card:has(#transactionsList) .gradient-text,
.glass-card h2.gradient-text {
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Navigation links */
.nav-item, .mobile-nav-item, .nav-links a {
    color: var(--text-light) !important;
}

/* Links in content */
a:not(.gradient-text):not(.nav-item):not(.mobile-nav-item):not(.cta-button):not(.btn-primary):not(.action-button) {
    color: var(--accent-pink) !important;
}

/* Make sure headers are properly styled */
h1, h2, h3, h4, h5, h6 {
    color: var(--primary-purple) !important;
}

/* Gradient text should override the above */
.gradient-text {
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Card styles */
.glass-card,
.stat-card,
.feature-card,
.pricing-card,
.dashboard-card,
.faq-item {
    background-color: var(--bg-card) !important;
    border: var(--glass-border) !important;
}

/* Card hover effects */
.glass-card:hover,
.stat-card:hover {
    border-color: rgba(255, 0, 0, 0.3) !important;
}

/* Background overrides */
body, html {
    background-color: var(--bg-dark) !important;
}

.animated-bg {
    background-color: var(--bg-dark) !important;
}

/* Admin theme overrides */
.admin-body {
    --primary-color: #FF0000;
    --secondary-color: #CC0000;
    --background-color: #0F0F0F;
    --text-color: #FFFFFF;
    --card-color: #212121;
    --accent-color: #3EA6FF;
}


