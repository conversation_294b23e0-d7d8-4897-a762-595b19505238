// Import Firebase modules
import {
    auth,
    db
} from './firebase-config.js';
import {
    collection,
    doc,
    setDoc,
    getDoc,
    serverTimestamp,
    updateDoc,
    increment,
    query,
    where,
    getDocs
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import {
    onAuthStateChanged,
    signInWithEmailAndPassword,
    createUserWithEmailAndPassword
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";

// Mobile Menu Functionality
document.addEventListener('DOMContentLoaded', function() {
    const menuToggle = document.getElementById('menuToggle');
    const mobileNav = document.getElementById('mobileNav');
    const mobileLoginBtn = document.getElementById('mobileLoginBtn');

    if (menuToggle && mobileNav) {
        menuToggle.addEventListener('click', function() {
            mobileNav.classList.toggle('active');
            document.body.style.overflow = mobileNav.classList.contains('active') ? 'hidden' : '';
        });

        document.addEventListener('click', function(event) {
            if (!mobileNav.contains(event.target) && !menuToggle.contains(event.target)) {
                mobileNav.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    if (mobileLoginBtn) {
        mobileLoginBtn.addEventListener('click', function() {
            showLoginModal();
            mobileNav.classList.remove('active');
            document.body.style.overflow = '';
        });
    }
});

// Modal Functions
window.showLoginModal = function() {
    const loginModal = document.getElementById('loginModal');
    if (loginModal) loginModal.style.display = 'flex';
};

window.closeLoginModal = function() {
    const loginModal = document.getElementById('loginModal');
    if (loginModal) loginModal.style.display = 'none';
};

window.showRegisterModal = function() {
    const registerModal = document.getElementById('registerModal');
    if (registerModal) registerModal.style.display = 'flex';
};

window.closeRegisterModal = function() {
    const registerModal = document.getElementById('registerModal');
    if (registerModal) registerModal.style.display = 'none';
};

window.togglePasswordVisibility = function(inputId) {
    const input = document.getElementById(inputId);
    const icon = event.currentTarget.querySelector('i');
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.replace('fa-eye', 'fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.replace('fa-eye-slash', 'fa-eye');
    }
};

// Referral Code Generator
async function generateReferralCode() {
    const usersSnapshot = await getDoc(doc(db, 'metadata', 'usersCount'));
    let count = 0;
    if (usersSnapshot.exists()) {
        count = usersSnapshot.data().count || 0;
    }
    count += 1;

    // Update count in Firestore
    await setDoc(doc(db, 'metadata', 'usersCount'), { count }, { merge: true });

    const code = `MY${String(count).padStart(4, '0')}`; // e.g. MY0001
    return code;
}

// Transfer Funds Function
async function transferFunds(fromWallet, toWallet, amount) {
    const user = auth.currentUser;
    if (!user) throw new Error('User not logged in');

    const userRef = doc(db, 'users', user.uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) throw new Error('User data not found');

    const userData = userSnap.data();
    if (userData.wallet[fromWallet] < amount) throw new Error('Insufficient funds');

    await updateDoc(userRef, {
        [`wallet.${fromWallet}`]: increment(-amount),
        [`wallet.${toWallet}`]: increment(amount)
    });
}

// Main Logic
document.addEventListener('DOMContentLoaded', () => {
    const loginBtn = document.getElementById('loginBtn');
    const registerForm = document.getElementById('registerForm');
    const loginForm = document.getElementById('loginForm');
    const switchToRegisterBtn = document.getElementById('switchToRegister');
    const switchToLoginBtn = document.getElementById('switchToLogin');

    // Handle Login Modal Switch
    if (switchToRegisterBtn) switchToRegisterBtn.addEventListener('click', e => {
        e.preventDefault(); closeLoginModal(); showRegisterModal();
    });

    if (switchToLoginBtn) switchToLoginBtn.addEventListener('click', e => {
        e.preventDefault(); closeRegisterModal(); showLoginModal();
    });

    // Close modals on outside click
    window.addEventListener('click', (event) => {
        if (event.target.id === 'loginModal') closeLoginModal();
        if (event.target.id === 'registerModal') closeRegisterModal();
    });

    // Login Handler
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            try {
                await signInWithEmailAndPassword(auth, email, password);
                closeLoginModal();
                loginForm.reset();
                window.location.href = 'dashboard.html';
            } catch (error) {
                let msg = 'Login failed. Please try again.';
                if (error.code === 'auth/invalid-email') msg = 'Invalid email address.';
                else if (error.code === 'auth/wrong-password') msg = 'Incorrect password.';
                else if (error.code === 'auth/user-not-found') msg = 'User not found.';
                Swal.fire({ icon: 'error', title: 'Login Failed', text: msg });
            }
        });
    }

    // Registration Handler
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('registerEmail').value.trim();
            const password = document.getElementById('registerPassword').value.trim();
            const fullName = document.getElementById('registerName').value.trim();
            const phoneNumber = document.getElementById('registerPhone').value.trim();
            const city = document.getElementById('registerCity').value.trim();
            const referredBy = document.getElementById('registerReferral').value.trim() || null;

            // Get register button for loading state
            const registerBtn = document.querySelector('#registerForm button[type="submit"]');

            // Show loading state
            if (registerBtn) {
                registerBtn.disabled = true;
                registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
            }

            try {
                // Check if phone number is already in use
                const usersRef = collection(db, "users");
                const phoneQuery = query(usersRef, where("phoneNumber", "==", phoneNumber));
                const phoneQuerySnapshot = await getDocs(phoneQuery);

                if (!phoneQuerySnapshot.empty) {
                    if (registerBtn) {
                        registerBtn.disabled = false;
                        registerBtn.innerHTML = 'Register';
                    }
                    Swal.fire({
                        icon: 'error',
                        title: 'Registration Failed',
                        text: 'This phone number is already registered. Please use a different phone number.'
                    });
                    return;
                }

                // Check if email is already in use
                const emailQuery = query(usersRef, where("email", "==", email.toLowerCase()));
                const emailQuerySnapshot = await getDocs(emailQuery);

                if (!emailQuerySnapshot.empty) {
                    if (registerBtn) {
                        registerBtn.disabled = false;
                        registerBtn.innerHTML = 'Register';
                    }
                    Swal.fire({
                        icon: 'error',
                        title: 'Registration Failed',
                        text: 'This email address is already registered. Please use a different email address.'
                    });
                    return;
                }

                // Update loading state
                if (registerBtn) {
                    registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Registering...';
                }

                const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;

                const referralCode = await generateReferralCode();

                const userRef = doc(db, 'users', user.uid);
                await setDoc(userRef, {
                    email,
                    fullName,
                    phoneNumber,
                    city,
                    referredBy,
                    referralCode,
                    createdAt: serverTimestamp(),
                    lastLogin: serverTimestamp(),

                    plan: {
                        name: 'Trial',
                        dailyLimit: 50,
                        rate: 0.2, // ₹10 for 50 videos = ₹0.2 per video
                        duration: 7, // 7 days trial period
                        earningsPerBatch: 10 // ₹10 for 50 videos
                    },

                    wallet: {
                        earning: 0,
                        bonus: 0,
                        balance: 0
                    },

                    stats: {
                        totalEarnings: 0,
                        referrals: 0,
                        totalVideosCompleted: 0,
                        dailyVideos: {
                            date: '',
                            count: 0,
                            submitted: false,
                            lastUpdated: serverTimestamp()
                        }
                    },

                    bankDetails: { // Direct field
                        account: '',
                        holder: '',
                        ifsc: '',
                        bank: ''
                    }
                });

                closeRegisterModal();
                registerForm.reset();

                Swal.fire({
                    icon: 'success',
                    title: 'Registration Successful!',
                    text: 'You can now login to your account.',
                    showConfirmButton: true
                }).then(() => {
                    document.getElementById('registerModal').style.display = 'none';
                    document.getElementById('loginModal').style.display = 'block';
                });

            } catch (error) {
                let message = 'An error occurred during registration.';
                if (error.code === 'auth/email-already-in-use') message = 'This email is already registered.';
                else if (error.code === 'auth/invalid-email') message = 'Invalid email address.';
                else if (error.code === 'auth/weak-password') message = 'Password is too weak.';
                Swal.fire({ icon: 'error', title: 'Registration Failed', text: message });

                // Reset button state
                if (registerBtn) {
                    registerBtn.disabled = false;
                    registerBtn.innerHTML = 'Register';
                }
            }
        });
    }

    // Auth State Change
    onAuthStateChanged(auth, (user) => {
        if (loginBtn) {
            loginBtn.textContent = user ? 'Dashboard' : 'Login';
            loginBtn.onclick = (e) => {
                e.preventDefault();
                window.location.href = user ? 'dashboard.html' : 'login.html';
            };
        }
    });
});
