import { auth, db, collection, query, where, orderBy, limit, getDocs, startAfter } from './firebase-config.js'; // Import from your Firebase config file
import { standardizeTransactionData, formatCurrency } from './user-data-utils.js';
import { fetchUserTransactions } from './transaction-data-utils.js';
import { getCachedTransactions, cacheAllTransactions } from './transaction-cache.js';
import { setupPageTransitionHandlers } from './page-transition.js';
import { updateLastLoaded, wasRecentlyLoaded, cameFromPage, DATA_TYPES, MAX_AGE } from './shared-data-store.js';

// DOM Elements
const transactionsList = document.getElementById('transactionsList');
const typeFilter = document.getElementById('typeFilter');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const noTransactionsEl = document.querySelector('.no-transactions');

// State variables
let lastDoc = null;
let isLoading = false;

// Set up page transition handlers
setupPageTransitionHandlers('transactions');

// Initialize page on authentication
auth.onAuthStateChanged(async (user) => {
    if (!user) {
        window.location.href = 'index.html';
        return;
    }
    console.log('User authenticated:', user.uid);
    await initializePage();
});

// Initialize page and load initial transactions
async function initializePage() {
    // Reset state for initial load
    lastDoc = null;
    transactionsList.innerHTML = '';
    loadMoreBtn.style.display = 'none';

    if (noTransactionsEl) {
        noTransactionsEl.style.display = 'none';
    }

    await loadTransactions();
}

// Load transactions from Firestore based on filters
async function loadTransactions(isLoadMore = false) {
    if (isLoading) return;
    isLoading = true;

    try {
        const userId = auth.currentUser.uid;
        console.log('Loading transactions for user:', userId);
        const typeFilterValue = typeFilter.value;
        console.log('Current filter value:', typeFilterValue);

        // Show loading state
        if (!isLoadMore) {
            transactionsList.innerHTML = `
                <div class="loading-transactions">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading transactions...</p>
                </div>
            `;
        }

        // If this is not a "load more" request, check if we can use cached data
        let transactions = [];

        if (!isLoadMore) {
            // Check if we recently came from wallet or dashboard page
            if (cameFromPage('wallet', MAX_AGE.MEDIUM) || cameFromPage('dashboard', MAX_AGE.MEDIUM)) {
                console.log('Recently came from wallet or dashboard, checking for cached transactions');

                // Try to get from cache
                const cachedTransactions = getCachedTransactions(userId);
                if (cachedTransactions && cachedTransactions.length > 0) {
                    console.log(`Using ${cachedTransactions.length} cached transactions`);
                    transactions = cachedTransactions;

                    // Update last loaded timestamp
                    updateLastLoaded(DATA_TYPES.TRANSACTIONS);

                    // Skip Firebase read
                    displayTransactions(transactions, isLoadMore);
                    return;
                }
            }

            // Check if transactions were recently loaded
            if (wasRecentlyLoaded(DATA_TYPES.TRANSACTIONS, MAX_AGE.MEDIUM)) {
                console.log('Transactions were recently loaded, checking for cached data');

                // Try to get from cache
                const cachedTransactions = getCachedTransactions(userId);
                if (cachedTransactions && cachedTransactions.length > 0) {
                    console.log(`Using ${cachedTransactions.length} cached transactions`);
                    transactions = cachedTransactions;

                    // Skip Firebase read
                    displayTransactions(transactions, isLoadMore);
                    return;
                }
            }
        }

        // No cache or load more request, fetch from Firebase
        console.log('Fetching transactions from Firebase');

        // Use our centralized transaction utility to fetch transactions
        transactions = await fetchUserTransactions(db, userId, {
            maxResults: 50, // Increased from 20 to show more transactions
            typeFilter: typeFilterValue !== 'all' ? typeFilterValue : null,
            lastDoc: isLoadMore ? lastDoc : null
        });

        console.log(`Fetched ${transactions.length} transactions from Firebase`);

        // Cache transactions if this is not a load more request
        if (!isLoadMore && transactions.length > 0) {
            cacheAllTransactions(userId, transactions);
            updateLastLoaded(DATA_TYPES.TRANSACTIONS);
        }

        // Display the transactions
        displayTransactions(transactions, isLoadMore);

    } catch (error) {
        console.error('Error loading transactions:', error);
        if (!isLoadMore) {
            if (noTransactionsEl) {
                noTransactionsEl.style.display = 'block';
            } else {
                transactionsList.innerHTML = `
                    <div class="no-transactions">
                        <i class="fas fa-exclamation-circle"></i>
                        <p>Error loading transactions: ${error.message || 'Unknown error'}</p>
                    </div>
                `;
            }
        }
    } finally {
        isLoading = false;
    }
}

// Display transactions in the UI
function displayTransactions(transactions, isLoadMore = false) {
    // Log transaction types for this user
    if (transactions.length > 0) {
        const userTypeCount = {};
        transactions.forEach(t => {
            const type = t.type || 'unknown';
            userTypeCount[type] = (userTypeCount[type] || 0) + 1;
        });
        console.log('Transaction types for this user:', userTypeCount);
    }

    // Clear existing content if not loading more
    if (!isLoadMore) {
        transactionsList.innerHTML = '';
    }

    // Show no transactions message if needed
    if (transactions.length === 0) {
        if (!isLoadMore) {
            if (noTransactionsEl) {
                noTransactionsEl.style.display = 'block';
            } else {
                transactionsList.innerHTML = `
                    <div class="no-transactions">
                        <i class="fas fa-receipt"></i>
                        <p>No transactions found</p>
                    </div>
                `;
            }
            loadMoreBtn.style.display = 'none';
        }
        return;
    } else if (noTransactionsEl) {
        noTransactionsEl.style.display = 'none';
    }

    // Create and append transaction elements
    transactions.forEach((transaction) => {
        try {
            const transactionElement = createTransactionElement(transaction);
            transactionsList.appendChild(transactionElement);
        } catch (error) {
            console.error('Error creating transaction element:', error, transaction);
        }
    });

    // Update last document for pagination
    if (transactions.length > 0) {
        // Store the last transaction for pagination
        lastDoc = transactions[transactions.length - 1];
        console.log('Last transaction for pagination:', lastDoc.id);

        // Show load more button if we have enough transactions
        if (transactions.length >= 10) {
            loadMoreBtn.style.display = 'block';
        } else {
            console.log('Fewer than 10 transactions, hiding load more button');
            loadMoreBtn.style.display = 'none';
        }
    } else {
        lastDoc = null;
        loadMoreBtn.style.display = 'none';
    }
}

// Create the HTML element for a single transaction
function createTransactionElement(transaction) {
    try {
        // Transaction should already be standardized by fetchUserTransactions
        // but we'll check and standardize if needed
        if (!transaction._standardized) {
            transaction = standardizeTransactionData(transaction);
        }

        const div = document.createElement('div');
        div.className = 'transaction-item';

        // Format the date using standardized timestamp
        let formattedDate = 'Unknown date';
        try {
            if (transaction.timestamp) {
                formattedDate = transaction.timestamp.toLocaleDateString('en-IN', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                });
            }
        } catch (dateError) {
            console.error('Error formatting date:', dateError);
            // Try a fallback date format
            try {
                formattedDate = new Date(transaction.timestamp).toLocaleDateString();
            } catch (fallbackError) {
                console.error('Fallback date formatting failed:', fallbackError);
            }
        }

        // Determine amount class based on transaction type
        let amountClass = '';
        const lowerType = transaction.type.toLowerCase();

        // Positive transaction types (money in)
        if (['earnings', 'credit', 'referral', 'referral_bonus', 'work', 'translation', 'translations',
             'translation earnings', 'translation_earnings', 'bonus', 'bonus_credit'].includes(lowerType)) {
            amountClass = 'success';
        }
        // Negative transaction types (money out)
        else if (['withdrawal', 'debit', 'withdraw', 'transfer-earning', 'transfer-bonus', 'plan',
                  'transfer_earning', 'transfer_bonus', 'transfer', 'wallet_transfer'].includes(lowerType)) {
            amountClass = 'error';
        }
        // Default based on amount
        else {
            amountClass = transaction.amount > 0 ? 'success' : 'error';
        }

        // Format the status with first letter capitalized
        let formattedStatus = 'Unknown';
        if (transaction.status && typeof transaction.status === 'string') {
            formattedStatus = transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1);
        }

        // Format the amount with currency symbol
        let formattedAmount = '₹0.00';
        try {
            const sign = transaction.amount > 0 ? '+' : (transaction.amount < 0 ? '-' : '');
            formattedAmount = `${sign}${formatCurrency(Math.abs(transaction.amount))}`;
        } catch (amountError) {
            console.error('Error formatting amount:', amountError);
        }

        div.innerHTML = `
            <div class="transaction-date" style="color: #000000 !important;">${formattedDate}</div>
            <div class="transaction-type" style="color: #000000 !important;">${transaction.typeDisplay || 'Unknown'}</div>
            <div class="transaction-status ${transaction.status || 'unknown'}" style="color: #000000 !important;">${formattedStatus}</div>
            <div class="transaction-amount ${amountClass}" style="color: #000000 !important;">${formattedAmount}</div>
        `;

        return div;
    } catch (error) {
        console.error('Error creating transaction element:', error, transaction);

        // Create a fallback element with basic information
        const fallbackDiv = document.createElement('div');
        fallbackDiv.className = 'transaction-item';
        fallbackDiv.innerHTML = `
            <div class="transaction-date" style="color: #000000 !important;">Unknown date</div>
            <div class="transaction-type" style="color: #000000 !important;">Transaction</div>
            <div class="transaction-status unknown" style="color: #000000 !important;">Unknown</div>
            <div class="transaction-amount" style="color: #000000 !important;">₹0.00</div>
        `;
        return fallbackDiv;
    }
}

// Apply filters and reload transactions
async function applyFilters() {
    lastDoc = null;
    transactionsList.innerHTML = '';
    loadMoreBtn.style.display = 'none';
    if (noTransactionsEl) {
        noTransactionsEl.style.display = 'none';
    }
    await loadTransactions();
}

// Load more transactions when the button is clicked
async function loadMoreTransactions() {
    await loadTransactions(true);
}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    typeFilter.addEventListener('change', applyFilters);
    loadMoreBtn.addEventListener('click', loadMoreTransactions);

    // Add event listener for the apply filters button
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', applyFilters);
    }
});