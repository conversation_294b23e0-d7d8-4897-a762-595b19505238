<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Plans</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #ff0000;
        }
        button {
            background-color: #ff0000;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 20px;
        }
        .plan-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .plan-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Update Plans in Firebase</h1>
    <p>This tool will update existing plans in Firebase with the new fields for video quality, referral bonuses, and short video days.</p>

    <div id="plans-container">Loading plans...</div>

    <button id="update-button">Update All Plans</button>
    <div id="result"></div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
        import { getFirestore, collection, getDocs, doc, updateDoc } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

        // Your Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyAMMabAMVSrNzKdTEVOcn4Ffh7oDVxD2EM",
            authDomain: "mytube-earnings.firebaseapp.com",
            projectId: "mytube-earnings",
            storageBucket: "mytube-earnings.firebasestorage.app",
            messagingSenderId: "116772605182",
            appId: "1:116772605182:web:a5e9875d48867cca03e9af",
            measurementId: "G-MR5HFN8J4V"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Default values for each plan
        const planDefaults = {
            'Trial': {
                videoQuality: 'Standard',
                referralBonus: 0,
                referralVideosCount: 0,
                referralWalletBonus: 0,
                shortVideoDays: 2
            },
            'Starter': {
                videoQuality: 'HD',
                referralBonus: 50,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7
            },
            'Premium': {
                videoQuality: 'Full HD',
                referralBonus: 300,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7
            },
            'Elite': {
                videoQuality: 'Ultra HD',
                referralBonus: 500,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7
            },
            'Ultimate': {
                videoQuality: 'Ultra HD+',
                referralBonus: 1000,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7
            }
        };

        // Get plans from Firestore
        async function getPlans() {
            const plansContainer = document.getElementById('plans-container');

            try {
                const plansCollection = collection(db, 'plans');
                const plansSnapshot = await getDocs(plansCollection);

                if (plansSnapshot.empty) {
                    plansContainer.innerHTML = '<p>No plans found in Firebase.</p>';
                    return [];
                }

                let plansHTML = '';
                const plans = [];

                plansSnapshot.forEach((doc) => {
                    const plan = { id: doc.id, ...doc.data() };
                    plans.push(plan);

                    // Create HTML for each plan
                    plansHTML += `
                        <div class="plan-card">
                            <div class="plan-title">${plan.name || 'Unnamed Plan'}</div>
                            <p><strong>Price:</strong> ₹${plan.price || 0}</p>
                            <p><strong>Duration:</strong> ${plan.duration || 0} days</p>
                            <p><strong>Earnings per Batch:</strong> ₹${plan.earningsPerBatch || 0}</p>
                            <p><strong>Video Quality:</strong> ${plan.videoQuality || 'Not set'}</p>
                            <p><strong>Referral Bonus:</strong> ₹${plan.referralBonus || 0}</p>
                            <p><strong>Referral Videos Count:</strong> ${plan.referralVideosCount || 0}</p>
                            <p><strong>Referral Wallet Bonus:</strong> ₹${plan.referralWalletBonus || 0}</p>
                            <p><strong>Short Video Days:</strong> ${plan.shortVideoDays || 0}</p>
                        </div>
                    `;
                });

                plansContainer.innerHTML = plansHTML;
                return plans;
            } catch (error) {
                console.error('Error getting plans:', error);
                plansContainer.innerHTML = `<p class="error">Error getting plans: ${error.message}</p>`;
                return [];
            }
        }

        // Update plans with new fields
        async function updatePlans(plans) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Updating plans...</p>';

            try {
                let updatedCount = 0;

                for (const plan of plans) {
                    // Get default values based on plan name
                    const defaults = planDefaults[plan.name] || planDefaults['Trial'];

                    // Only update if fields are missing
                    const updates = {};

                    if (!plan.videoQuality) {
                        updates.videoQuality = defaults.videoQuality;
                    }

                    if (plan.referralBonus === undefined) {
                        updates.referralBonus = defaults.referralBonus;
                    }

                    if (plan.referralVideosCount === undefined) {
                        updates.referralVideosCount = defaults.referralVideosCount;
                    }

                    if (plan.referralWalletBonus === undefined) {
                        updates.referralWalletBonus = defaults.referralWalletBonus;
                    }

                    if (plan.shortVideoDays === undefined) {
                        updates.shortVideoDays = defaults.shortVideoDays;
                    }

                    // Only update if there are changes to make
                    if (Object.keys(updates).length > 0) {
                        await updateDoc(doc(db, 'plans', plan.id), updates);
                        updatedCount++;
                    }
                }

                resultDiv.innerHTML = `<p class="success">Successfully updated ${updatedCount} plans. Please refresh the page to see the changes.</p>`;

                // Add refresh button
                const refreshButton = document.createElement('button');
                refreshButton.textContent = 'Refresh Page';
                refreshButton.onclick = () => location.reload();
                resultDiv.appendChild(refreshButton);

            } catch (error) {
                console.error('Error updating plans:', error);
                resultDiv.innerHTML = `<p class="error">Error updating plans: ${error.message}</p>`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            const plans = await getPlans();

            // Add event listener to update button
            document.getElementById('update-button').addEventListener('click', async () => {
                await updatePlans(plans);
            });
        });
    </script>
</body>
</html>
