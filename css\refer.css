:root {
    --primary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --text-light: #1f2937;
    --text-gray: #4b5563;
    --bg: #ffffff;
    --bg-light: rgba(34, 197, 94, 0.05);
    --glass-border: 1px solid rgba(34, 197, 94, 0.1);
}

body {
    background: var(--bg);
    color: var(--text-light);
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    width: 100%;
}

.page-container {
    min-height: 100vh;
    background-color: var(--bg);
    padding: 15px;
    padding-bottom: 85px; /* Space for mobile menu */
    box-sizing: border-box; /* Include padding in width calculation */
    width: 100%;
    max-width: 100vw; /* Never exceed viewport width */
}

.page-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    width: 100%;
}

.page-header h1 {
    font-size: 1.5rem;
    color: var(--text-light);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.back-button {
    background: rgba(34, 197, 94, 0.1);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.refer-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 100%;
}

/* Referral Stats */
.referral-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    width: 100%;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: var(--bg-light);
    border-radius: 15px;
    border: var(--glass-border);
    transition: all 0.3s ease;
    width: 100%;
    box-sizing: border-box;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: var(--text-light);
    flex-shrink: 0;
}

.stat-info {
    min-width: 0; /* Allow text truncation */
}

.stat-info h3 {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 600;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    white-space: nowrap;
}

/* Referral Code Section */
.referral-code-section {
    text-align: center;
    padding: 20px 15px;
    background: var(--bg-light);
    border-radius: 20px;
    border: var(--glass-border);
    width: 100%;
    box-sizing: border-box;
}

.referral-code {
    display: flex;
    justify-content: center;
    margin: 15px 0;
    width: 100%;
}

.referral-code span {
    font-size: 1.3rem;
    font-weight: 600;
    letter-spacing: 1px;
    color: var(--text-light);
    padding: 12px 20px;
    background: var(--bg-light);
    border-radius: 12px;
    border: var(--glass-border);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: inline-block;
}

.share-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.share-buttons button {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    background: var(--primary-gradient);
    color: white;
    cursor: pointer;
    transition: 0.3s ease;
}

/* How It Works */
.how-it-works {
    text-align: center;
    padding: 20px 0;
    width: 100%;
}

.steps-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 20px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: var(--bg-light);
    border-radius: 15px;
    border: var(--glass-border);
}

.step-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-light);
}

.step h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-light);
}

.step p {
    margin: 0;
    color: var(--text-gray);
    font-size: 0.85rem;
    text-align: center;
}

/* Referral History */
.referral-history {
    margin-top: 20px;
    background: var(--bg-light);
    border-radius: 20px;
    padding: 20px 15px;
    border: var(--glass-border);
    width: 100%;
    box-sizing: border-box;
}

.referral-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
    width: 100%;
}

.referral-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-bottom: var(--glass-border);
    width: 100%;
}

.referral-user {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    flex-shrink: 0;
}

.user-info {
    min-width: 0;
}

.user-info h4 {
    margin: 0;
    font-size: 0.95rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-info p {
    margin: 0.2rem 0 0;
    font-size: 0.8rem;
    color: var(--text-gray);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.referral-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    background: var(--bg-light);
    color: var(--text-light);
    order: 3;
    width: 100%;
    justify-content: center;
    margin-top: 8px;
}

.referral-reward {
    font-size: 1rem;
    font-weight: 600;
    color: #4caf50;
    order: 2;
    text-align: center;
    width: 100%;
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #1a1f2e;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    padding: 12px 0;
    border-top: 2px solid #2d3748;
    display: flex;
    justify-content: space-around;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #ffffff;
    font-size: 0.7rem;
    padding: 8px 4px;
    border-radius: 8px;
    transition: all 0.3s ease;
    width: 60px;
    text-align: center;
}

.mobile-nav-item i {
    font-size: 1.4rem;
    margin-bottom: 4px;
    color: #ffffff;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
    .page-container {
        padding: 20px;
    }

    .referral-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .steps-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .share-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .share-buttons button {
        width: auto;
        min-width: 200px;
    }

    .mobile-menu {
        display: none;
    }
}

@media (min-width: 1024px) {
    .referral-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .steps-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}