// Admin Users Module
import { auth, db, showLoading, hideLoading } from './admin-auth.js';

// Import Firebase Auth functions for password update
import {
    getAuth,
    updatePassword,
    EmailAuthProvider,
    reauthenticateWithCredential,
    updateEmail
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";

// Import the referral bonus processing function
import { processReferralBonus, normalizePlanName } from "./referral-bonus.js";
import {
    collection,
    query,
    getDocs,
    orderBy,
    limit,
    where,
    Timestamp,
    doc,
    getDoc,
    updateDoc,
    addDoc,
    deleteDoc,
    serverTimestamp,
    setDoc,
    startAfter
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import {
    formatDateTime,
    showModal,
    hideModal,
    getUserDetails
} from './admin.js';

// Import standardized user data utilities
import { standardizeUserData, formatCurrency, formatDate } from './user-data-utils.js';

// Import active days utilities
import { getValidatedActiveDays, getCompletePlanInfo } from './active-days-utils.js';

// Import cache utilities to clear cache when needed
import { clearAllCache } from './cache-utils.js';

// DOM Elements
const usersTable = document.getElementById('users-table');
const userSearch = document.getElementById('user-search');
const statusFilter = document.getElementById('status-filter');
const planFilter = document.getElementById('plan-filter');
const exportUsersBtn = document.getElementById('export-users-btn');
const prevPageBtn = document.getElementById('prev-page');
const nextPageBtn = document.getElementById('next-page');
const pageInfoElement = document.getElementById('page-info');

// Modal Elements
const userModal = document.getElementById('user-modal');
const modalUserId = document.getElementById('modal-user-id');
const modalUserName = document.getElementById('modal-user-name');
const modalUserEmail = document.getElementById('modal-user-email');
const modalUserMobile = document.getElementById('modal-user-mobile');
const modalUserPlan = document.getElementById('modal-user-plan');
const modalUserStatus = document.getElementById('modal-user-status');
const modalUserJoined = document.getElementById('modal-user-joined');
const modalUserReferralCode = document.getElementById('modal-user-referral-code');
const modalUserReferredBy = document.getElementById('modal-user-referred-by');
const modalUserWalletBalance = document.getElementById('modal-user-wallet-balance');
const modalUserEarnings = document.getElementById('modal-user-earnings');
const modalUserTranslations = document.getElementById('modal-user-translations');
const modalUserLastLogin = document.getElementById('modal-user-last-login');
const modalPlanExpiry = document.getElementById('modal-plan-expiry');

// Form Elements
const editUserForm = document.getElementById('edit-user-form');
const editUserNameInput = document.getElementById('edit-user-name');
const editUserEmailInput = document.getElementById('edit-user-email');
const editUserMobileInput = document.getElementById('edit-user-mobile');
const editUserReferralInput = document.getElementById('edit-user-referral');
const editUserPlanSelect = document.getElementById('edit-user-plan');
const editUserPlanDuration = document.getElementById('edit-user-plan-duration');
const editUserEarningsPerBatch = document.getElementById('edit-user-earnings-per-batch');
const editUserVideoDuration = document.getElementById('edit-user-video-duration');
const editShortVideoToggle = document.getElementById('edit-short-video');
const shortVideoDurationContainer = document.getElementById('short-video-duration-container');
const shortVideoDurationInput = document.getElementById('short-video-duration-days');
const updateUserBtn = document.getElementById('update-user-btn');
const cancelEditBtn = document.getElementById('cancel-edit-btn');

// Plan Form Elements
const activatePlanForm = document.getElementById('activate-plan-form');
const planSelect = document.getElementById('plan-select');
const planDuration = document.getElementById('plan-duration');
const planEarningsPerBatch = document.getElementById('plan-earnings-per-batch');
const confirmPlanBtn = document.getElementById('confirm-plan-btn');
const cancelPlanBtn = document.getElementById('cancel-plan-btn');

// Store plans data globally
let availablePlans = [];

// Action Buttons
const editUserBtn = document.getElementById('edit-user-btn');
const activatePlanBtn = document.getElementById('activate-plan-btn');
const blockUserBtn = document.getElementById('block-user-btn');
const unblockUserBtn = document.getElementById('unblock-user-btn');

// State variables
let currentUsers = [];
let lastVisible = null;
let currentPage = 1;
let totalPages = 1;
let itemsPerPage = 10;
let currentFilters = {
    status: 'all',
    plan: 'all',
    search: ''
};
let currentUserId = null;

// Define table headers and corresponding data fields
const TABLE_HEADERS = {
    'Name': 'fullName',
    'Email': 'email',
    'Plan': 'planName',
    'Videos': 'totalTranslations',
    'Last Active': 'lastActive',
    'Status': 'status',
    'Actions': null // For action buttons
};

// Note: This function was part of the old implementation and is no longer used

// Note: The loadUsers function is defined below with more features

// Note: These helper functions were part of the old implementation and are no longer used

// Note: getUserDetails function is imported from admin.js

// Setup mobile table scrolling enhancements
function setupMobileTableScrolling() {
    // Get all table containers
    const tableContainers = document.querySelectorAll('.admin-table-container');

    tableContainers.forEach(container => {
        // Add scroll event listener to show/hide scroll indicator
        container.addEventListener('scroll', function() {
            // Check if scrolled to the right edge
            const isScrolledToRight = this.scrollLeft + this.clientWidth >= this.scrollWidth - 10;

            // Add or remove a class based on scroll position
            if (isScrolledToRight) {
                this.classList.add('scrolled-to-end');
            } else {
                this.classList.remove('scrolled-to-end');
            }
        });

        // Add touch event listeners for better mobile scrolling
        let startX, scrollLeft;

        container.addEventListener('touchstart', function(e) {
            startX = e.touches[0].pageX - this.offsetLeft;
            scrollLeft = this.scrollLeft;
        }, { passive: true });

        container.addEventListener('touchmove', function(e) {
            if (!startX) return;

            const x = e.touches[0].pageX - this.offsetLeft;
            const walk = (x - startX) * 2; // Scroll speed multiplier
            this.scrollLeft = scrollLeft - walk;
        }, { passive: true });

        container.addEventListener('touchend', function() {
            startX = null;
        }, { passive: true });
    });
}

// Load plans from Firebase
async function loadPlans() {
    try {
        // Get plans from Firestore
        const plansQuery = query(collection(db, 'plans'), orderBy('name'));
        const snapshot = await getDocs(plansQuery);

        // Clear existing plans
        availablePlans = [];

        // Populate plan filter dropdown
        if (planFilter) {
            // Keep the 'All Plans' option
            planFilter.innerHTML = '<option value="all">All Plans</option>';
        }

        // Populate edit user plan dropdown
        if (editUserPlanSelect) {
            editUserPlanSelect.innerHTML = '<option value="">Select a plan</option>';
        }

        // Populate activate plan dropdown
        if (planSelect) {
            planSelect.innerHTML = '<option value="">Select a plan</option>';
        }

        // Process plans
        snapshot.forEach(doc => {
            const plan = { id: doc.id, ...doc.data() };
            availablePlans.push(plan);

            // Add to plan filter dropdown
            if (planFilter) {
                const option = document.createElement('option');
                option.value = plan.name;
                option.textContent = plan.name;
                planFilter.appendChild(option);
            }

            // Add to edit user plan dropdown
            if (editUserPlanSelect) {
                const option = document.createElement('option');
                option.value = plan.id;
                option.textContent = plan.name;
                option.dataset.earnings = plan.earningsPerBatch || 0;
                option.dataset.duration = plan.duration || 30;
                editUserPlanSelect.appendChild(option);
            }

            // Add to activate plan dropdown
            if (planSelect) {
                const option = document.createElement('option');
                option.value = plan.id;
                option.textContent = plan.name;
                option.dataset.earnings = plan.earningsPerBatch || 0;
                option.dataset.duration = plan.duration || 30;
                planSelect.appendChild(option);
            }
        });

        // Add event listeners for plan selection changes
        if (editUserPlanSelect) {
            editUserPlanSelect.addEventListener('change', updatePlanEarnings);
        }

        if (planSelect) {
            planSelect.addEventListener('change', updateActivatePlanEarnings);
        }

    } catch (error) {
        console.error('Error loading plans:', error);
        throw error;
    }
}

// Update earnings per batch based on selected plan
function updatePlanEarnings() {
    if (!editUserPlanSelect || !editUserEarningsPerBatch || !editUserPlanDuration) return;

    const selectedOption = editUserPlanSelect.options[editUserPlanSelect.selectedIndex];
    if (selectedOption && selectedOption.dataset.earnings) {
        editUserEarningsPerBatch.value = selectedOption.dataset.earnings;
        editUserPlanDuration.value = selectedOption.dataset.duration || 30;
    } else {
        editUserEarningsPerBatch.value = '';
        editUserPlanDuration.value = 30;
    }
}

// Update earnings per batch for activate plan form
function updateActivatePlanEarnings() {
    if (!planSelect || !planEarningsPerBatch || !planDuration) return;

    const selectedOption = planSelect.options[planSelect.selectedIndex];
    if (selectedOption && selectedOption.dataset.earnings) {
        planEarningsPerBatch.value = selectedOption.dataset.earnings;
        planDuration.value = selectedOption.dataset.duration || 30;
    } else {
        planEarningsPerBatch.value = '';
        planDuration.value = 30;
    }
}

// Helper function to verify admin status
async function verifyAdminStatus(action = 'perform this action') {
    try {
        if (!auth.currentUser) {
            console.error('No user is currently signed in');
            hideLoading();
            Swal.fire({
                icon: 'error',
                title: 'Authentication Error',
                text: 'You are not signed in. Please log in again.'
            });
            return false;
        }

        console.log('Verifying admin status for user:', auth.currentUser.uid);

        // First, verify that the current user is an admin
        const adminRef = doc(db, 'admins', auth.currentUser.uid);
        const adminSnap = await getDoc(adminRef);

        if (adminSnap.exists()) {
            console.log('User verified as admin in admins collection');
            return true;
        }

        // Double-check in users collection with admin role
        const userRef = doc(db, 'users', auth.currentUser.uid);
        const userSnap = await getDoc(userRef);

        if (userSnap.exists() && userSnap.data().role === 'admin') {
            console.log('User verified as admin in users collection');
            return true;
        }

        // Not an admin
        console.warn('User is not an admin:', auth.currentUser.uid);
        hideLoading();
        Swal.fire({
            icon: 'error',
            title: 'Permission Denied',
            text: `You do not have permission to ${action}.`
        });
        return false;
    } catch (error) {
        console.error('Error verifying admin status:', error);
        hideLoading();
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to verify admin permissions: ' + error.message
        });
        return false;
    }
}

// Global variable to store the current index URL
let currentIndexUrl = '';

// Setup event listeners
function setupEventListeners() {
    // Filter change events
    if (statusFilter) {
        statusFilter.addEventListener('change', handleFilterChange);
    }

    if (planFilter) {
        planFilter.addEventListener('change', handleFilterChange);
    }

    // Search input event
    if (userSearch) {
        userSearch.addEventListener('input', debounce(handleFilterChange, 500));
    }

    // Export button with enhanced error handling
    if (exportUsersBtn) {
        exportUsersBtn.addEventListener('click', async function(event) {
            event.preventDefault();
            console.log('Users export button clicked');

            // Check if XLSX library is available
            if (typeof XLSX === 'undefined') {
                console.error('XLSX library not available');
                Swal.fire({
                    icon: 'error',
                    title: 'Export Library Missing',
                    text: 'Excel export library is not loaded. Please refresh the page and try again.',
                    footer: 'If the problem persists, check your internet connection.'
                });
                return;
            }

            // Check if user is authenticated
            if (!auth?.currentUser) {
                console.error('User not authenticated');
                Swal.fire({
                    icon: 'error',
                    title: 'Authentication Required',
                    text: 'Please log in to export data.'
                });
                return;
            }

            try {
                await exportUsers();
            } catch (error) {
                console.error('Error in export function:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Export Failed',
                    text: 'Failed to export users data: ' + error.message
                });
            }
        });
        console.log('Users export button event listener attached successfully');
    } else {
        console.error('Users export button not found in DOM');
    }

    // Add test export function to window for debugging
    window.testUsersExportSimple = function() {
        console.log('Testing simple users export...');
        try {
            if (typeof XLSX === 'undefined') {
                throw new Error('XLSX library not available');
            }

            const testData = [
                { name: 'Test User 1', email: '<EMAIL>', plan: 'Trial', wallet: 100 },
                { name: 'Test User 2', email: '<EMAIL>', plan: 'Starter', wallet: 250 }
            ];

            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(testData);
            XLSX.utils.book_append_sheet(wb, ws, 'Test Users');

            const filename = `test_users_export_${Date.now()}.xlsx`;
            XLSX.writeFile(wb, filename);

            console.log('Simple users export test successful');
            alert('Simple users export test successful! Check your downloads.');
        } catch (error) {
            console.error('Simple users export test failed:', error);
            alert('Simple users export test failed: ' + error.message);
        }
    };

    // Create index button
    const createIndexBtn = document.getElementById('create-index-btn');
    if (createIndexBtn) {
        createIndexBtn.addEventListener('click', function() {
            if (currentIndexUrl) {
                // Open the index creation URL in a new tab
                window.open(currentIndexUrl, '_blank');

                Swal.fire({
                    icon: 'info',
                    title: 'Creating Index',
                    html: `
                        <p>The index creation page has been opened in a new tab.</p>
                        <p>Click "Create" on that page to create the index.</p>
                        <p>After creating the index, it will take a few minutes to build.</p>
                        <p>You can try filtering again in a few minutes.</p>
                    `,
                    showConfirmButton: true,
                    confirmButtonText: 'OK'
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Index URL',
                    text: 'No index URL is currently available. Try filtering by plan first to generate the URL.',
                    showConfirmButton: true,
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    // Pagination buttons
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', goToPreviousPage);
    }

    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', goToNextPage);
    }

    // Modal action buttons
    if (editUserBtn) {
        editUserBtn.addEventListener('click', showEditUserForm);
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', hideEditUserForm);
    }

    if (updateUserBtn) {
        updateUserBtn.addEventListener('click', updateUser);
    }

    if (activatePlanBtn) {
        activatePlanBtn.addEventListener('click', showActivatePlanForm);
    }

    if (cancelPlanBtn) {
        cancelPlanBtn.addEventListener('click', hideActivatePlanForm);
    }

    if (confirmPlanBtn) {
        confirmPlanBtn.addEventListener('click', activatePlan);
    }

    if (blockUserBtn) {
        blockUserBtn.addEventListener('click', blockUser);
    }

    if (unblockUserBtn) {
        unblockUserBtn.addEventListener('click', unblockUser);
    }

    // Process referral bonus button
    const processReferralBtn = document.getElementById('process-referral-btn');
    if (processReferralBtn) {
        processReferralBtn.addEventListener('click', processReferralBonusManually);
    }

    // Short video advantage toggle
    if (editShortVideoToggle) {
        editShortVideoToggle.addEventListener('change', function() {
            if (shortVideoDurationContainer) {
                shortVideoDurationContainer.style.display = this.checked ? 'block' : 'none';
            }
        });
    }
}

// Load users based on filters
async function loadUsers(isNextPage = false) {
    console.log('Loading users, isNextPage:', isNextPage);
    try {
        if (!isNextPage) {
            lastVisible = null;
            currentPage = 1;
        }
        console.log('Current filters:', currentFilters);

        // Build query based on filters
        let usersQuery = buildUsersQuery(isNextPage);

        // Flag to track if we're using client-side sorting
        let usingClientSideSort = false;
        let needsClientSideSort = false;

        // Check if we're filtering by plan (which might need client-side sorting)
        if (currentFilters.plan !== 'all') {
            needsClientSideSort = true;
        }

        // Check if we need to do client-side search
        const hasSearchTerm = currentFilters.search && currentFilters.search.trim() !== '';

        // Execute query
        const snapshot = await getDocs(usersQuery);

        // If we need client-side sorting and the query didn't include orderBy
        if (needsClientSideSort && !usersQuery._query.filters.some(f => f.field.segments[0] === 'createdAt')) {
            usingClientSideSort = true;
        }

        // Update pagination state
        if (!isNextPage) {
            // Get total count for pagination
            const countQuery = buildUsersQuery(false, true);
            const countSnapshot = await getDocs(countQuery);
            totalPages = Math.ceil(countSnapshot.size / itemsPerPage);
        } else {
            currentPage++;
        }

        // Update last visible for pagination
        if (!snapshot.empty) {
            lastVisible = snapshot.docs[snapshot.docs.length - 1];
        } else {
            lastVisible = null;
        }

        // Update UI
        updatePaginationUI();

        // If we're using client-side sorting or search, process the documents before rendering
        if (usingClientSideSort || hasSearchTerm) {
            // Convert snapshot to array for processing
            const docs = [];
            snapshot.forEach(doc => {
                docs.push(doc);
            });

            // Apply client-side sorting if needed
            if (usingClientSideSort) {
                console.log('Applying client-side sorting by createdAt');
                docs.sort((a, b) => {
                    // Get createdAt timestamps
                    const aTimestamp = a.data().createdAt;
                    const bTimestamp = b.data().createdAt;

                    // Handle missing timestamps
                    if (!aTimestamp) return 1;  // a comes after b
                    if (!bTimestamp) return -1; // a comes before b

                    // Convert to milliseconds for comparison
                    const aTime = aTimestamp instanceof Timestamp ?
                        aTimestamp.toMillis() :
                        (aTimestamp.seconds ? aTimestamp.seconds * 1000 : 0);

                    const bTime = bTimestamp instanceof Timestamp ?
                        bTimestamp.toMillis() :
                        (bTimestamp.seconds ? bTimestamp.seconds * 1000 : 0);

                    // Sort descending (newest first)
                    return bTime - aTime;
                });
                console.log('Client-side sorting complete');
            }

            // Apply search filter if needed
            let filteredDocs = docs;
            if (hasSearchTerm) {
                console.log('Applying client-side search filter:', currentFilters.search);
                const searchTerm = currentFilters.search.toLowerCase();

                filteredDocs = docs.filter(doc => {
                    const data = doc.data();
                    // Standardize user data to ensure consistent field access
                    const standardizedUser = standardizeUserData(data);

                    // Search in multiple fields
                    return (
                        (standardizedUser.fullName && standardizedUser.fullName.toLowerCase().includes(searchTerm)) ||
                        (standardizedUser.email && standardizedUser.email.toLowerCase().includes(searchTerm)) ||
                        (standardizedUser.phoneNumber && standardizedUser.phoneNumber.includes(searchTerm)) ||
                        (standardizedUser.referralCode && standardizedUser.referralCode.toLowerCase().includes(searchTerm)) ||
                        (standardizedUser.referredBy && standardizedUser.referredBy.toLowerCase().includes(searchTerm))
                    );
                });

                console.log(`Search found ${filteredDocs.length} matching users out of ${docs.length} total`);

                // Update pagination for search results
                totalPages = Math.ceil(filteredDocs.length / itemsPerPage);

                // Apply pagination to filtered results
                const startIndex = (currentPage - 1) * itemsPerPage;
                filteredDocs = filteredDocs.slice(startIndex, startIndex + itemsPerPage);
            }

            // Sort by createdAt in descending order
            filteredDocs.sort((a, b) => {
                const dateA = a.data().createdAt?.toDate() || new Date(0);
                const dateB = b.data().createdAt?.toDate() || new Date(0);
                return dateB - dateA;
            });

            // Create a custom snapshot-like object
            const processedSnapshot = {
                docs: filteredDocs,
                empty: filteredDocs.length === 0,
                size: filteredDocs.length,
                forEach: callback => filteredDocs.forEach(callback)
            };

            renderUsersTable(processedSnapshot);
        } else {
            renderUsersTable(snapshot);
        }

    } catch (error) {
        console.error('Error loading users:', error);

        // Check if this is a Firestore index error
        if (error.message && error.message.includes('index')) {
            // Extract the index creation URL from the error message if available
            let indexUrl = '';
            const urlMatch = error.message.match(/https:\/\/console\.firebase\.google\.com[^\s]+/);
            if (urlMatch) {
                indexUrl = urlMatch[0];

                // Store the URL globally so the Create Index button can use it
                currentIndexUrl = indexUrl;

                // Show the Create Index button
                const createIndexBtn = document.getElementById('create-index-btn');
                if (createIndexBtn) {
                    createIndexBtn.style.display = 'inline-block';
                }
            }

            Swal.fire({
                icon: 'warning',
                title: 'Index Required',
                html: `
                    <p>A database index is required for this filter combination.</p>
                    <p>You can create the index by clicking the "Create Index" button that has appeared in the filters section.</p>
                    <p>After creating the index, it will take a few minutes to build.</p>
                    <p>In the meantime, we'll try to show results using client-side sorting.</p>
                `,
                showConfirmButton: true,
                confirmButtonText: 'OK'
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to load users: ' + error.message,
            });
        }
    }
}

// Build users query based on filters
function buildUsersQuery(isNextPage = false, countOnly = false) {
    let baseQuery = collection(db, 'users');
    let conditions = [];
    let needsClientSideSearch = false;

    // Apply status filter
    if (currentFilters.status !== 'all') {
        conditions.push(where('status', '==', currentFilters.status));
    }

    // Apply plan filter
    if (currentFilters.plan !== 'all') {
        conditions.push(where('plan.name', '==', currentFilters.plan));
    }

    // Create query with conditions
    let usersQuery;

    // Check if we're filtering by plan (which requires a composite index)
    const hasPlanFilter = currentFilters.plan !== 'all';
    console.log('Has plan filter:', hasPlanFilter, 'Plan filter value:', currentFilters.plan);

    // Check if we have a search term
    const hasSearchTerm = currentFilters.search && currentFilters.search.trim() !== '';
    console.log('Has search term:', hasSearchTerm, 'Search term:', currentFilters.search);

    if (hasSearchTerm) {
        // We'll need to do client-side search since Firestore doesn't support text search across multiple fields
        needsClientSideSearch = true;
    }

    if (conditions.length > 0) {
        if (hasPlanFilter) {
            // If we're filtering by plan and the index isn't ready yet,
            // we'll fetch all documents that match the conditions and sort them in memory
            console.log('Attempting to build query with plan filter');
            try {
                // Try with the index first (this will work once the index is built)
                usersQuery = query(baseQuery, ...conditions, orderBy('createdAt', 'desc'));
                console.log('Successfully created query with plan filter and orderBy');

                // Hide the Create Index button if it was previously shown
                // since the index is now working
                const createIndexBtn = document.getElementById('create-index-btn');
                if (createIndexBtn) {
                    createIndexBtn.style.display = 'none';
                }

                // Clear the stored index URL since we don't need it anymore
                currentIndexUrl = '';
            } catch (indexError) {
                console.warn('Index not ready yet, using client-side sorting as fallback', indexError);
                // Fallback: don't use orderBy (no index needed)
                usersQuery = query(baseQuery, ...conditions);

                // We'll handle the index error message in the loadUsers function
                // to avoid showing multiple alerts
            }
        } else {
            // For other filters, we can use orderBy safely
            usersQuery = query(baseQuery, ...conditions, orderBy('createdAt', 'desc'));
        }
    } else {
        usersQuery = query(baseQuery, orderBy('createdAt', 'desc'));
    }

    // Add pagination if not counting and not doing client-side search
    if (!countOnly && !needsClientSideSearch) {
        if (isNextPage && lastVisible) {
            usersQuery = query(usersQuery, startAfter(lastVisible), limit(itemsPerPage));
        } else {
            usersQuery = query(usersQuery, limit(itemsPerPage));
        }
    }

    return usersQuery;
}

// Render users table
function renderUsersTable(snapshot) {
    console.log('Rendering users table, snapshot size:', snapshot.size);
    if (!usersTable) {
        console.error('usersTable element not found');
        return;
    }

    if (snapshot.empty) {
        console.log('Snapshot is empty, no users found');
        usersTable.innerHTML = `
            <tr>
                <td colspan="17" class="admin-empty-cell">No users found</td>
            </tr>
        `;
        currentUsers = [];
        return;
    }

    console.log('Found users, processing data...');

    // Process users data
    currentUsers = [];
    let tableHTML = '';

    snapshot.forEach(doc => {
        const rawUserData = doc.data();
        // Standardize user data to ensure consistent field access
        const standardizedUser = standardizeUserData(rawUserData);
        const user = { id: doc.id, ...rawUserData, _standardized: standardizedUser };
        currentUsers.push(user);

        // Format date
        const joinDate = formatDate(user.createdAt);

        // Format status
        const statusClass = standardizedUser.status === 'blocked' ? 'error' : 'success';

        // Get plan name from standardized data
        const planName = standardizedUser.plan.name;

        // Get videos count from standardized data (using totalVideosCompleted as source of truth)
        const videosCount = standardizedUser.stats.totalVideosCompleted || standardizedUser.stats.totalTranslationsCompleted || 'N/A'; // Try both fields for backward compatibility

        // Get today's videos count
        let todayVideosCount = 0;

        // Check if videos have been submitted for today
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        const videosSubmitted =
            (standardizedUser.stats?.dailyVideos?.submitted === true) ||
            (standardizedUser.stats?.dailyTranslations?.submitted === true) ||
            (standardizedUser.stats?.lastSubmission &&
             standardizedUser.stats.lastSubmission.submitted === true &&
             standardizedUser.stats.lastSubmission.count === 100 &&
             standardizedUser.stats.lastSubmission.date === today);

        // If videos have been submitted today, always show 100
        if (videosSubmitted) {
            todayVideosCount = 100;
            console.log(`User ${user.id} has submitted videos today, showing count as 100`);
        } else {
            // Otherwise check for today's videos in different possible locations
            if (standardizedUser.stats && standardizedUser.stats.todayVideos) {
                todayVideosCount = standardizedUser.stats.todayVideos;
            } else if (standardizedUser.stats && standardizedUser.stats.dailyVideos && standardizedUser.stats.dailyVideos.count) {
                todayVideosCount = standardizedUser.stats.dailyVideos.count;
            } else if (user.stats && user.stats.dailyVideos && user.stats.dailyVideos.count) {
                todayVideosCount = user.stats.dailyVideos.count;
            } else if (user.dailyVideos && typeof user.dailyVideos === 'object') {
                // Check if dailyVideos is at the root level and is an object (date-based structure)
                // Find the most recent date entry
                const dates = Object.keys(user.dailyVideos);
                if (dates.length > 0) {
                    // Sort dates in descending order (newest first)
                    dates.sort((a, b) => new Date(b) - new Date(a));
                    // Get the count from the most recent date
                    todayVideosCount = user.dailyVideos[dates[0]];
                }
            } else if (standardizedUser.stats && standardizedUser.stats.todayTranslations) {
                // Fallback to translation fields for backward compatibility
                todayVideosCount = standardizedUser.stats.todayTranslations;
            } else if (standardizedUser.stats && standardizedUser.stats.dailyTranslations && standardizedUser.stats.dailyTranslations.count) {
                todayVideosCount = standardizedUser.stats.dailyTranslations.count;
            } else if (user.stats && user.stats.dailyTranslations && user.stats.dailyTranslations.count) {
                todayVideosCount = user.stats.dailyTranslations.count;
            }
        }

        console.log(`User ${user.id} today's videos: ${todayVideosCount} (sources: standardized=${standardizedUser.stats?.todayVideos}, dailyVideos=${standardizedUser.stats?.dailyVideos?.count}, raw=${user.stats?.dailyVideos?.count})`);

        // Get active days using the utility function to ensure consistent handling for all plan types
        let activeDays = getValidatedActiveDays(user);

        // Log plan type and active days for debugging
        console.log(`User ${user.id} plan: ${planName}, active days: ${activeDays} (sources: direct=${user.activeDays}, stats=${user.stats?.activeDays}, validated=${activeDays})`);

        // Make sure we're displaying numbers, not undefined or null
        todayVideosCount = todayVideosCount || 0;
        activeDays = activeDays || 0;

        // Get earnings from standardized data
        const earnings = formatCurrency(standardizedUser.stats.totalEarnings);

        // Get referred by value directly from raw data if it exists and is not empty/invalid
        // This ensures we display exactly what's in the database
        let referredByValue;

        // Log the raw referredBy value for debugging
        console.log(`User ${user.id} raw referredBy value:`, user.referredBy,
                    `type: ${typeof user.referredBy},
                    exists: ${'referredBy' in user},
                    null: ${user.referredBy === null},
                    undefined: ${user.referredBy === undefined}`);

        // Special handling for numeric referredBy values
        if ('referredBy' in user &&
            user.referredBy !== null &&
            user.referredBy !== undefined) {

            // Convert to string and trim
            referredByValue = String(user.referredBy).trim();

            // Check if it's a valid referral code (not empty, not "0", not "ZERO")
            if (referredByValue !== '' &&
                referredByValue !== '0' &&
                referredByValue.toUpperCase() !== 'ZERO') {

                console.log(`User ${user.id} has valid referredBy value: ${referredByValue}`);
            } else {
                // Invalid referral code, use N/A
                referredByValue = 'N/A';
                console.log(`User ${user.id} has invalid referredBy value, using N/A`);
            }
        } else {
            // No referredBy value, use N/A
            referredByValue = 'N/A';
            console.log(`User ${user.id} has no referredBy value, using N/A`);
        }

        console.log(`User ${user.id} final referredBy value: ${referredByValue} (raw: ${user.referredBy}, standardized: ${standardizedUser.referredBy})`);

        // Get refers done count - we'll fetch this in a separate batch after rendering the table
        let refersDoneCount = 0;

        // Create table row using standardized data
        tableHTML += `
            <tr data-user-id="${user.id}" class="user-row">
                <td data-field="actions">
                    <button class="admin-btn-small" onclick="editUser('${user.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
                <td data-field="fullName">${standardizedUser.fullName}</td>
                <td data-field="email">${standardizedUser.email}</td>
                <td data-field="phoneNumber">${standardizedUser.phoneNumber}</td>
                <td data-field="referralCode">${standardizedUser.referralCode}</td>
                <td data-field="referredBy">${referredByValue}</td>
                <td data-field="refersDone">${refersDoneCount}</td>
                <td data-field="plan">${planName}</td>
                <td data-field="videos">${videosCount}</td>
                <td data-field="todayVideos">${todayVideosCount}</td>
                <td data-field="activeDays">${activeDays}</td>
                <td data-field="earnings">${earnings}</td>
                <td data-field="earningWallet">${formatCurrency(standardizedUser.wallet?.earning || 0)}</td>
                <td data-field="bonusWallet">${formatCurrency(standardizedUser.wallet?.bonus || 0)}</td>
                <td data-field="mainWallet">${formatCurrency(standardizedUser.wallet?.balance || standardizedUser.walletBalance || 0)}</td>
                <td data-field="joinDate">${joinDate}</td>
                <td data-field="status"><span class="admin-badge ${statusClass}">${standardizedUser.status}</span></td>
            </tr>
        `;
    });

    usersTable.innerHTML = tableHTML;

    // Add global functions for buttons
    window.viewUserDetails = viewUserDetails;
    window.editUser = editUser;
    window.saveUserData = saveUserData;
    window.cancelEditRow = cancelEditRow;

    // Fetch referrals count for all users
    fetchReferralsCounts();
}

// View user details
async function viewUserDetails(userId) {
    try {
        showLoading('Loading user details...');

        // Force fresh data by bypassing any cached versions
        console.log('Getting fresh user data for user details view');

        // Get user details directly from Firestore
        const userDoc = await getDoc(doc(db, 'users', userId));

        if (!userDoc.exists()) {
            hideLoading();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'User not found',
            });
            return;
        }

        const rawUserData = userDoc.data();
        console.log('Raw user data from Firebase:', rawUserData);
        console.log('Raw user data keys:', Object.keys(rawUserData));

        // Check if referredBy exists in the raw data
        if ('referredBy' in rawUserData) {
            console.log('referredBy exists in raw data with value:', rawUserData.referredBy);
            console.log('referredBy type:', typeof rawUserData.referredBy);
        } else {
            console.log('referredBy field does NOT exist in raw data');
        }

        // Standardize user data to ensure consistent field access
        const user = standardizeUserData(rawUserData);
        console.log('Standardized user data:', user);
        currentUserId = userId;

        // Get referred by value directly from raw data if it exists and is not empty/invalid
        // This ensures we display exactly what's in the database
        let referredByValue;

        // Log the raw referredBy value for debugging
        console.log('Raw referredBy value:', rawUserData.referredBy,
                    `type: ${typeof rawUserData.referredBy},
                    exists: ${'referredBy' in rawUserData},
                    null: ${rawUserData.referredBy === null},
                    undefined: ${rawUserData.referredBy === undefined}`);

        // Special handling for numeric referredBy values
        if ('referredBy' in rawUserData &&
            rawUserData.referredBy !== null &&
            rawUserData.referredBy !== undefined) {

            // Convert to string and trim
            referredByValue = String(rawUserData.referredBy).trim();

            // Check if it's a valid referral code (not empty, not "0", not "ZERO")
            if (referredByValue !== '' &&
                referredByValue !== '0' &&
                referredByValue.toUpperCase() !== 'ZERO') {

                console.log('User has valid referredBy value:', referredByValue);
            } else {
                // Invalid referral code, use N/A
                referredByValue = 'N/A';
                console.log('User has invalid referredBy value, using N/A');
            }
        } else {
            // No referredBy value, use N/A
            referredByValue = 'N/A';
            console.log('User has no referredBy value, using N/A');
        }

        console.log('Final referredBy value to display:', referredByValue);

        // Get refers done count
        let refersDoneCount = 0;
        try {
            // Check if we can get referrals from the users collection
            console.log(`Fetching referrals for code: ${user.referralCode}`);
            const usersQuery = query(
                collection(db, 'users'),
                where('referredBy', '==', user.referralCode),
                limit(100) // Limit to 100 to avoid performance issues
            );

            const usersSnapshot = await getDocs(usersQuery);
            refersDoneCount = usersSnapshot.size;
            console.log(`Found ${refersDoneCount} referrals for code ${user.referralCode} in users collection`);

            // If we hit the limit, show 100+
            if (refersDoneCount === 100) {
                refersDoneCount = '100+';
            }
        } catch (error) {
            console.error('Error getting referrals count:', error);
            // Continue execution even if there's an error
            refersDoneCount = 0;
        }

        // Get user's last login
        let lastLogin = 'Never';
        try {
            // First check if the user has any login records
            const loginCountQuery = query(
                collection(db, 'user_logins'),
                where('userId', '==', userId)
            );

            const loginCountSnapshot = await getDocs(loginCountQuery);

            if (!loginCountSnapshot.empty) {
                // If there are login records, get the most recent one
                // This is a simpler query that doesn't require a compound index
                const allLogins = loginCountSnapshot.docs.map(doc => {
                    const data = doc.data();
                    return {
                        ...data,
                        timestamp: data.timestamp instanceof Timestamp ?
                            data.timestamp.toDate() :
                            new Date(data.timestamp)
                    };
                });

                // Sort by timestamp descending
                allLogins.sort((a, b) => b.timestamp - a.timestamp);

                if (allLogins.length > 0) {
                    lastLogin = formatDateTime(allLogins[0].timestamp);
                }
            }
        } catch (error) {
            console.error('Error getting last login:', error);
            // Continue execution even if there's an error
            lastLogin = 'Error loading';
        }

        // Get user's total videos from standardized data (using totalVideosCompleted as source of truth)
        let totalVideos = user.stats.totalVideosCompleted || user.stats.totalTranslations || 'N/A'; // Try both fields for backward compatibility

        // Get today's videos count
        let todayVideos = 0;

        // Check if videos have been submitted for today
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        const videosSubmitted =
            (user.stats?.dailyVideos?.submitted === true) ||
            (user.stats?.dailyTranslations?.submitted === true) ||
            (rawUserData.stats?.dailyVideos?.submitted === true) ||
            (rawUserData.stats?.dailyTranslations?.submitted === true) ||
            (rawUserData.stats?.lastSubmission &&
             rawUserData.stats.lastSubmission.submitted === true &&
             rawUserData.stats.lastSubmission.count === 100 &&
             rawUserData.stats.lastSubmission.date === today);

        // If videos have been submitted today, always show 100
        if (videosSubmitted) {
            todayVideos = 100;
            console.log(`User ${userId} has submitted videos today, showing count as 100`);
        } else {
            // Otherwise check for today's videos in different possible locations
            if (user.stats && user.stats.todayVideos) {
                todayVideos = user.stats.todayVideos;
            } else if (user.stats && user.stats.dailyVideos && user.stats.dailyVideos.count) {
                todayVideos = user.stats.dailyVideos.count;
            } else if (rawUserData.stats && rawUserData.stats.dailyVideos && rawUserData.stats.dailyVideos.count) {
                todayVideos = rawUserData.stats.dailyVideos.count;
            } else if (rawUserData.dailyVideos && typeof rawUserData.dailyVideos === 'object') {
                // Check if dailyVideos is at the root level and is an object (date-based structure)
                // Find the most recent date entry
                const dates = Object.keys(rawUserData.dailyVideos);
                if (dates.length > 0) {
                    // Sort dates in descending order (newest first)
                    dates.sort((a, b) => new Date(b) - new Date(a));
                    // Get the count from the most recent date
                    todayVideos = rawUserData.dailyVideos[dates[0]];
                    console.log(`Found today's videos in root dailyVideos object for date ${dates[0]}: ${todayVideos}`);
                }
            } else if (user.stats && user.stats.todayTranslations) {
                // Fallback to translation fields for backward compatibility
                todayVideos = user.stats.todayTranslations;
            } else if (user.stats && user.stats.dailyTranslations && user.stats.dailyTranslations.count) {
                todayVideos = user.stats.dailyTranslations.count;
            } else if (rawUserData.stats && rawUserData.stats.dailyTranslations && rawUserData.stats.dailyTranslations.count) {
                todayVideos = rawUserData.stats.dailyTranslations.count;
            }
        }

        console.log(`User ${userId} today's videos: ${todayVideos} (sources: standardized=${user.stats?.todayVideos}, dailyVideos=${user.stats?.dailyVideos?.count}, raw=${rawUserData.stats?.dailyVideos?.count})`);

        // Get active days using the utility function to ensure consistent handling for all plan types
        let activeDays = getValidatedActiveDays(rawUserData);

        // Get plan type for debugging
        const planType = user.plan?.name || rawUserData.plan?.name || 'Unknown';
        console.log(`User ${userId} plan: ${planType}, active days: ${activeDays} (sources: direct=${rawUserData.activeDays}, stats=${rawUserData.stats?.activeDays}, validated=${activeDays})`);

        // Make sure we're displaying numbers, not undefined or null
        todayVideos = todayVideos || 0;
        activeDays = activeDays || 0;

        // If we don't have videos count in user data, try to get from videos collection
        if (totalVideos === 0 || totalVideos === 'N/A') {
            try {
                const videosQuery = query(
                    collection(db, 'videos'),
                    where('userId', '==', userId),
                    limit(100) // Limit to 100 to avoid performance issues
                );

                const videosSnapshot = await getDocs(videosQuery);
                totalVideos = videosSnapshot.size;

                // If we hit the limit, show 100+
                if (totalVideos === 100) {
                    totalVideos = '100+';
                }
            } catch (error) {
                console.warn('Could not access videos collection:', error);
                // Use user's earnings to estimate videos watched
                if (user.stats.totalEarnings > 0) {
                    totalVideos = 'Has videos';
                }
            }
        }

        // Update modal content with standardized user data
        if (modalUserId) modalUserId.textContent = userId;
        if (modalUserName) modalUserName.textContent = user.fullName;
        if (modalUserEmail) modalUserEmail.textContent = user.email;
        if (modalUserMobile) modalUserMobile.textContent = user.phoneNumber;

        // Use plan name from standardized data
        if (modalUserPlan) {
            modalUserPlan.textContent = user.plan.name;
        }

        // Handle plan expiry
        if (modalPlanExpiry) {
            if (rawUserData.planExpiresAt) {
                modalPlanExpiry.textContent = formatDateTime(rawUserData.planExpiresAt);
            } else {
                modalPlanExpiry.textContent = 'N/A';
            }
        }

        if (modalUserStatus) {
            modalUserStatus.textContent = user.status;
            modalUserStatus.className = `admin-badge ${user.status === 'blocked' ? 'error' : 'success'}`;
        }

        if (modalUserJoined) modalUserJoined.textContent = formatDateTime(user.createdAt);
        if (modalUserReferralCode) modalUserReferralCode.textContent = user.referralCode;
        if (modalUserReferredBy) modalUserReferredBy.textContent = referredByValue;
        if (document.getElementById('modal-user-refers-done')) document.getElementById('modal-user-refers-done').textContent = refersDoneCount;
        if (document.getElementById('modal-user-earning-wallet')) document.getElementById('modal-user-earning-wallet').textContent = formatCurrency(user.wallet.earning || 0);
        if (document.getElementById('modal-user-bonus-wallet')) document.getElementById('modal-user-bonus-wallet').textContent = formatCurrency(user.wallet.bonus || 0);
        if (modalUserWalletBalance) modalUserWalletBalance.textContent = formatCurrency(user.wallet.balance);
        if (modalUserEarnings) modalUserEarnings.textContent = formatCurrency(user.stats.totalEarnings);
        if (document.getElementById('modal-user-videos')) document.getElementById('modal-user-videos').textContent = totalVideos;
        if (document.getElementById('modal-user-today-videos')) document.getElementById('modal-user-today-videos').textContent = todayVideos;
        if (document.getElementById('modal-user-active-days')) document.getElementById('modal-user-active-days').textContent = activeDays;
        if (modalUserLastLogin) modalUserLastLogin.textContent = lastLogin;

        // Update button visibility based on status
        if (blockUserBtn) {
            blockUserBtn.style.display = user.status !== 'blocked' ? 'block' : 'none';
        }

        if (unblockUserBtn) {
            unblockUserBtn.style.display = user.status === 'blocked' ? 'block' : 'none';
        }

        // Show modal
        hideLoading();
        showModal('user-modal');

    } catch (error) {
        console.error('Error viewing user details:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load user details',
        });
    }
}

// Show edit user form
function showEditUserForm() {
    // Hide user profile and tabs
    document.querySelector('.admin-user-profile').style.display = 'none';
    document.querySelector('.admin-tabs').style.display = 'none';
    document.querySelector('.admin-tab-content.active').style.display = 'none';
    document.querySelector('.admin-user-actions').style.display = 'none';

    // Get current user data
    const userDoc = currentUsers.find(user => user.id === currentUserId);
    if (userDoc) {
        // Get standardized user data
        const user = userDoc._standardized || standardizeUserData(userDoc);

        // Populate form fields with standardized data
        editUserNameInput.value = user.fullName;
        editUserEmailInput.value = user.email;

        // Clear password field - we don't want to show the current password
        const passwordInput = document.getElementById('edit-user-password');
        if (passwordInput) {
            passwordInput.value = '';
            // Show the stored password if it exists (for admin reference)
            if (userDoc.password) {
                passwordInput.placeholder = `Current: ${userDoc.password}`;
            }
        }

        editUserMobileInput.value = user.phoneNumber;
        editUserReferralInput.value = user.referralCode;

        // Populate referred by field
        const referredByInput = document.getElementById('edit-user-referred-by');
        if (referredByInput) {
            referredByInput.value = user.referredBy || '';
        }

        // Populate wallet fields
        const mainWalletInput = document.getElementById('edit-wallet');
        if (mainWalletInput) {
            mainWalletInput.value = user.wallet?.balance || user.walletBalance || 0;
        }

        const earningWalletInput = document.getElementById('edit-earning-wallet');
        if (earningWalletInput) {
            earningWalletInput.value = user.wallet?.earning || 0;
        }

        const bonusWalletInput = document.getElementById('edit-bonus-wallet');
        if (bonusWalletInput) {
            bonusWalletInput.value = user.wallet?.bonus || 0;
        }

        // Populate stats fields
        const totalEarningsInput = document.getElementById('edit-earnings');
        if (totalEarningsInput) {
            totalEarningsInput.value = user.stats?.totalEarnings || user.totalEarnings || 0;
        }

        const totalVideosInput = document.getElementById('edit-videos');
        if (totalVideosInput) {
            totalVideosInput.value = user.stats?.totalVideosCompleted || user.stats?.totalTranslations || user.videosCount || user.translationsCount || 0;
        }

        // Populate today's videos field
        const todayVideosInput = document.getElementById('edit-today-videos');
        if (todayVideosInput) {
            // Check if videos have been submitted for today
            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
            const videosSubmitted =
                (user.stats?.dailyVideos?.submitted === true) ||
                (user.stats?.dailyTranslations?.submitted === true) ||
                (userDoc.stats?.dailyVideos?.submitted === true) ||
                (userDoc.stats?.dailyTranslations?.submitted === true) ||
                (userDoc.stats?.lastSubmission &&
                 userDoc.stats.lastSubmission.submitted === true &&
                 userDoc.stats.lastSubmission.count === 100 &&
                 userDoc.stats.lastSubmission.date === today);

            // If videos have been submitted today, always show 100
            if (videosSubmitted) {
                todayVideosInput.value = 100;
                console.log(`User ${userDoc.id} has submitted videos today, showing count as 100 in edit form`);
            } else {
                todayVideosInput.value = user.stats?.todayVideos || user.stats?.dailyVideos?.count || user.stats?.todayTranslations || 0;
            }
        }

        // Populate active days field using the validated active days utility
        const activeDaysInput = document.getElementById('edit-active-days');
        if (activeDaysInput) {
            // Use the utility function to get consistent active days value
            const validatedActiveDays = getValidatedActiveDays(userDoc);
            activeDaysInput.value = validatedActiveDays;
            console.log(`Setting active days input for user ${userDoc.id} to ${validatedActiveDays} (raw: ${userDoc.activeDays})`);
        }

        // Set plan selection using standardized plan name
        if (editUserPlanSelect) {
            const planName = user.plan.name;

            // Find and select the matching option
            for (let i = 0; i < editUserPlanSelect.options.length; i++) {
                if (editUserPlanSelect.options[i].value === planName) {
                    editUserPlanSelect.selectedIndex = i;
                    break;
                }
            }
        }

        // Set default plan duration
        if (editUserPlanDuration) {
            editUserPlanDuration.value = 30; // Default to 30 days
        }

        // Set custom video duration
        if (editUserVideoDuration) {
            const customDuration = userDoc.customVideoDuration;
            if (customDuration) {
                editUserVideoDuration.value = customDuration;
            } else {
                editUserVideoDuration.value = ''; // Use default
            }
        }

        // Set short video advantage checkbox
        if (editShortVideoToggle) {
            // Check if user has short video advantage
            editShortVideoToggle.checked = userDoc.shortVideoAdvantage === true;

            // Show/hide duration container based on checkbox state
            if (shortVideoDurationContainer) {
                shortVideoDurationContainer.style.display = editShortVideoToggle.checked ? 'block' : 'none';
            }

            // Set duration if available
            if (shortVideoDurationInput && userDoc.shortVideoExpiryDate) {
                const expiryDate = userDoc.shortVideoExpiryDate.toDate();
                const currentDate = new Date();
                const daysRemaining = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));
                shortVideoDurationInput.value = daysRemaining > 0 ? daysRemaining : 7;
            } else if (shortVideoDurationInput) {
                shortVideoDurationInput.value = 7; // Default to 7 days
            }
        }
    }

    // Show edit form
    editUserForm.style.display = 'block';
}

// Hide edit user form
function hideEditUserForm() {
    // Hide edit form
    editUserForm.style.display = 'none';

    // Show user profile and tabs
    document.querySelector('.admin-user-profile').style.display = 'flex';
    document.querySelector('.admin-tabs').style.display = 'flex';
    document.querySelector('.admin-tab-content.active').style.display = 'block';
    document.querySelector('.admin-user-actions').style.display = 'flex';
}

// Show activate plan form
function showActivatePlanForm() {
    // Hide user profile and tabs
    document.querySelector('.admin-user-profile').style.display = 'none';
    document.querySelector('.admin-tabs').style.display = 'none';
    document.querySelector('.admin-tab-content.active').style.display = 'none';
    document.querySelector('.admin-user-actions').style.display = 'none';

    // Show plan form
    activatePlanForm.style.display = 'block';
}

// Hide activate plan form
function hideActivatePlanForm() {
    // Hide plan form
    activatePlanForm.style.display = 'none';

    // Show user profile and tabs
    document.querySelector('.admin-user-profile').style.display = 'flex';
    document.querySelector('.admin-tabs').style.display = 'flex';
    document.querySelector('.admin-tab-content.active').style.display = 'block';
    document.querySelector('.admin-user-actions').style.display = 'flex';
}

// Generate a new referral code
function generateReferralCode() {
    // Get a random 4-digit number
    const randomNum = Math.floor(1000 + Math.random() * 9000);
    // Return the code with MY prefix
    return `MY${randomNum}`;
}

// Update user
async function updateUser() {
    if (!currentUserId) return;

    try {
        // Get form values
        const name = editUserNameInput.value;
        const email = editUserEmailInput.value;
        const password = document.getElementById('edit-user-password')?.value;
        const mobile = editUserMobileInput.value;
        let referralCode = editUserReferralInput.value;
        const planId = editUserPlanSelect ? editUserPlanSelect.value : '';
        const planDuration = editUserPlanDuration ? parseInt(editUserPlanDuration.value) : 30;
        const earningsPerBatch = editUserEarningsPerBatch ? parseFloat(editUserEarningsPerBatch.value) : 0;
        const customVideoDuration = editUserVideoDuration ? editUserVideoDuration.value : '';

        // Get plan details
        let planName = '';
        let planDetails = null;

        if (planId) {
            // Find the selected plan in the available plans
            const selectedPlan = availablePlans.find(plan => plan.id === planId);
            if (selectedPlan) {
                planName = selectedPlan.name;
                planDetails = {
                    id: selectedPlan.id,
                    name: selectedPlan.name,
                    earningsPerBatch: selectedPlan.earningsPerBatch || earningsPerBatch,
                    duration: selectedPlan.duration || planDuration
                };
            }
        }

        if (!name) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Name is required',
            });
            return;
        }

        if (planName && (isNaN(planDuration) || planDuration < 1)) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Plan duration must be at least 1 day',
            });
            return;
        }

        showLoading('Updating user...');

        // Verify admin status again before proceeding
        if (!await verifyAdminStatus('update users')) {
            hideLoading();
            return;
        }

        // Get the current user document to ensure we have the latest data
        const userRef = doc(db, 'users', currentUserId);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
            throw new Error('User document not found');
        }

        console.log('Current user data:', userDoc.data());

        // APPROACH: Break the update into multiple smaller updates
        // 1. First update basic fields
        console.log('Step 1: Updating basic user information...');
        // Prepare update data
        const updateData = {
            fullName: name,
            email: email || userDoc.data().email,
            mobile: mobile || userDoc.data().mobile,
            phoneNumber: mobile || userDoc.data().phoneNumber,
            referralCode: referralCode || generateReferralCode(),
            referredBy: document.getElementById('edit-user-referred-by')?.value?.trim() || userDoc.data().referredBy || '',
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid
        };

        // Add custom video duration if specified
        if (customVideoDuration && customVideoDuration !== '') {
            updateData.customVideoDuration = parseInt(customVideoDuration);
        } else {
            // Remove custom duration if set to default
            updateData.customVideoDuration = null;
        }

        await updateDoc(userRef, updateData);
        console.log('Basic user information updated successfully');

        // 1.1 Update password if provided
        if (password && password.trim() !== '') {
            console.log('Step 1.1: Updating user password...');
            try {
                // Store the password in the user document for admin reference
                await updateDoc(userRef, {
                    password: password, // Store password in plain text for admin reference
                    passwordUpdatedAt: serverTimestamp(),
                    passwordUpdatedBy: auth.currentUser.uid
                });
                console.log('Password stored in user document for admin reference');

                // Note: In a production environment, you would use Firebase Auth Admin SDK
                // to update the user's password securely. Since we don't have access to that
                // in the client, we're just storing the password in the user document.

                // Show a message to the admin
                Swal.fire({
                    icon: 'info',
                    title: 'Password Updated',
                    text: 'The password has been stored in the user document. The user will need to use this password for their next login.',
                    timer: 5000
                });
            } catch (passwordError) {
                console.error('Error updating password:', passwordError);
                // Show error but continue with other updates
                Swal.fire({
                    icon: 'warning',
                    title: 'Password Update Issue',
                    text: 'Could not update password: ' + passwordError.message,
                    timer: 3000
                });
            }
        }

        // 2. Update wallet fields if provided
        const walletBalance = parseFloat(document.getElementById('edit-wallet')?.value);
        const earningWallet = parseFloat(document.getElementById('edit-earning-wallet')?.value);
        const bonusWallet = parseFloat(document.getElementById('edit-bonus-wallet')?.value);

        if (!isNaN(walletBalance) || !isNaN(earningWallet) || !isNaN(bonusWallet)) {
            console.log('Step 2: Updating wallet information...');
            const walletUpdate = {};

            // Get the current wallet data for reference
            const currentWalletData = userDoc.data().wallet || { earning: 0, bonus: 0, balance: 0 };
            console.log('Current wallet data:', currentWalletData);

            // Only update fields that were provided
            if (!isNaN(walletBalance)) {
                walletUpdate['wallet.balance'] = walletBalance;
                walletUpdate['walletBalance'] = walletBalance; // For backward compatibility
            }

            if (!isNaN(earningWallet)) {
                walletUpdate['wallet.earning'] = earningWallet;
            }

            if (!isNaN(bonusWallet)) {
                walletUpdate['wallet.bonus'] = bonusWallet;
            }

            await updateDoc(userRef, walletUpdate);
            console.log('Wallet information updated successfully');
        }

        // 3. Update stats fields if provided
        const totalEarnings = parseFloat(document.getElementById('edit-earnings')?.value);
        const videosCount = parseInt(document.getElementById('edit-videos')?.value);
        const todayVideosCount = parseInt(document.getElementById('edit-today-videos')?.value);
        const activeDaysCount = parseInt(document.getElementById('edit-active-days')?.value);

        if (!isNaN(totalEarnings) || !isNaN(videosCount) || !isNaN(todayVideosCount) || !isNaN(activeDaysCount)) {
            console.log('Step 3: Updating stats information...');
            const statsUpdate = {};

            if (!isNaN(totalEarnings)) {
                statsUpdate['stats.totalEarnings'] = totalEarnings;
                statsUpdate['totalEarnings'] = totalEarnings; // For backward compatibility
            }

            if (!isNaN(videosCount)) {
                statsUpdate['stats.totalVideosCompleted'] = videosCount; // Use totalVideosCompleted as source of truth
                statsUpdate['stats.totalTranslationsCompleted'] = videosCount; // Keep for backward compatibility
                statsUpdate['stats.totalTranslations'] = videosCount; // Keep for backward compatibility
                statsUpdate['translationsCount'] = videosCount; // For backward compatibility
                statsUpdate['videosCount'] = videosCount; // For backward compatibility
            }

            if (!isNaN(todayVideosCount)) {
                // Get the current date in user's local timezone
                const now = new Date();
                const currentDate = now.toLocaleDateString('en-US');

                // Update in multiple locations to ensure compatibility
                statsUpdate['stats.dailyVideos.count'] = todayVideosCount;
                statsUpdate['stats.dailyVideos.date'] = currentDate;
                statsUpdate['stats.dailyVideos.lastUpdated'] = serverTimestamp();
                statsUpdate['stats.todayVideos'] = todayVideosCount;

                // Keep backward compatibility with translation fields
                statsUpdate['stats.dailyTranslations.count'] = todayVideosCount;
                statsUpdate['stats.dailyTranslations.date'] = currentDate;
                statsUpdate['stats.dailyTranslations.lastUpdated'] = serverTimestamp();
                statsUpdate['stats.todayTranslations'] = todayVideosCount;
            }

            if (!isNaN(activeDaysCount)) {
                // Update in multiple locations to ensure compatibility
                statsUpdate['activeDays'] = activeDaysCount;
                statsUpdate['stats.activeDays'] = activeDaysCount;
            }

            await updateDoc(userRef, statsUpdate);
            console.log('Stats information updated successfully');
        }

        // 4. Update short video advantage if provided
        if (editShortVideoToggle) {
            console.log('Step 4: Updating short video advantage...');
            const shortVideoUpdate = {
                shortVideoAdvantage: editShortVideoToggle.checked,
                // Mark this advantage as set by admin to prevent automatic overrides
                advantageSource: 'admin'
            };

            if (editShortVideoToggle.checked) {
                const duration = shortVideoDurationInput ? parseInt(shortVideoDurationInput.value) : 7;

                const now = new Date();
                const expiryDate = new Date(now.getTime() + (duration * 24 * 60 * 60 * 1000));
                shortVideoUpdate.shortVideoExpiryDate = Timestamp.fromDate(expiryDate);

                console.log(`Setting admin-controlled short video advantage to expire in ${duration} days on ${expiryDate.toLocaleDateString()}`);
            } else {
                shortVideoUpdate.shortVideoExpiryDate = null;
                console.log('Explicitly disabling short video advantage by admin');
            }

            await updateDoc(userRef, shortVideoUpdate);
            console.log('Short video advantage updated successfully with admin override');
        }

        // 5. Update plan if selected
        let oldPlanName = '';
        if (userDoc.exists() && userDoc.data().plan && userDoc.data().plan.name) {
            oldPlanName = userDoc.data().plan.name;
        }

        if (planId && planDetails) {
            console.log('Step 5: Updating plan information...');
            console.log('Old plan:', oldPlanName, 'New plan:', planDetails.name);
            const now = new Date();
            const expiryDate = new Date(now.getTime() + (planDuration * 24 * 60 * 60 * 1000));

            // Break plan update into smaller steps
            // 5.1 Update basic plan info
            console.log('Step 5.1: Updating basic plan info...');

            // Create a complete plan object to ensure consistent structure
            const planObject = {
                id: planDetails.id,
                name: planDetails.name,
                dailyLimit: 100,
                // Other fields will be added in the next step
            };

            // Update with the complete plan object
            await updateDoc(userRef, {
                plan: planObject
            });
            console.log('Basic plan info updated successfully');

            // 5.2 Update plan details
            console.log('Step 5.2: Updating plan details...');

            // Update the plan object with additional fields
            planObject.earningsPerBatch = planDetails.earningsPerBatch;
            planObject.duration = planDetails.duration;

            // Set plan rate based on plan name
            let planRate = 0.1; // Default for Trial
            if (planDetails.name === 'Trial') {
                planRate = 0.1; // ₹10 for 100 videos
            } else if (planDetails.name === 'Starter') {
                planRate = 0.25; // ₹25 for 100 videos
            } else if (planDetails.name === 'Premium') {
                planRate = 1.5; // ₹150 for 100 videos
            }
            planObject.rate = planRate;

            // Update the plan object in the database
            await updateDoc(userRef, {
                plan: planObject,
                earningsPerBatch: planDetails.earningsPerBatch
            });
            console.log('Plan details updated successfully');

            // 5.3 Update plan dates
            console.log('Step 5.3: Updating plan dates...');
            await updateDoc(userRef, {
                'planActivatedAt': serverTimestamp(),
                'planExpiresAt': Timestamp.fromDate(expiryDate)
            });
            console.log('Plan dates updated successfully');

            // 5.4 Update active days
            console.log('Step 5.4: Updating active days...');
            await updateDoc(userRef, {
                'activeDays': 1 // Reset active days when activating a new plan
            });
            console.log('Active days updated successfully');

            // 5.5 Add transaction record for plan activation
            console.log('Step 5.5: Adding transaction record...');
            try {
                // Create transaction data
                const transactionData = {
                    userId: currentUserId,
                    type: 'plan',
                    amount: 0, // Free activation by admin
                    description: `${planName} plan activated by admin for ${planDuration} days`,
                    status: 'completed',
                    timestamp: serverTimestamp(),
                    planDetails: {
                        id: planDetails.id,
                        name: planDetails.name,
                        duration: planDuration,
                        earningsPerBatch: planDetails.earningsPerBatch
                    },
                    adminId: auth.currentUser.uid, // Add admin ID for tracking
                    createdBy: 'admin'
                };

                // Skip transaction creation for now - we'll handle it differently
                console.log('Transaction data prepared:', transactionData);
                console.log('Skipping transaction creation due to security rules - this is expected');

                // In a production environment, you would use Cloud Functions to create transactions
                // for other users, or modify your security rules to allow admins to create transactions
                // for any user.

                // For now, we'll just log the transaction data and continue
                console.log('Plan activation transaction would be recorded with:', transactionData);
            } catch (transactionError) {
                console.error('Error preparing transaction record:', transactionError);
                // Continue even if transaction creation fails
            }

            // 5.6 Process referral bonus if plan changed to a paid plan
            if (planDetails.name !== oldPlanName) {
                console.log('Step 5.6: Processing referral bonus...');
                // Normalize the plan name to ensure consistency
                const normalizedPlanName = normalizePlanName(planDetails.name);
                console.log('Normalized plan name for referral bonus:', normalizedPlanName);

                // Process for all paid plans (both current and legacy plan names)
                if (['Starter', 'Premium'].includes(normalizedPlanName)) {
                    try {
                        console.log('Processing referral bonus for user:', currentUserId, 'with plan:', planDetails.name);

                        // Get fresh user data to check referral code
                        const refreshedUserDoc = await getDoc(userRef);
                        if (refreshedUserDoc.exists()) {
                            const userData = refreshedUserDoc.data();
                            console.log('User referral data:', {
                                email: userData.email,
                                referredBy: userData.referredBy,
                                plan: planDetails.name
                            });

                            // Only process if user has a referral code
                            if (userData.referredBy) {
                                // Use normalized plan name in the plan details
                                const normalizedPlanDetails = {
                                    ...planDetails,
                                    name: normalizedPlanName
                                };

                                try {
                                    console.log('Checking if user is eligible for referral bonus (only processed on first plan upgrade)');
                                    const bonusProcessed = await processReferralBonus(currentUserId, normalizedPlanDetails);
                                    if (bonusProcessed) {
                                        console.log('Referral bonus processed successfully');
                                    } else {
                                        console.log('No referral bonus processed (user may not have been referred or already received a bonus)');
                                    }
                                } catch (bonusError) {
                                    console.error('Error processing referral bonus:', bonusError);
                                    // Continue even if referral bonus processing fails
                                }
                            } else {
                                console.log('User has no referral code, skipping bonus processing');
                            }
                        }
                    } catch (referralError) {
                        console.error('Error in referral bonus processing:', referralError);
                        // Continue even if referral bonus processing fails
                    }
                } else {
                    console.log('Plan not eligible for referral bonus:', planDetails.name);
                }
            } else {
                console.log('Plan not changed, skipping referral bonus processing');
            }
        }

        hideLoading();
        hideEditUserForm();

        // Refresh user details
        await viewUserDetails(currentUserId);

        // Refresh data
        await loadUsers(false);

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'User has been updated',
        });
    } catch (error) {
        console.error('Error updating user:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to update user: ' + error.message,
        });
    }
}

// Activate plan
async function activatePlan() {
    if (!currentUserId) return;

    try {
        // Get form values
        const planId = planSelect.value;
        const duration = parseInt(planDuration.value);
        const earningsPerBatch = planEarningsPerBatch ? parseFloat(planEarningsPerBatch.value) : 0;

        // Get plan details
        let planDetails = null;

        if (planId) {
            // Find the selected plan in the available plans
            const selectedPlan = availablePlans.find(plan => plan.id === planId);
            if (selectedPlan) {
                planDetails = {
                    id: selectedPlan.id,
                    name: selectedPlan.name,
                    earningsPerBatch: selectedPlan.earningsPerBatch || earningsPerBatch,
                    duration: selectedPlan.duration || duration
                };
            }
        }

        if (!planId || !planDetails || isNaN(duration) || duration <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please select a valid plan and duration',
            });
            return;
        }

        showLoading('Activating plan...');

        // Verify admin status again before proceeding
        if (!await verifyAdminStatus('activate plans for users')) {
            hideLoading();
            return;
        }

        // Get the current user document to ensure we have the latest data
        const userRef = doc(db, 'users', currentUserId);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
            throw new Error('User document not found');
        }

        console.log('Current user data:', userDoc.data());

        // Calculate expiry date
        const now = new Date();
        const expiryDate = new Date(now.getTime() + (duration * 24 * 60 * 60 * 1000));

        // APPROACH: Break the update into smaller parts
        // 1. First update plan fields - use a complete plan object to ensure proper structure
        console.log('Step 1: Updating plan information...');

        // Create a complete plan object to ensure consistent structure
        const planObject = {
            id: planDetails.id,
            name: planDetails.name,
            dailyLimit: 100,
            // Other plan fields will be added in the next step
        };

        // Update with the complete plan object
        await updateDoc(userRef, {
            plan: planObject,
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid
        });
        console.log('Basic plan information updated successfully');

        // 2. Update plan details
        console.log('Step 2: Updating plan details...');

        // Set plan rate based on plan name
        let planRate = 0.1; // Default for Trial
        if (planDetails.name === 'Trial') {
            planRate = 0.1; // ₹10 for 100 videos
        } else if (planDetails.name === 'Starter') {
            planRate = 0.25; // ₹25 for 100 videos
        } else if (planDetails.name === 'Premium') {
            planRate = 1.5; // ₹150 for 100 videos
        }

        // Update the plan object with additional fields
        planObject.earningsPerBatch = planDetails.earningsPerBatch;
        planObject.duration = planDetails.duration;
        planObject.rate = planRate;

        // Update the plan object in the database
        await updateDoc(userRef, {
            plan: planObject,
            earningsPerBatch: planDetails.earningsPerBatch
        });
        console.log('Plan details updated successfully');

        // 3. Update plan dates and active days
        console.log('Step 3: Updating plan dates and active days...');
        await updateDoc(userRef, {
            'planActivatedAt': serverTimestamp(),
            'planExpiresAt': Timestamp.fromDate(expiryDate),
            'activeDays': 1, // Reset active days when activating a new plan
            'lastActiveDayUpdate': serverTimestamp()
        });
        console.log('Plan dates and active days updated successfully');

        // 4. Add transaction record
        console.log('Step 4: Adding transaction record...');
        try {
            // Create transaction data
            const transactionData = {
                userId: currentUserId,
                type: 'plan',
                amount: 0, // Free activation by admin
                description: `${planDetails ? planDetails.name : 'Unknown'} plan activated by admin for ${duration} days`,
                status: 'completed',
                timestamp: serverTimestamp(),
                planDetails: {
                    id: planDetails.id,
                    name: planDetails.name,
                    duration: duration,
                    earningsPerBatch: planDetails.earningsPerBatch,
                    rate: planRate
                },
                adminId: auth.currentUser.uid, // Add admin ID for tracking
                createdBy: 'admin'
            };

            // Skip transaction creation for now - we'll handle it differently
            console.log('Transaction data prepared:', transactionData);
            console.log('Skipping transaction creation due to security rules - this is expected');

            // In a production environment, you would use Cloud Functions to create transactions
            // for other users, or modify your security rules to allow admins to create transactions
            // for any user.

            // For now, we'll just log the transaction data and continue
            console.log('Plan activation transaction would be recorded with:', transactionData);
        } catch (transactionError) {
            console.error('Error preparing transaction record:', transactionError);
            // Continue even if transaction creation fails
        }
        console.log('Transaction record processing completed');

        // 5. Process referral bonus if applicable
        console.log('Step 5: Processing referral bonus...');
        // Normalize the plan name to ensure consistency
        const normalizedPlanName = normalizePlanName(planDetails.name);
        console.log('Normalized plan name for referral bonus:', normalizedPlanName);

        // Process for all paid plans (both current and legacy plan names)
        if (['Starter', 'Premium'].includes(normalizedPlanName)) {
            try {
                console.log('Processing referral bonus for user:', currentUserId, 'with plan:', planDetails.name);

                // Get fresh user data to check referral code
                const refreshedUserDoc = await getDoc(userRef);
                if (refreshedUserDoc.exists()) {
                    const userData = refreshedUserDoc.data();
                    console.log('User referral data:', {
                        email: userData.email,
                        referredBy: userData.referredBy,
                        plan: planDetails.name
                    });

                    // Only process if user has a referral code
                    if (userData.referredBy) {
                        // Use normalized plan name in the plan details
                        const normalizedPlanDetails = {
                            ...planDetails,
                            name: normalizedPlanName
                        };

                        try {
                            // Process the referral bonus in a separate try-catch block
                            console.log('Calling processReferralBonus with:', {
                                userId: currentUserId,
                                planName: normalizedPlanName,
                                referredBy: userData.referredBy
                            });

                            console.log('Checking if user is eligible for referral bonus (only processed on first plan upgrade)');
                            const bonusProcessed = await processReferralBonus(currentUserId, normalizedPlanDetails);
                            if (bonusProcessed) {
                                console.log('Referral bonus processed successfully');
                            } else {
                                console.log('No referral bonus processed (user may not have been referred or already received a bonus)');
                            }
                        } catch (bonusError) {
                            console.error('Error processing referral bonus:', bonusError);
                            // Continue even if referral bonus processing fails
                        }
                    } else {
                        console.log('User has no referral code, skipping bonus processing');
                    }
                } else {
                    console.log('Could not get refreshed user data, skipping bonus processing');
                }
            } catch (referralError) {
                console.error('Error in referral bonus processing:', referralError);
                // Continue even if referral bonus processing fails
            }
        } else {
            console.log('Plan not eligible for referral bonus:', planDetails.name);
        }

        hideLoading();
        hideActivatePlanForm();

        // Refresh user details
        await viewUserDetails(currentUserId);

        // Refresh data
        await loadUsers(false);

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: `${planDetails ? planDetails.name : 'Unknown'} plan has been activated for ${duration} days with earnings of ${formatCurrency(planDetails ? planDetails.earningsPerBatch : 0)} per 100 videos`,
        });
    } catch (error) {
        console.error('Error activating plan:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to activate plan: ' + error.message,
        });
    }
}

// Edit user directly in the table
async function editUser(userId) {
    try {
        // Verify admin status
        if (!await verifyAdminStatus('edit users')) {
            return;
        }

        // Check if any row is already in edit mode
        const editingRow = document.querySelector('tr.editing');
        if (editingRow) {
            // Ask user if they want to cancel the current edit
            const result = await Swal.fire({
                icon: 'warning',
                title: 'Edit in Progress',
                text: 'You are already editing another user. Would you like to cancel that edit and edit this user instead?',
                showCancelButton: true,
                confirmButtonText: 'Yes, edit this user',
                cancelButtonText: 'No, continue current edit',
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)',
                customClass: {
                    title: 'gradient-text',
                    popup: 'glass-card',
                    confirmButton: 'admin-btn',
                    cancelButton: 'admin-btn admin-btn-secondary'
                }
            });

            if (!result.isConfirmed) {
                return;
            }

            // Cancel the current edit
            cancelEditRow(editingRow);
        }

        // Find the user row
        const userRow = document.querySelector(`tr[data-user-id="${userId}"]`);
        if (!userRow) {
            throw new Error('User row not found');
        }

        // Show loading indicator
        Swal.fire({
            title: 'Loading User Data',
            html: '<div class="admin-spinner"></div>',
            showConfirmButton: false,
            allowOutsideClick: false,
            background: 'var(--admin-card-bg)',
            color: 'var(--admin-text)',
            customClass: {
                title: 'gradient-text',
                popup: 'glass-card'
            }
        });

        // Try to get user data from currentUsers array
        let userData = currentUsers.find(user => user.id === userId);
        console.log('Looking for user with ID:', userId);
        console.log('Current users array length:', currentUsers.length);
        console.log('User found in currentUsers array:', userData ? 'Yes' : 'No');

        // If not found in the array, fetch directly from Firestore
        if (!userData) {
            console.log('User data not found in currentUsers array, fetching from Firestore...');
            try {
                // Validate userId
                if (!userId || typeof userId !== 'string' || userId.trim() === '') {
                    throw new Error('Invalid user ID');
                }

                // Fetch user document
                const userDoc = await getDoc(doc(db, 'users', userId));
                console.log('User document exists in Firestore:', userDoc.exists() ? 'Yes' : 'No');

                if (!userDoc.exists()) {
                    // If user doesn't exist, create a new user object with default values
                    console.warn(`User document with ID ${userId} not found in Firestore. Creating default user data.`);

                    // Create default user data
                    const defaultUserData = {
                        fullName: 'New User',
                        email: '',
                        phoneNumber: '',
                        mobile: '',
                        status: 'active',
                        createdAt: serverTimestamp(),
                        wallet: {
                            earning: 0,
                            bonus: 0,
                            balance: 0
                        },
                        plan: {
                            name: 'Trial',
                            duration: 2,
                            earningsPerBatch: 10,
                            rate: 0.2
                        },
                        stats: {
                            totalTranslations: 0,
                            totalEarnings: 0
                        },
                        referralCode: generateReferralCode(),
                        referredBy: ''
                    };

                    console.log('Created default user data:', defaultUserData);
                    const standardizedUser = standardizeUserData(defaultUserData);
                    userData = { id: userId, ...defaultUserData, _standardized: standardizedUser };

                    // Add to currentUsers array for future reference
                    currentUsers.push(userData);

                    console.log('Default user data created for new user');

                    // Create the user in Firestore
                    await setDoc(doc(db, 'users', userId), defaultUserData);
                    console.log('New user created in Firestore');
                } else {
                    // Create user data object with the same structure as in currentUsers
                    const rawUserData = userDoc.data();
                    console.log('Raw user data from Firestore:', rawUserData);

                    // Ensure wallet structure exists
                    if (!rawUserData.wallet) {
                        console.log('Creating wallet structure as it does not exist');
                        rawUserData.wallet = {
                            earning: 0,
                            bonus: 0,
                            balance: rawUserData.walletBalance || 0
                        };
                    } else {
                        console.log('Existing wallet structure:', rawUserData.wallet);
                    }

                    // Ensure wallet fields exist
                    if (rawUserData.wallet.earning === undefined) {
                        console.log('Adding missing earning field to wallet');
                        rawUserData.wallet.earning = 0;
                    }

                    if (rawUserData.wallet.bonus === undefined) {
                        console.log('Adding missing bonus field to wallet');
                        rawUserData.wallet.bonus = 0;
                    }

                    if (rawUserData.wallet.balance === undefined) {
                        console.log('Adding missing balance field to wallet');
                        rawUserData.wallet.balance = rawUserData.walletBalance || 0;
                    }

                    // For backward compatibility, ensure walletBalance field exists
                    if (rawUserData.walletBalance === undefined && rawUserData.wallet.balance !== undefined) {
                        console.log('Adding walletBalance field for backward compatibility');
                        rawUserData.walletBalance = rawUserData.wallet.balance;
                    }

                    // Ensure plan structure exists
                    if (!rawUserData.plan || typeof rawUserData.plan !== 'object') {
                        console.log('Creating plan structure as it does not exist or is not an object');
                        rawUserData.plan = {
                            name: rawUserData.plan || 'Trial',
                            duration: 2,
                            earningsPerBatch: 10,
                            rate: 0.2
                        };
                    }

                    // Ensure stats structure exists
                    if (!rawUserData.stats) {
                        console.log('Creating stats structure as it does not exist');
                        rawUserData.stats = {
                            totalTranslations: 0,
                            totalEarnings: 0
                        };
                    }

                    console.log('Processed user data before standardization:', rawUserData);
                    const standardizedUser = standardizeUserData(rawUserData);
                    console.log('Standardized user data:', standardizedUser);
                    userData = { id: userId, ...rawUserData, _standardized: standardizedUser };

                    // Add to currentUsers array for future reference
                    currentUsers.push(userData);

                    console.log('User data fetched successfully from Firestore');
                }
            } catch (fetchError) {
                console.error('Error fetching user data from Firestore:', fetchError);
                Swal.close();
                throw new Error('Failed to fetch user data: ' + fetchError.message);
            }
        }

        // Standardize user data
        const standardizedUser = userData._standardized || standardizeUserData(userData);

        // Store the current user ID for saving
        currentUserId = userId;

        // Close loading indicator
        Swal.close();

        // Scroll to the row being edited
        userRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Add a highlight effect before editing
        userRow.style.transition = 'background-color 0.3s ease';
        userRow.style.backgroundColor = 'rgba(139, 92, 246, 0.2)';

        setTimeout(() => {
            userRow.style.backgroundColor = '';

            // Open the user modal
            showModal('user-modal');

            // Show the edit form instead of toggling row edit mode
            currentUserId = userId;
            showEditUserForm();

            // Show a toast notification
            Swal.fire({
                icon: 'info',
                title: 'Editing User',
                text: `Now editing ${standardizedUser.fullName || 'user'}`,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)',
                customClass: {
                    title: 'gradient-text',
                    popup: 'glass-card'
                }
            });
        }, 300);

    } catch (error) {
        console.error('Error editing user:', error);
        hideLoading();
        Swal.close();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to edit user: ' + error.message,
            background: 'var(--admin-card-bg)',
            color: 'var(--admin-text)',
            customClass: {
                title: 'gradient-text',
                popup: 'glass-card'
            }
        });
    }
}

// Block user
async function blockUser() {
    if (!currentUserId) return;

    try {
        const result = await Swal.fire({
            title: 'Block User',
            text: 'Are you sure you want to block this user?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, block user',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#d33'
        });

        if (result.isConfirmed) {
            showLoading('Blocking user...');

            // Verify admin status
            if (!await verifyAdminStatus('block users')) {
                return;
            }

            // Get the current user document to ensure we have the latest data
            const userRef = doc(db, 'users', currentUserId);
            const userDoc = await getDoc(userRef);

            if (!userDoc.exists()) {
                throw new Error('User document not found');
            }

            console.log('Current user data:', userDoc.data());

            // Update user status
            console.log('Blocking user with ID:', currentUserId);
            console.log('Step 1: Updating user status...');
            await updateDoc(userRef, {
                status: 'blocked'
            });
            console.log('User status updated successfully');

            // Update metadata separately
            console.log('Step 2: Updating metadata...');
            await updateDoc(userRef, {
                updatedAt: serverTimestamp(),
                updatedBy: auth.currentUser.uid
            });
            console.log('Metadata updated successfully');

            hideLoading();
            hideModal('user-modal');

            // Refresh data
            await loadUsers(false);

            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'User has been blocked',
            });
        }
    } catch (error) {
        console.error('Error blocking user:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to block user',
        });
    }
}

// Unblock user
async function unblockUser() {
    if (!currentUserId) return;

    try {
        const result = await Swal.fire({
            title: 'Unblock User',
            text: 'Are you sure you want to unblock this user?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, unblock user',
            cancelButtonText: 'Cancel'
        });

        if (result.isConfirmed) {
            showLoading('Unblocking user...');

            // Verify admin status
            if (!await verifyAdminStatus('unblock users')) {
                return;
            }

            // Get the current user document to ensure we have the latest data
            const userRef = doc(db, 'users', currentUserId);
            const userDoc = await getDoc(userRef);

            if (!userDoc.exists()) {
                throw new Error('User document not found');
            }

            console.log('Current user data:', userDoc.data());

            // Update user status
            console.log('Unblocking user with ID:', currentUserId);
            console.log('Step 1: Updating user status...');
            await updateDoc(userRef, {
                status: 'active'
            });
            console.log('User status updated successfully');

            // Update metadata separately
            console.log('Step 2: Updating metadata...');
            await updateDoc(userRef, {
                updatedAt: serverTimestamp(),
                updatedBy: auth.currentUser.uid
            });
            console.log('Metadata updated successfully');

            hideLoading();
            hideModal('user-modal');

            // Refresh data
            await loadUsers(false);

            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'User has been unblocked',
            });
        }
    } catch (error) {
        console.error('Error unblocking user:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to unblock user',
        });
    }
}

// Helper function to detect if search term is a mobile number
function isMobileNumber(searchTerm) {
    if (!searchTerm || typeof searchTerm !== 'string') return false;

    const cleanedTerm = searchTerm.trim().replace(/\D/g, ''); // Remove non-digits

    // Check if it's a valid mobile number pattern
    // Indian mobile numbers: 10 digits starting with 6-9, or with +91 prefix
    const mobilePatterns = [
        /^[6-9]\d{9}$/, // 10 digits starting with 6-9
        /^91[6-9]\d{9}$/, // With 91 prefix
        /^\+91[6-9]\d{9}$/ // With +91 prefix
    ];

    return mobilePatterns.some(pattern => pattern.test(searchTerm.trim())) ||
           (cleanedTerm.length === 10 && /^[6-9]/.test(cleanedTerm));
}

// Handle filter change
function handleFilterChange() {
    // Update current filters
    currentFilters.status = statusFilter ? statusFilter.value : 'all';
    currentFilters.plan = planFilter ? planFilter.value : 'all';
    currentFilters.search = userSearch ? userSearch.value.trim() : '';

    // Show mobile search indicator if searching by mobile number
    showMobileSearchIndicator();

    // Hide the Create Index button when changing filters
    // It will be shown again if an index error occurs
    const createIndexBtn = document.getElementById('create-index-btn');
    if (createIndexBtn) {
        createIndexBtn.style.display = 'none';
    }

    // Reset pagination and load users
    loadUsers(false);
}

// Show mobile search indicator
function showMobileSearchIndicator() {
    const searchTerm = currentFilters.search;
    const isMobileSearch = searchTerm && isMobileNumber(searchTerm);

    // Remove existing indicator
    const existingIndicator = document.querySelector('.mobile-search-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    if (isMobileSearch) {
        // Create and show mobile search indicator
        const indicator = document.createElement('div');
        indicator.className = 'mobile-search-indicator';
        indicator.innerHTML = `
            <div style="background: #e8f5e8; border: 1px solid #4caf50; border-radius: 6px; padding: 10px; margin: 10px 0; color: #2e7d32; font-size: 14px;">
                <i class="fas fa-mobile-alt" style="margin-right: 8px;"></i>
                <strong>Mobile Search Active:</strong> Searching historical data for mobile number "${searchTerm}"
            </div>
        `;

        // Insert after search input
        const searchContainer = userSearch?.parentElement || document.querySelector('.admin-filters');
        if (searchContainer) {
            searchContainer.appendChild(indicator);
        }
    } else if (searchTerm) {
        // Show today-only search indicator for other searches
        const indicator = document.createElement('div');
        indicator.className = 'mobile-search-indicator';
        indicator.innerHTML = `
            <div style="background: #fff3e0; border: 1px solid #ff9800; border-radius: 6px; padding: 10px; margin: 10px 0; color: #f57c00; font-size: 14px;">
                <i class="fas fa-calendar-day" style="margin-right: 8px;"></i>
                <strong>Today's Data:</strong> Searching only today's registrations. Use mobile number for historical search.
            </div>
        `;

        // Insert after search input
        const searchContainer = userSearch?.parentElement || document.querySelector('.admin-filters');
        if (searchContainer) {
            searchContainer.appendChild(indicator);
        }
    }
}

// Go to previous page
function goToPreviousPage() {
    if (currentPage > 1) {
        // This is a simplified approach - for a real implementation,
        // you would need to store the first visible item of the previous page
        // Here we just reload from the beginning and skip to the right page
        currentPage--;
        loadUsers(false);
    }
}

// Go to next page
function goToNextPage() {
    if (lastVisible && currentPage < totalPages) {
        loadUsers(true);
    }
}

// Update pagination UI
function updatePaginationUI() {
    if (pageInfoElement) {
        pageInfoElement.textContent = `Page ${currentPage} of ${totalPages || 1}`;
    }

    if (prevPageBtn) {
        prevPageBtn.disabled = currentPage <= 1;
    }

    if (nextPageBtn) {
        nextPageBtn.disabled = currentPage >= totalPages || !lastVisible;
    }
}

// Fetch referrals counts for all users in the table
async function fetchReferralsCounts() {
    try {
        console.log('Starting fetchReferralsCounts function');

        // Get all user rows
        const userRows = document.querySelectorAll('#users-table tr.user-row');

        if (userRows.length === 0) {
            console.log('No user rows found in the table');
            return;
        }

        console.log(`Found ${userRows.length} user rows in the table`);

        // Process each user row
        for (let i = 0; i < userRows.length; i++) {
            const row = userRows[i];
            const userId = row.getAttribute('data-user-id');

            if (!userId) {
                console.log(`Row ${i} has no user ID, skipping`);
                continue;
            }

            // Get the referral code cell (5th column, index 4)
            const referralCodeCell = row.querySelector('td[data-field="referralCode"]');
            if (!referralCodeCell) {
                console.log(`Row ${i} has no referral code cell, skipping`);
                continue;
            }

            const referralCode = referralCodeCell.textContent.trim();
            if (!referralCode || referralCode === 'N/A') {
                console.log(`User ${userId} has no valid referral code, skipping`);
                continue;
            }

            console.log(`Fetching referrals for user ${userId} with code: ${referralCode}`);

            // Get the refers done cell (7th column, index 6)
            const refersDoneCell = row.querySelector('td[data-field="refersDone"]');
            if (!refersDoneCell) {
                console.log(`Row ${i} has no refers done cell, skipping`);
                continue;
            }

            try {
                // Query the users collection for referrals
                const usersQuery = query(
                    collection(db, 'users'),
                    where('referredBy', '==', referralCode)
                );

                const snapshot = await getDocs(usersQuery);
                const count = snapshot.size;

                console.log(`Found ${count} referrals for user ${userId} with code ${referralCode}`);

                // Update the refers done cell
                refersDoneCell.textContent = count;
            } catch (error) {
                console.error(`Error fetching referrals for user ${userId}:`, error);
            }
        }

        console.log('Finished fetchReferralsCounts function');
    } catch (error) {
        console.error('Error in fetchReferralsCounts:', error);
    }
}

// Validate user data before display
// Note: This function is currently unused but kept for future use
function validateUserData(userData) {
    const requiredFields = {
        fullName: 'string',
        email: 'string',
        planName: 'string',
        totalTranslations: 'number',
        lastActive: 'timestamp',
        status: 'string'
    };

    const issues = [];

    for (const [field, type] of Object.entries(requiredFields)) {
        if (!userData.hasOwnProperty(field)) {
            issues.push(`Missing field: ${field}`);
            continue;
        }

        const value = userData[field];

        switch (type) {
            case 'string':
                if (typeof value !== 'string' && value !== null) {
                    issues.push(`Invalid type for ${field}: expected string, got ${typeof value}`);
                }
                break;
            case 'number':
                if (typeof value !== 'number' && value !== null) {
                    issues.push(`Invalid type for ${field}: expected number, got ${typeof value}`);
                }
                break;
            case 'timestamp':
                if (!(value instanceof Timestamp) && value !== null) {
                    issues.push(`Invalid type for ${field}: expected Timestamp, got ${typeof value}`);
                }
                break;
        }
    }

    return {
        isValid: issues.length === 0,
        issues
    };
}

// Note: This was a duplicate loadUsers function that has been removed
// The main loadUsers function is defined above with more features and includes validation

// Note: The fetchReferralsCounts function is already defined above

// Process referral bonus manually for a user
async function processReferralBonusManually() {
    if (!currentUserId) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'No user selected'
        });
        return;
    }

    try {
        // Get user data to check if they have a referral code
        const userDoc = await getDoc(doc(db, 'users', currentUserId));
        if (!userDoc.exists()) {
            throw new Error('User not found');
        }

        const userData = userDoc.data();
        const referredBy = userData.referredBy;

        // Special handling for MY0001
        if (referredBy && referredBy.trim().toUpperCase() === 'MY0001') {
            const result = await Swal.fire({
                title: 'Process MY0001 Referral',
                text: 'This user was referred by MY0001. Would you like to process this special referral bonus?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, process bonus',
                cancelButtonText: 'Cancel'
            });

            if (result.isConfirmed) {
                // Process MY0001 referral bonus
                showLoading('Processing MY0001 referral bonus...');

                try {
                    // Get user's plan
                    if (!userData.plan || !userData.plan.name) {
                        throw new Error('User does not have an active plan');
                    }

                    const planName = userData.plan.name;
                    console.log('Processing MY0001 referral bonus for plan:', planName);

                    // Determine bonus amount based on plan
                    let bonusAmount = 0;
                    if (planName === 'Starter' || planName === 'Junior') {
                        bonusAmount = 50; // Updated bonus amount for Starter plan
                    } else if (planName === 'Premium' || planName === 'Senior' || planName === 'Executive' || planName === 'Expert') {
                        bonusAmount = 300; // Updated bonus amount for Premium plan
                    } else if (planName === 'Elite') {
                        bonusAmount = 500; // Updated bonus amount for Elite plan
                    } else if (planName === 'Ultimate') {
                        bonusAmount = 1000; // Updated bonus amount for Ultimate plan
                    } else {
                        throw new Error('Plan not eligible for referral bonus');
                    }

                    console.log('MY0001 referral bonus amount:', bonusAmount);

                    // Log the transaction data that would be created
                    const transactionData = {
                        userId: currentUserId,
                        type: 'referral_bonus',
                        amount: bonusAmount,
                        description: `MY0001 referral bonus for ${userData.fullName || userData.email} with ${planName} plan`,
                        status: 'completed',
                        timestamp: serverTimestamp(),
                        adminId: auth.currentUser.uid,
                        createdBy: 'admin'
                    };

                    console.log('MY0001 referral bonus transaction data:', transactionData);

                    // In a production environment, you would use Cloud Functions to create transactions
                    // or modify your security rules to allow admins to create transactions for any user

                    hideLoading();

                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: `MY0001 referral bonus of ₹${bonusAmount} would be processed for ${userData.fullName || userData.email}`
                    });

                    return true;
                } catch (error) {
                    console.error('Error processing MY0001 referral bonus:', error);
                    hideLoading();

                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to process MY0001 referral bonus: ' + error.message
                    });

                    return false;
                }
            }
        }
        const result = await Swal.fire({
            title: 'Process Referral Bonus',
            text: 'This will manually process the referral bonus for this user. Continue?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, process bonus',
            cancelButtonText: 'Cancel'
        });

        if (result.isConfirmed) {
            showLoading('Processing referral bonus...');

            // Verify admin status
            if (!await verifyAdminStatus('process referral bonus')) {
                return;
            }

            // Get user data
            const userDoc = await getDoc(doc(db, 'users', currentUserId));
            if (!userDoc.exists()) {
                throw new Error('User not found');
            }

            const userData = userDoc.data();
            console.log('Processing manual referral bonus for user:', userData.email);

            // Get user's plan
            if (!userData.plan || !userData.plan.name) {
                throw new Error('User does not have an active plan');
            }

            const planDetails = {
                id: userData.plan.id || 'manual',
                name: userData.plan.name,
                earningsPerBatch: userData.plan.earningsPerBatch || 0,
                duration: userData.plan.duration || 30
            };

            console.log('User plan details:', planDetails);

            // Process the referral bonus
            console.log('Checking if user is eligible for referral bonus (only processed on first plan upgrade)');
            const bonusProcessed = await processReferralBonus(currentUserId, planDetails);

            hideLoading();

            if (bonusProcessed) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Referral bonus processed successfully'
                });
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'No Bonus Processed',
                    text: 'No referral bonus was processed. The user may not have been referred, or the bonus may have already been processed previously.'
                });
            }
        }
    } catch (error) {
        console.error('Error processing referral bonus manually:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to process referral bonus: ' + error.message
        });
    }
}

// Export users with options for date range or unlimited export
async function exportUsers() {
    try {
        // Show export options dialog
        const { value: exportOption } = await Swal.fire({
            title: 'Export Users - Select Export Type',
            html: `
                <div style="text-align: left; margin: 20px 0;">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">Choose Export Type:</label>
                        <div style="margin-bottom: 10px;">
                            <input type="radio" id="export-date-range" name="export-type" value="date-range" checked>
                            <label for="export-date-range" style="margin-left: 8px;">Export by Date Range (Recommended)</label>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <input type="radio" id="export-unlimited" name="export-type" value="unlimited">
                            <label for="export-unlimited" style="margin-left: 8px;">Export All Users (No Date Limit)</label>
                        </div>
                    </div>

                    <div id="date-range-section" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">From Date:</label>
                            <input type="date" id="export-from-date" class="swal2-input" style="margin: 0;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">To Date:</label>
                            <input type="date" id="export-to-date" class="swal2-input" style="margin: 0;">
                        </div>
                        <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; padding: 10px; margin: 10px 0; font-size: 12px;">
                            <i class="fas fa-info-circle"></i>
                            <strong>Tip:</strong> Smaller date ranges reduce Firebase reads and improve performance.
                        </div>
                    </div>

                    <div id="unlimited-warning" style="display: none; background: #fff3cd; border: 1px solid #ffc107; border-radius: 4px; padding: 15px; margin: 10px 0;">
                        <div style="color: #856404; font-weight: bold; margin-bottom: 10px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            Warning: Unlimited Export
                        </div>
                        <div style="color: #856404; font-size: 12px; line-height: 1.4;">
                            • This will export ALL users from the database<br>
                            • May take several minutes for large datasets<br>
                            • Will consume more Firebase reads<br>
                            • Use only when you need complete historical data
                        </div>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Export',
            cancelButtonText: 'Cancel',
            background: 'var(--admin-card-bg)',
            color: 'var(--admin-text)',
            preConfirm: () => {
                const exportType = document.querySelector('input[name="export-type"]:checked').value;

                if (exportType === 'date-range') {
                    const fromDate = document.getElementById('export-from-date').value;
                    const toDate = document.getElementById('export-to-date').value;

                    if (!fromDate || !toDate) {
                        Swal.showValidationMessage('Please select both from and to dates');
                        return false;
                    }

                    if (new Date(fromDate) > new Date(toDate)) {
                        Swal.showValidationMessage('From date cannot be later than to date');
                        return false;
                    }

                    return {
                        type: 'date-range',
                        fromDate: fromDate,
                        toDate: toDate
                    };
                } else {
                    return {
                        type: 'unlimited'
                    };
                }
            },
            didOpen: () => {
                // Set default dates (last 30 days)
                const today = new Date();
                const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

                document.getElementById('export-to-date').value = today.toISOString().split('T')[0];
                document.getElementById('export-from-date').value = thirtyDaysAgo.toISOString().split('T')[0];

                // Add event listeners for radio buttons
                document.addEventListener('change', function(e) {
                    if (e.target.name === 'export-type') {
                        const dateRangeSection = document.getElementById('date-range-section');
                        const unlimitedWarning = document.getElementById('unlimited-warning');

                        if (e.target.value === 'unlimited') {
                            dateRangeSection.style.display = 'none';
                            unlimitedWarning.style.display = 'block';
                        } else {
                            dateRangeSection.style.display = 'block';
                            unlimitedWarning.style.display = 'none';
                        }
                    }
                });
            }
        });

        if (!exportOption) {
            return; // User cancelled
        }

        showLoading('Preparing users data for export...');

        let result;

        if (exportOption.type === 'unlimited') {
            // Show additional confirmation for unlimited export
            const confirmUnlimited = await Swal.fire({
                title: 'Confirm Unlimited Export',
                html: `
                    <div style="text-align: left;">
                        <div style="background: #fff3cd; border: 1px solid #ffc107; border-radius: 4px; padding: 15px; margin: 10px 0;">
                            <div style="color: #856404; font-weight: bold; margin-bottom: 10px;">
                                <i class="fas fa-exclamation-triangle"></i>
                                You are about to export ALL users
                            </div>
                            <div style="color: #856404; font-size: 12px; line-height: 1.4;">
                                • This will export every user in the database<br>
                                • No date restrictions will be applied<br>
                                • This may take several minutes<br>
                                • Higher Firebase usage will occur<br>
                                • Only proceed if you need complete data
                            </div>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Yes, Export All Users',
                cancelButtonText: 'Cancel',
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)',
                confirmButtonColor: '#ff9800'
            });

            if (!confirmUnlimited.isConfirmed) {
                hideLoading();
                return;
            }

            // Use unlimited export function
            const { getAllUsersForExport } = await import('./admin-data-service.js');

            result = await getAllUsersForExport({
                statusFilter: currentFilters.status !== 'all' ? currentFilters.status : null,
                planFilter: currentFilters.plan !== 'all' ? currentFilters.plan : null,
                searchTerm: currentFilters.search || null
            });
        } else {
            // Date range export
            const fromDate = new Date(exportOption.fromDate);
            const toDate = new Date(exportOption.toDate);
            const daysDiff = Math.ceil((toDate - fromDate) / (1000 * 60 * 60 * 24));
            const estimatedReads = Math.min(daysDiff * 50, 1000); // Estimate 50 users per day, max 1000

            const confirmExport = await Swal.fire({
                title: 'Confirm Date Range Export',
                html: `
                    <div style="text-align: left;">
                        <p><strong>Date Range:</strong> ${exportOption.fromDate} to ${exportOption.toDate}</p>
                        <p><strong>Duration:</strong> ${daysDiff} days</p>
                        <p><strong>Estimated Firebase Reads:</strong> ~${estimatedReads}</p>
                        <div style="background: #fff3e0; border: 1px solid #ff9800; border-radius: 4px; padding: 10px; margin: 10px 0; font-size: 12px;">
                            <i class="fas fa-info-circle"></i>
                            Smaller date ranges use fewer Firebase reads and export faster.
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Proceed with Export',
                cancelButtonText: 'Cancel',
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)'
            });

            if (!confirmExport.isConfirmed) {
                hideLoading();
                return;
            }

            // Use date range export function
            const { getUsersForDateRange } = await import('./admin-data-service.js');

            result = await getUsersForDateRange({
                fromDate: fromDate,
                toDate: toDate,
                statusFilter: currentFilters.status !== 'all' ? currentFilters.status : null,
                planFilter: currentFilters.plan !== 'all' ? currentFilters.plan : null,
                searchTerm: currentFilters.search || null
            });
        }

        if (!result.users || result.users.length === 0) {
            hideLoading();
            Swal.fire({
                icon: 'info',
                title: 'No Data',
                text: 'There are no users to export.'
            });
            return;
        }

        // Show info about export type
        if (exportOption.type === 'unlimited') {
            if (result._unlimited) {
                await Swal.fire({
                    icon: 'success',
                    title: 'Unlimited Export',
                    text: `Successfully exported ${result.users.length} users with no date restrictions.`,
                    background: 'var(--admin-card-bg)',
                    color: 'var(--admin-text)'
                });
            }
        } else if (result.users.length === 1000) {
            await Swal.fire({
                icon: 'info',
                title: 'Limited Export',
                text: `Export limited to 1000 most recent users to optimize Firebase usage. Use "Export All Users" option for complete export.`,
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)'
            });
        }

        // Process users data
        let usersData = result.users;

        // Apply client-side filters if needed
        if (currentFilters.plan !== 'all') {
            usersData = usersData.filter(user =>
                user.plan && user.plan.name === currentFilters.plan
            );
        }

        if (currentFilters.search && currentFilters.search.trim() !== '') {
            const searchTerm = currentFilters.search.toLowerCase();
            usersData = usersData.filter(user => {
                const standardizedUser = standardizeUserData(user);
                return (
                    (standardizedUser.fullName && standardizedUser.fullName.toLowerCase().includes(searchTerm)) ||
                    (standardizedUser.email && standardizedUser.email.toLowerCase().includes(searchTerm)) ||
                    (standardizedUser.phoneNumber && standardizedUser.phoneNumber.includes(searchTerm)) ||
                    (standardizedUser.referralCode && standardizedUser.referralCode.toLowerCase().includes(searchTerm)) ||
                    (standardizedUser.referredBy && standardizedUser.referredBy.toLowerCase().includes(searchTerm))
                );
            });
        }

        // Standardize data for export
        const standardizedData = usersData.map(user => {
            // Calculate short video balance days if available
            let shortVideoBalanceDays = 0;
            if (user.shortVideoAdvantage && user.shortVideoExpiryDate) {
                try {
                    const expiryDate = user.shortVideoExpiryDate.toDate ? user.shortVideoExpiryDate.toDate() : new Date(user.shortVideoExpiryDate);
                    const currentDate = new Date();
                    shortVideoBalanceDays = Math.max(0, Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24)));
                } catch (error) {
                    console.error('Error calculating short video balance days:', error);
                }
            }

            // Create a standardized user object with all fields flattened
            return {
                id: user.id || '',
                fullName: user.fullName || user.fullname || '',
                email: user.email || '',
                phoneNumber: user.phoneNumber || user.phone || '',
                mobile: user.mobile || user.phoneNumber || '',
                status: user.status || '',
                referralCode: user.referralCode || '',
                referredBy: user.referredBy || '',
                createdAt: user.createdAt ? formatDate(user.createdAt.toDate()) : '',
                lastLogin: user.lastLogin ? formatDate(user.lastLogin.toDate()) : '',
                city: user.city || '',

                // Plan details
                planName: user.plan?.name || '',
                planStartDate: user.planActivatedAt ? formatDate(user.planActivatedAt.toDate()) : '',
                planEndDate: user.planExpiresAt ? formatDate(user.planExpiresAt.toDate()) : '',
                planDuration: user.plan?.duration || '',
                planRate: user.plan?.rate || '',
                planEarningsPerBatch: user.plan?.earningsPerBatch || '',
                planDailyLimit: user.plan?.dailyLimit || 100,
                planValidity: user.plan?.validity || '',
                planPrice: user.plan?.price || '',

                // Active days and leave days
                activeDays: user.activeDays || 0,
                leaveDaysTaken: user.leaveDaysTaken || 0,
                lastActiveDayUpdate: user.lastActiveDayUpdate ? formatDate(user.lastActiveDayUpdate.toDate()) : '',

                // Wallet details
                walletBalance: user.wallet?.balance || user.walletBalance || 0,
                walletEarning: user.wallet?.earning || 0,
                walletBonus: user.wallet?.bonus || 0,

                // Bank details
                bankName: user.bankDetails?.bank || user.bankDetails?.bankName || '',
                accountNumber: user.bankDetails?.account || user.bankDetails?.accountNumber || '',
                ifscCode: user.bankDetails?.ifsc || user.bankDetails?.ifscCode || '',
                accountHolder: user.bankDetails?.holder || user.bankDetails?.accountHolder || '',

                // Stats
                totalVideosCompleted: user.stats?.totalVideosCompleted || 0,
                totalTranslationsCompleted: user.stats?.totalTranslationsCompleted || 0,
                dailyVideosCount: user.stats?.dailyVideos?.count || 0,
                dailyTranslationsCount: user.stats?.dailyTranslations?.count || 0,
                totalEarnings: user.stats?.totalEarnings || 0,
                totalReferrals: user.stats?.referrals || 0,

                // Short video advantage
                shortVideoAdvantage: user.shortVideoAdvantage ? 'Yes' : 'No',
                shortVideoBalanceDays: shortVideoBalanceDays,
                shortVideoExpiryDate: user.shortVideoExpiryDate ? formatDate(user.shortVideoExpiryDate.toDate()) : '',
                advantageSource: user.advantageSource || '',

                // Copy/Paste permissions
                copyPasteEnabled: user.copyPasteEnabled ? 'Yes' : 'No',
                copyPasteExpiryDate: user.copyPasteExpiryDate ? formatDate(new Date(user.copyPasteExpiryDate)) : '',

                // Additional fields
                updatedAt: user.updatedAt ? formatDate(user.updatedAt.toDate()) : '',
                updatedBy: user.updatedBy || '',
            };
        });

        hideLoading();

        // Generate filename with date and export type
        const date = new Date().toISOString().split('T')[0];
        const exportTypeLabel = exportOption.type === 'unlimited' ? 'all' : 'filtered';
        const filename = `mytube_users_${exportTypeLabel}_${date}.xlsx`;

        // Export to Excel
        try {
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(standardizedData);
            XLSX.utils.book_append_sheet(wb, ws, 'Users');
            XLSX.writeFile(wb, filename);

            const exportTypeText = exportOption.type === 'unlimited' ? 'ALL' : 'filtered';
            Swal.fire({
                icon: 'success',
                title: 'Export Successful',
                text: `Exported ${standardizedData.length} ${exportTypeText} MyTube users to ${filename}`
            });
        } catch (error) {
            console.error('Error exporting to Excel:', error);

            // Fallback to CSV if Excel export fails
            const csvContent = 'data:text/csv;charset=utf-8,' +
                Object.keys(standardizedData[0]).join(',') + '\n' +
                standardizedData.map(row =>
                    Object.values(row).map(val =>
                        typeof val === 'string' ? `"${val.replace(/"/g, '""')}"` : val
                    ).join(',')
                ).join('\n');

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', filename.replace('.xlsx', '.csv'));
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            Swal.fire({
                icon: 'success',
                title: 'Export Successful (CSV)',
                text: `Exported ${standardizedData.length} MyTube users to CSV format`
            });
        }
    } catch (error) {
        console.error('Error exporting users:', error);
        hideLoading();
        Swal.fire({
            icon: 'error',
            title: 'Export Failed',
            text: 'Failed to export users data: ' + error.message
        });
    }
}

// This function is no longer used - we're using the modal edit form instead
// Keeping the function signature to avoid breaking any existing code references
function toggleRowEditMode(row, userData) {
    console.log('toggleRowEditMode is deprecated - using modal edit form instead');
    // Instead of inline editing, open the modal edit form
    if (userData && userData.id) {
        editUser(userData.id);
    }
}

// Cancel row editing
function cancelEditRow(row) {
    // Remove editing class
    row.classList.remove('editing');

    // Restore original content
    if (row.dataset.originalContent) {
        row.innerHTML = row.dataset.originalContent;
    } else {
        // If no original content stored, refresh the table
        loadUsers(false);
    }
}

// Save user data from inline edit
async function saveUserData(userId, row) {
    try {
        // Verify admin status
        if (!await verifyAdminStatus('edit users')) {
            return;
        }

        showLoading('Saving user data...');

        // Try to get user data from currentUsers array
        let userData = currentUsers.find(user => user.id === userId);
        console.log('Looking for user with ID:', userId);
        console.log('Current users array length:', currentUsers.length);
        console.log('User found in currentUsers array:', userData ? 'Yes' : 'No');

        // If not found in the array, fetch directly from Firestore
        if (!userData) {
            console.log('User data not found in currentUsers array, fetching from Firestore...');
            try {
                // Validate userId
                if (!userId || typeof userId !== 'string' || userId.trim() === '') {
                    throw new Error('Invalid user ID');
                }

                // Fetch user document
                const userDoc = await getDoc(doc(db, 'users', userId));
                console.log('User document exists in Firestore:', userDoc.exists() ? 'Yes' : 'No');

                if (!userDoc.exists()) {
                    // If user doesn't exist, create a new user object with default values
                    console.warn(`User document with ID ${userId} not found in Firestore. Creating default user data.`);

                    // Get values from inputs to create a new user
                    const fullName = row.querySelector('td[data-field="fullName"] input').value.trim();
                    const email = row.querySelector('td[data-field="email"] input').value.trim();
                    const phoneNumber = row.querySelector('td[data-field="phoneNumber"] input').value.trim();

                    // Create default user data
                    const defaultUserData = {
                        fullName: fullName || 'New User',
                        email: email || '',
                        phoneNumber: phoneNumber || '',
                        mobile: phoneNumber || '',
                        status: 'active',
                        createdAt: serverTimestamp(),
                        wallet: {
                            earning: 0,
                            bonus: 0,
                            balance: 0
                        },
                        plan: {
                            name: 'Trial',
                            duration: 30,
                            earningsPerBatch: 25
                        },
                        stats: {
                            totalTranslations: 0,
                            totalEarnings: 0
                        },
                        referralCode: generateReferralCode(),
                        referredBy: ''
                    };

                    console.log('Created default user data for new user:', defaultUserData);

                    const standardizedUser = standardizeUserData(defaultUserData);
                    userData = { id: userId, ...defaultUserData, _standardized: standardizedUser };

                    // Add to currentUsers array for future reference
                    currentUsers.push(userData);

                    // Create the user in Firestore
                    await setDoc(doc(db, 'users', userId), defaultUserData);
                    console.log('New user created in Firestore with ID:', userId);
                } else {
                    // Create user data object with the same structure as in currentUsers
                    const rawUserData = userDoc.data();
                    console.log('Raw user data from Firestore:', rawUserData);

                    // Ensure wallet structure exists
                    if (!rawUserData.wallet) {
                        console.log('Creating wallet structure as it does not exist');
                        rawUserData.wallet = {
                            earning: 0,
                            bonus: 0,
                            balance: rawUserData.walletBalance || 0
                        };
                    }

                    // Ensure wallet fields exist
                    if (rawUserData.wallet.earning === undefined) rawUserData.wallet.earning = 0;
                    if (rawUserData.wallet.bonus === undefined) rawUserData.wallet.bonus = 0;
                    if (rawUserData.wallet.balance === undefined) {
                        rawUserData.wallet.balance = rawUserData.walletBalance || 0;
                    }

                    // Ensure plan structure exists
                    if (!rawUserData.plan || typeof rawUserData.plan !== 'object') {
                        console.log('Creating plan structure as it does not exist or is not an object');
                        rawUserData.plan = {
                            name: rawUserData.plan || 'Trial',
                            duration: 30,
                            earningsPerBatch: 25
                        };
                    }

                    // Ensure stats structure exists
                    if (!rawUserData.stats) {
                        console.log('Creating stats structure as it does not exist');
                        rawUserData.stats = {
                            totalTranslations: 0,
                            totalEarnings: 0
                        };
                    }

                    console.log('Processed user data before standardization:', rawUserData);
                    const standardizedUser = standardizeUserData(rawUserData);
                    console.log('Standardized user data:', standardizedUser);
                    userData = { id: userId, ...rawUserData, _standardized: standardizedUser };

                    // Add to currentUsers array for future reference
                    currentUsers.push(userData);

                    console.log('User data fetched successfully from Firestore');
                }
            } catch (fetchError) {
                console.error('Error fetching user data from Firestore:', fetchError);
                throw new Error('Failed to fetch user data: ' + fetchError.message);
            }
        }

        // Get values from inputs
        const fullName = row.querySelector('td[data-field="fullName"] input').value.trim();
        const email = row.querySelector('td[data-field="email"] input').value.trim();
        const phoneNumber = row.querySelector('td[data-field="phoneNumber"] input').value.trim();
        const referralCode = row.querySelector('td[data-field="referralCode"] input').value.trim();
        const referredBy = row.querySelector('td[data-field="referredBy"] input').value.trim();
        const planSelect = row.querySelector('td[data-field="plan"] select');
        const newPlan = planSelect ? planSelect.value : userData.plan.name;
        const translations = parseInt(row.querySelector('td[data-field="translations"] input').value) || 0;
        const earnings = parseFloat(row.querySelector('td[data-field="earnings"] input').value) || 0;
        const earningWallet = parseFloat(row.querySelector('td[data-field="earningWallet"] input').value) || 0;
        const bonusWallet = parseFloat(row.querySelector('td[data-field="bonusWallet"] input').value) || 0;
        const mainWallet = parseFloat(row.querySelector('td[data-field="mainWallet"] input').value) || 0;

        // Validate required fields
        if (!fullName) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Name is required',
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)',
                customClass: {
                    title: 'gradient-text',
                    popup: 'glass-card',
                    confirmButton: 'admin-btn'
                }
            });
            hideLoading();
            return;
        }

        // Create update data object
        const updateData = {
            fullName,
            email,
            mobile: phoneNumber,
            phoneNumber,
            referralCode,
            referredBy: referredBy === 'N/A' ? '' : referredBy,
            // Update wallet fields individually instead of replacing the entire object
            'wallet.earning': earningWallet,
            'wallet.bonus': bonusWallet,
            'wallet.balance': mainWallet,
            // Update stats fields individually
            'stats.totalTranslations': translations,
            'stats.totalEarnings': earnings,
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid
        };

        // Preserve copy/paste permission settings if they exist
        if (userData.copyPastePermission !== undefined) {
            updateData.copyPastePermission = userData.copyPastePermission;
        }

        if (userData.copyPasteExpiryDate) {
            updateData.copyPasteExpiryDate = userData.copyPasteExpiryDate;
        }

        // Handle plan changes
        const oldPlan = userData.plan?.name || 'Trial';
        const planDuration = 30; // Default to 30 days

        // Update plan fields individually using dot notation
        updateData['plan.name'] = newPlan;
        updateData['plan.duration'] = planDuration;

        // If plan changed, reset active days and set plan activation date
        if (newPlan !== oldPlan) {
            updateData['activeDays'] = 1;
            updateData['planActivatedAt'] = serverTimestamp();

            // Set earnings per batch based on plan
            let earningsPerBatch = 10; // Default for Trial
            let planRate = 0.1; // Default for Trial
            if (newPlan === 'Trial') {
                earningsPerBatch = 10; // ₹0.1 per video * 100 videos
                planRate = 0.1;
            } else if (newPlan === 'Starter') {
                earningsPerBatch = 25; // ₹0.25 per video * 100 videos
                planRate = 0.25;
            } else if (newPlan === 'Premium') {
                earningsPerBatch = 150; // ₹1.5 per video * 100 videos
                planRate = 1.5;
            }

            // Update plan.earningsPerBatch, earningsPerBatch, and plan.rate fields
            updateData['plan.earningsPerBatch'] = earningsPerBatch;
            updateData['earningsPerBatch'] = earningsPerBatch;
            updateData['plan.rate'] = planRate;

            // Calculate expiry date
            const now = new Date();
            const expiryDate = new Date(now.getTime() + (planDuration * 24 * 60 * 60 * 1000));
            updateData.planExpiresAt = Timestamp.fromDate(expiryDate);

            // Add transaction record for plan activation
            await addDoc(collection(db, 'transactions'), {
                userId: userId,
                type: 'plan',
                amount: 0, // Free activation by admin
                description: `${newPlan} plan activated by admin for ${planDuration} days`,
                status: 'completed',
                timestamp: serverTimestamp(),
                planDetails: {
                    name: newPlan,
                    duration: planDuration,
                    earningsPerBatch: updateData.plan.earningsPerBatch
                }
            });
        }

        // Update user document
        console.log('Updating user with data:', updateData);

        try {
            // Use updateDoc instead of setDoc for better handling of nested fields
            console.log('Using updateDoc for better handling of nested fields...');
            await updateDoc(doc(db, 'users', userId), updateData);
            console.log('User updated successfully with updateDoc');
        } catch (updateError) {
            console.error('Error with updateDoc:', updateError);

            // If updateDoc fails, try an alternative approach with setDoc
            try {
                console.log('Trying alternative approach with setDoc and merge...');
                // Create a different structure for the update
                const alternativeUpdateData = {
                    fullName,
                    email,
                    mobile: phoneNumber,
                    phoneNumber,
                    referralCode,
                    referredBy: referredBy === 'N/A' ? '' : referredBy,
                    wallet: {
                        earning: earningWallet,
                        bonus: bonusWallet,
                        balance: mainWallet
                    },
                    stats: {
                        totalTranslations: translations,
                        totalEarnings: earnings
                    },
                    updatedAt: serverTimestamp(),
                    updatedBy: auth.currentUser.uid
                };

                await setDoc(doc(db, 'users', userId), alternativeUpdateData, { merge: true });
                console.log('User updated successfully with alternative approach');
            } catch (secondError) {
                console.error('Error with alternative approach:', secondError);
                throw new Error('Failed to save user data: ' + updateError.message + ' | Second attempt: ' + secondError.message);
            }
        }

        // Process referral bonus if plan changed to a paid plan
        if (newPlan !== oldPlan) {
            // Normalize the plan name to ensure consistency
            const normalizedPlanName = normalizePlanName(newPlan);
            console.log('Normalized plan name for referral bonus:', normalizedPlanName);

            // Process for all paid plans (both current and legacy plan names)
            if (['Starter', 'Premium', 'Junior', 'Senior', 'Executive', 'Expert'].includes(normalizedPlanName)) {
                try {
                    console.log('Processing referral bonus for user:', userId, 'with plan:', newPlan);

                    // Get user data to check referral code
                    const userDoc = await getDoc(doc(db, 'users', userId));
                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        console.log('User referral data:', {
                            email: userData.email,
                            referredBy: userData.referredBy,
                            plan: newPlan
                        });
                    }

                    // Use normalized plan name in the plan details
                    const normalizedPlanDetails = {
                        name: normalizedPlanName,
                        duration: planDuration,
                        earningsPerBatch: updateData.plan.earningsPerBatch
                    };

                    console.log('Checking if user is eligible for referral bonus (only processed on first plan upgrade)');
                    const bonusProcessed = await processReferralBonus(userId, normalizedPlanDetails);
                    if (bonusProcessed) {
                        console.log('Referral bonus processed successfully');
                    } else {
                        console.log('No referral bonus processed (user may not have been referred or already received a bonus)');
                    }
                } catch (referralError) {
                    console.error('Error processing referral bonus:', referralError);
                    // Continue even if referral bonus processing fails
                }
            } else {
                console.log('Plan not eligible for referral bonus:', newPlan);
            }
        }

        hideLoading();

        // Show success message with animation
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'User data has been updated successfully',
            background: 'var(--admin-card-bg)',
            color: 'var(--admin-text)',
            customClass: {
                title: 'gradient-text',
                popup: 'glass-card',
                confirmButton: 'admin-btn'
            },
            showClass: {
                popup: 'animate__animated animate__fadeInUp animate__faster'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutDown animate__faster'
            }
        }).then(() => {
            // Refresh the table
            loadUsers(false);
        });

    } catch (error) {
        console.error('Error saving user data:', error);
        hideLoading();

        // Check if it's a permission error
        const isPermissionError = error.message && error.message.includes('permission');

        if (isPermissionError) {
            Swal.fire({
                icon: 'error',
                title: 'Permission Error',
                html: `
                    <p>Failed to save user data due to permission issues.</p>
                    <p>This may be related to the wallet structure.</p>
                    <p><strong>Error details:</strong> ${error.message}</p>
                    <p>Please try again or contact the developer.</p>
                `,
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)',
                customClass: {
                    title: 'gradient-text',
                    popup: 'glass-card',
                    confirmButton: 'admin-btn'
                }
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to save user data: ' + error.message,
                background: 'var(--admin-card-bg)',
                color: 'var(--admin-text)',
                customClass: {
                    title: 'gradient-text',
                    popup: 'glass-card',
                    confirmButton: 'admin-btn'
                }
            });
        }
    }
}

// Debounce function for search input
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Note: generateReferralCode function is already defined above

// Check and ensure XLSX library is loaded
function ensureXLSXLibrary() {
    return new Promise((resolve, reject) => {
        if (typeof XLSX !== 'undefined') {
            console.log('XLSX library is already available');
            resolve(true);
            return;
        }

        console.log('XLSX library not found, attempting to load...');
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
        script.onload = function() {
            console.log('XLSX library loaded successfully');
            resolve(true);
        };
        script.onerror = function() {
            console.error('Failed to load XLSX library');
            reject(new Error('Failed to load XLSX library'));
        };
        document.head.appendChild(script);
    });
}

// Initialize users page
async function initUsersPage() {
    showLoading('Loading users data...');

    try {
        // Ensure XLSX library is loaded
        try {
            await ensureXLSXLibrary();
        } catch (xlsxError) {
            console.warn('XLSX library could not be loaded:', xlsxError.message);
        }
        console.log('Initializing admin users page with active days fix for Trial users...');

        // Add event listeners
        setupEventListeners();

        // Load plans for filter dropdown
        await loadPlans();

        // Load initial users data
        await loadUsers(false);

        // Log that we're using the active days utility for consistent handling
        console.log('Using getValidatedActiveDays utility for consistent active days handling across all plan types');

        hideLoading();
    } catch (error) {
        console.error('Error initializing users page:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load users data: ' + error.message,
        });
    }
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', initUsersPage);





