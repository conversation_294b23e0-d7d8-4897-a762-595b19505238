// Import Firebase modules and functions
import {
    auth,
    onAuthStateChanged
} from './firebase-config.js';

// Import data service
import {
    getUserData,
    getWalletData,
    getVideoCountData,
    getTransactions,
    getReferrals,
    updateUserData,
    addTransaction,
    FIELD_NAMES
} from './data-service.js';

// Import Firebase increment function
import { increment } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

// Import cache utilities
import {
    getCachedUserData
} from './cache-utils.js';

// Import cross-page data sharing utilities
import { setupPageTransitionHandlers } from './page-transition.js';
import {
    updateLastLoaded,
    wasRecentlyLoaded,
    cameFromPage,
    DATA_TYPES,
    CACHE_DURATIONS as MAX_AGE
} from './shared-data-store.js';

// Import active days utilities
import {
    updateActiveDaysIfNeeded,
    getCompletePlanInfoSync
} from './active-days-utils.js';

// DOM Elements
const userGreeting = document.getElementById('userGreeting');
const userName = document.getElementById('userName');
const userPlan = document.getElementById('userPlan');
const todayQR = document.getElementById('todayQR');
const totalQR = document.getElementById('totalQR');
const todayEarnings = document.getElementById('todayEarnings');
const totalEarnings = document.getElementById('totalEarnings');
const totalReferrals = document.getElementById('totalReferrals');
const referralEarnings = document.getElementById('referralEarnings');
const loginHistory = document.getElementById('loginHistory');
// Plan card removed
// const currentPlanCard = document.getElementById('currentPlanCard');

// Set up page transition handlers
setupPageTransitionHandlers('dashboard');

// Function to show debug information for active days
async function showActiveDaysDebugInfo() {
    try {
        const userId = auth.currentUser?.uid || localStorage.getItem('userId');
        if (!userId) {
            console.error('No user ID available for debug');
            return;
        }

        const userData = await refreshUserData(userId);
        if (!userData) {
            console.error('No user data available for debug');
            return;
        }

        // Get complete plan info using the synchronous version
        const planInfo = getCompletePlanInfoSync(userData);

        // Show debug information in a dialog
        Swal.fire({
            title: 'Active Days Debug Info',
            html: `
                <div style="text-align: left; font-family: monospace; background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">
                    <p><strong>Plan Type:</strong> ${planInfo.planType}</p>
                    <p><strong>Active Days:</strong> ${planInfo.activeDays}</p>
                    <p><strong>Valid Days:</strong> ${planInfo.validDays}</p>
                    <p><strong>Days Left:</strong> ${planInfo.daysLeft}</p>
                    <p><strong>Plan Activated:</strong> ${planInfo.planActivationDate || 'Unknown'}</p>
                    <p><strong>Plan Expired:</strong> ${planInfo.expired ? 'Yes' : 'No'}</p>
                    <hr>
                    <p><strong>Raw Data:</strong></p>
                    <pre style="white-space: pre-wrap; word-break: break-all;">${JSON.stringify(userData, null, 2)}</pre>
                </div>
            `,
            width: '600px',
            confirmButtonText: 'Close'
        });

    } catch (error) {
        console.error('Error showing active days debug info:', error);
        Swal.fire({
            icon: 'error',
            title: 'Debug Error',
            text: 'Failed to load active days debug information.'
        });
    }
}

// Set up debug button if in development mode
document.addEventListener('DOMContentLoaded', () => {
    const debugBtn = document.getElementById('debugActiveDaysBtn');
    if (debugBtn) {
        // Show debug button in development mode or if URL has debug parameter
        const isDevMode = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.search.includes('debug=true');

        if (isDevMode) {
            debugBtn.style.display = 'block';
            debugBtn.addEventListener('click', showActiveDaysDebugInfo);
        }
    }
});

// Check authentication state
onAuthStateChanged(auth, async (user) => {
    // First check if we have an admin password login stored in localStorage
    const adminPasswordLogin = localStorage.getItem('adminPasswordLogin');
    const userId = localStorage.getItem('userId');

    if (adminPasswordLogin === 'true' && userId) {
        console.log('User is signed in with admin-set password');
        try {
            // Load user data using the data service
            const userData = await getUserData(userId, true); // Force refresh for admin login

            if (userData) {
                console.log('User data loaded for admin password login:', userData);

                // Update UI
                updateDashboardUI(userData, true); // true indicates admin password login

                // Load other data
                loadReferralData(userId);
                loadLoginHistory(userId);

                // Show a notification about admin password
                const passwordNotification = document.createElement('div');
                passwordNotification.className = 'admin-password-notification';
                passwordNotification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-key"></i>
                        <p>You are using an admin-set password. For security, please update your password in your profile.</p>
                        <button class="close-notification"><i class="fas fa-times"></i></button>
                    </div>
                `;
                document.body.appendChild(passwordNotification);

                // Add event listener to close button
                const closeBtn = passwordNotification.querySelector('.close-notification');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        passwordNotification.remove();
                    });
                }

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (document.body.contains(passwordNotification)) {
                        passwordNotification.remove();
                    }
                }, 10000);
            } else {
                console.error('No user data found for admin password login');
                localStorage.removeItem('adminPasswordLogin');
                localStorage.removeItem('userId');
                window.location.href = 'login.html';
            }
        } catch (error) {
            console.error('Error getting user data for admin password login:', error);
            localStorage.removeItem('adminPasswordLogin');
            localStorage.removeItem('userId');
            window.location.href = 'login.html';
        }
    } else if (user) {
        console.log('User is signed in with Firebase Auth:', user.email);
        try {
            // Use data service to get user data with caching
            const userData = await getUserData(user.uid);

            if (userData) {
                // Update UI with user data
                updateDashboardUI(userData);

                // Load other data
                loadReferralData(user.uid);
                loadLoginHistory(user.uid);
            } else {
                console.error('No user data found');
            }
        } catch (error) {
            console.error('Error getting user data:', error);
        }
    } else {
        console.log('No user is signed in');
        window.location.href = 'index.html';
    }
});

/**
 * Refresh user data from Firebase in the background
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} - Fresh user data or null
 */
async function refreshUserData(userId) {
    try {
        console.log('Refreshing user data from Firebase for user ID:', userId);

        // Use data service to get fresh user data
        const userData = await getUserData(userId, true); // Force refresh

        if (userData) {
            console.log('Fresh user data loaded from Firebase');

            // Update active days if needed using our utility function
            const updatedUserData = await updateActiveDaysIfNeeded(userId, userData);

            if (updatedUserData !== userData) {
                // If active days were updated, update the user data in Firebase
                await updateUserData(userId, {
                    activeDays: updatedUserData.activeDays,
                    [`${FIELD_NAMES.ACTIVE_DAYS}`]: updatedUserData[FIELD_NAMES.ACTIVE_DAYS]
                });
                console.log('Active days updated in Firebase');
            }

            // Log active days information for debugging
            const planInfo = getCompletePlanInfoSync(updatedUserData);
            console.log('Active days information (from refreshUserData):', {
                activeDays: planInfo.activeDays,
                validDays: planInfo.validDays,
                daysLeft: planInfo.daysLeft,
                planType: planInfo.planType,
                planActivationDate: planInfo.planActivationDate
            });

            return updatedUserData;
        }

        console.warn('User document not found in refreshUserData');
        return null;
    } catch (error) {
        console.error('Error refreshing user data:', error);
        return null;
    }
}

// Update dashboard UI with user data
function updateDashboardUI(userData, isAdminPasswordLogin = false) {
    console.log('Updating dashboard UI with user data');

    // Get current user from auth or use stored data for admin password login
    const currentUser = isAdminPasswordLogin ? null : auth.currentUser;

    // Prefer Firestore fullName, fallback to Auth, then to localStorage, then to 'User'
    const displayName = userData.fullName ||
                        currentUser?.displayName ||
                        localStorage.getItem('userName') ||
                        'User';

    if (userGreeting) userGreeting.textContent = displayName;
    if (userName) userName.textContent = displayName;

    // Get complete plan info using our utility function
    console.log('Getting plan info from userData:', userData);

    // Check if userData has plan information
    if (!userData.plan) {
        console.warn('userData.plan is missing or undefined:', userData.plan);

        // Add a default plan object if it's missing
        userData.plan = {
            name: 'Trial',
            dailyLimit: 100,
            rate: 0.1,
            duration: 2,
            earningsPerBatch: 10
        };
        console.log('Added default plan information to userData:', userData.plan);
    } else {
        console.log('userData.plan details:', userData.plan);
    }

    // Ensure activeDays exists
    if (userData.activeDays === undefined || userData.activeDays === null) {
        userData.activeDays = 1;
        console.log('Added default activeDays value:', userData.activeDays);
    }

    // Use the synchronous version to avoid any async issues
    const planInfo = getCompletePlanInfoSync(userData);
    console.log('Plan info from utility (sync version):', planInfo);

    // Update plan name display
    if (userPlan) userPlan.textContent = planInfo.planType || 'Free Plan';

    // Add a visual indicator if using admin password login
    if (isAdminPasswordLogin) {
        // Add a small key icon next to the username to indicate admin password login
        if (userName && !userName.querySelector('.admin-password-icon')) {
            const keyIcon = document.createElement('i');
            keyIcon.className = 'fas fa-key admin-password-icon';
            keyIcon.style.fontSize = '0.8em';
            keyIcon.style.marginLeft = '5px';
            keyIcon.title = 'Using admin-set password';
            userName.appendChild(keyIcon);
        }
    }

    // Update the plan display in the upgrade card
    const userPlanDisplay = document.getElementById('userPlanDisplay');
    if (userPlanDisplay) {
        userPlanDisplay.textContent = planInfo.planType || 'Trial';
        console.log('Updated plan display to:', planInfo.planType || 'Trial');
    }

    // Update active days display if element exists
    const activeDaysElement = document.getElementById('activeDays');
    if (activeDaysElement) {
        // Ensure we have valid numbers for activeDays and validDays
        const activeDays = typeof planInfo.activeDays === 'number' ? planInfo.activeDays : 1;
        const validDays = typeof planInfo.validDays === 'number' ? planInfo.validDays : (planInfo.planType === 'Trial' ? 2 : 30);

        activeDaysElement.textContent = `${activeDays}/${validDays}`;
        activeDaysElement.title = `Day ${activeDays} of your ${validDays}-day ${planInfo.planType} plan`;
        console.log('Updated active days display to:', `${activeDays}/${validDays}`);
    }

    // Update plan activation date if element exists
    const planActivationElement = document.getElementById('planActivationDate');
    if (planActivationElement && planInfo.planActivationDate) {
        planActivationElement.textContent = `Activated: ${planInfo.planActivationDate}`;
        planActivationElement.style.display = 'block';
    } else if (planActivationElement) {
        planActivationElement.style.display = 'none';
    }

    // Wallet balances
    if (document.getElementById('earningBalance')) {
        document.getElementById('earningBalance').textContent = `₹${userData.wallet?.earning || 0}`;
    }
    if (document.getElementById('bonusBalance')) {
        document.getElementById('bonusBalance').textContent = `₹${userData.wallet?.bonus || 0}`;
    }
    if (document.getElementById('mainBalance')) {
        document.getElementById('mainBalance').textContent = `₹${userData.wallet?.balance || 0}`;
    }

    // Statistics
    const stats = userData.stats || {};

    // For total videos:
    // 1. First try stats.totalVideosCompleted (primary field)
    // 2. Then try stats.totalTranslationsCompleted (legacy field)
    // 3. Then try translationsCount (legacy field at root level)
    // 4. Fall back to 0 if none exists
    if (totalQR) {
        const totalVideos = stats.totalVideosCompleted || stats.totalTranslationsCompleted || userData.translationsCount || 0;
        totalQR.textContent = totalVideos;
    }

    // For today's videos:
    // 1. First try stats.todayVideos (primary field)
    // 2. Then try stats.todayTranslations (legacy field)
    // 3. Then try stats.dailyVideos.count (structured field)
    // 4. Then try stats.dailyTranslations.count (legacy structured field)
    // 5. Fall back to 0 if none exists
    if (todayQR) {
        // Check if videos have been submitted for today - check multiple indicators
        const videosSubmitted =
            stats.dailyVideos?.submitted === true ||
            stats.dailyTranslations?.submitted === true ||
            (stats.lastSubmission && stats.lastSubmission.submitted === true &&
             stats.lastSubmission.count === 100 &&
             stats.lastSubmission.date === new Date().toISOString().split('T')[0]);

        // If videos have been submitted, always show 100 as the count
        if (videosSubmitted) {
            console.log('Videos already submitted for today, showing 100 as the count on dashboard');
            todayQR.textContent = 100;

            // Also update Firebase to ensure consistency if the count doesn't match
            const currentCount = stats.todayVideos || stats.todayTranslations || stats.dailyVideos?.count || stats.dailyTranslations?.count || 0;
            if (currentCount !== 100) {
                try {
                    const userId = auth.currentUser?.uid || localStorage.getItem('userId');
                    if (userId) {
                        const userRef = doc(db, 'users', userId);
                        updateDoc(userRef, {
                            'stats.todayVideos': 100,
                            'stats.todayTranslations': 100,
                            'stats.dailyVideos.count': 100,
                            'stats.dailyTranslations.count': 100
                        }).then(() => {
                            console.log('Updated Firebase with submitted count of 100 from dashboard');
                        }).catch(error => {
                            console.error('Error updating Firebase with submitted count from dashboard:', error);
                        });
                    }
                } catch (firebaseError) {
                    console.error('Error updating Firebase in dashboard:', firebaseError);
                }
            }
        } else {
            // If videos haven't been submitted, show the actual count
            todayQR.textContent = stats.todayVideos || stats.todayTranslations || stats.dailyVideos?.count || stats.dailyTranslations?.count || 0;
        }
    }

    // For today's earnings:
    // Use stats.dailyEarnings.amount if it exists and the date matches today
    if (todayEarnings) {
        const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        let todaysEarningsAmount = 0;

        // Check if we have daily earnings data for today
        if (stats.dailyEarnings && stats.dailyEarnings.date === currentDate) {
            todaysEarningsAmount = stats.dailyEarnings.amount || 0;
        }

        todayEarnings.textContent = `₹${todaysEarningsAmount}`;
        console.log('Updated today\'s earnings display:', todaysEarningsAmount);
    }

    // Other stats
    if (totalEarnings) totalEarnings.textContent = `₹${stats.totalEarnings || 0}`;
    if (totalReferrals) totalReferrals.textContent = stats.totalReferrals || 0;

    // Plan card removed
    // updatePlanCard(userData);

    // Add debug information in development mode
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const debugInfoElement = document.getElementById('planDebugInfo');
        if (debugInfoElement) {
            debugInfoElement.innerHTML = `
                <strong>Plan Debug Info:</strong><br>
                Plan Type: ${planInfo.planType || 'Unknown'}<br>
                Active Days: ${planInfo.activeDays || 0}<br>
                Valid Days: ${planInfo.validDays || 0}<br>
                Days Left: ${planInfo.daysLeft || 0}<br>
                Activation Date: ${planInfo.planActivationDate || 'Unknown'}<br>
                Expired: ${planInfo.expired ? 'Yes' : 'No'}
            `;
            debugInfoElement.style.display = 'block';
        }
    }
}

// Update plan card with user's plan information - REMOVED
/*
function updatePlanCard(userData) {
    // Function removed as plan card has been removed from the dashboard
    // This function is kept commented for reference in case it needs to be restored in the future
}
*/


// Load transactions function removed as Recent Activities section was removed
/*
function loadTransactions(userId) {
    // Function code removed as it's no longer needed
}
*/

// Load referral data
async function loadReferralData(userId) {
    console.log('Loading referral data for user ID:', userId);
    try {
        // Check if we recently came from the refer page
        if (cameFromPage('refer', MAX_AGE.MEDIUM)) {
            console.log('Recently came from refer page, using cached data');

            // Try to get cached user data
            const cachedUserData = getCachedUserData(userId);
            if (cachedUserData) {
                // Update referral count in UI from cache
                if (totalReferrals) {
                    const refCount = cachedUserData.stats?.totalReferrals || cachedUserData.referralCount || 0;
                    totalReferrals.textContent = refCount;
                    console.log('Updated referral count in UI from cache:', refCount);
                }

                // Update referral earnings in UI if available in cached data
                if (referralEarnings && cachedUserData.referralEarnings !== undefined) {
                    referralEarnings.textContent = `₹${(cachedUserData.referralEarnings || 0).toFixed(2)}`;
                    console.log('Updated referral earnings from cached data:', cachedUserData.referralEarnings);

                    // Skip Firebase read
                    return;
                }
            }
        }

        // Check if referral data was recently loaded
        if (wasRecentlyLoaded(DATA_TYPES.REFERRALS, MAX_AGE.MEDIUM)) {
            console.log('Referral data was recently loaded, using cached data');

            // Try to get cached user data
            const cachedUserData = getCachedUserData(userId);
            if (cachedUserData) {
                // Update referral count in UI from cache
                if (totalReferrals) {
                    const refCount = cachedUserData.stats?.totalReferrals || cachedUserData.referralCount || 0;
                    totalReferrals.textContent = refCount;
                    console.log('Updated referral count in UI from cache:', refCount);
                }

                // Update referral earnings in UI if available in cached data
                if (referralEarnings && cachedUserData.referralEarnings !== undefined) {
                    referralEarnings.textContent = `₹${(cachedUserData.referralEarnings || 0).toFixed(2)}`;
                    console.log('Updated referral earnings from cached data:', cachedUserData.referralEarnings);

                    // Skip Firebase read
                    return;
                }
            }
        }

        // Use data service to get referral data
        const referralData = await getReferrals(userId);

        // Update referral count in UI
        if (totalReferrals) {
            totalReferrals.textContent = referralData.count;
            console.log('Updated referral count in UI:', referralData.count);
        } else {
            console.warn('totalReferrals element not found in DOM');
        }

        // Update referral earnings in UI
        if (referralEarnings) {
            referralEarnings.textContent = `₹${(referralData.earnings || 0).toFixed(2)}`;
            console.log('Updated referral earnings from data service:', referralData.earnings);

            // Mark referral data as loaded
            updateLastLoaded(DATA_TYPES.REFERRALS);

            return; // Skip the transaction query
        } else {
            console.warn('referralEarnings element not found in DOM');
        }

        // If we get here, we need to calculate referral earnings from transactions
        console.log('Calculating referral earnings from transactions');

        // Use data service to get transactions
        const transactions = await getTransactions(userId);
        console.log('Found', transactions.length, 'transactions');

        // Filter for referral transactions and calculate total
        const referralTransactions = transactions.filter(t => t.type === 'referral');
        let total = 0;

        referralTransactions.forEach(transaction => {
            const amount = transaction.amount || 0;
            total += amount;
        });

        console.log('Calculated referral earnings from transactions:', total);

        if (referralEarnings) {
            referralEarnings.textContent = `₹${total.toFixed(2)}`;
        }

        // Store the calculated earnings in the user document for future reference
        try {
            await updateUserData(userId, {
                referralEarnings: total
            });
            console.log('Stored referral earnings in user document:', total);

            // Mark referral data as loaded
            updateLastLoaded(DATA_TYPES.REFERRALS);
        } catch (error) {
            console.error('Error updating user document with referral earnings:', error);
        }
    } catch (error) {
        console.error('Error loading referral data:', error);
    }
}

/**
 * Refresh referral data in the background
 * @param {string} userId - User ID
 */
async function refreshReferralData(userId) {
    try {
        console.log('Refreshing referral data in background');

        // Use data service to get transactions
        const transactions = await getTransactions(userId, true); // Force refresh
        console.log('Found', transactions.length, 'transactions in refresh');

        // Filter for referral transactions and calculate total
        const referralTransactions = transactions.filter(t => t.type === 'referral');
        let total = 0;

        referralTransactions.forEach(transaction => {
            total += transaction.amount || 0;
        });

        // Only update UI if the total has changed
        const currentEarnings = parseFloat(referralEarnings?.textContent?.replace('₹', '') || '0');
        if (Math.abs(currentEarnings - total) > 0.01) { // Allow for small floating point differences
            console.log('Referral earnings changed, updating UI');
            if (referralEarnings) {
                referralEarnings.textContent = `₹${total.toFixed(2)}`;
            }

            // Update the user document
            await updateUserData(userId, {
                referralEarnings: total
            });
        }
    } catch (error) {
        console.error('Error refreshing referral data:', error);
    }
}

// Load login history
async function loadLoginHistory(userId) {
    if (!loginHistory) return; // Don't proceed if element doesn't exist

    // Check if we recently came from the profile page
    if (cameFromPage('profile', MAX_AGE.MEDIUM)) {
        console.log('Recently came from profile page, skipping login history load');
        return; // Skip loading login history as it was likely loaded in profile page
    }

    // Check if login history was recently loaded
    if (wasRecentlyLoaded(DATA_TYPES.LOGIN_HISTORY, MAX_AGE.MEDIUM)) {
        console.log('Login history was recently loaded, skipping Firebase read');
        return; // Skip loading login history as it was recently loaded
    }

    try {
        // Show loading state
        loginHistory.innerHTML = `
            <div class="loading-activity">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading login history...</p>
            </div>
        `;

        // Import the Firebase functions we need directly
        const { collection, query, where, orderBy, limit, getDocs } = await import("https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js");
        const { db } = await import('./firebase-config.js');

        // Use a more efficient query with limit and orderBy
        const loginHistoryRef = collection(db, 'user_logins');
        const loginQuery = query(
            loginHistoryRef,
            where('userId', '==', userId),
            where('timestamp', '!=', null),
            orderBy('timestamp', 'desc'), // Most recent first
            limit(5) // Only get the 5 most recent logins
        );

        // Use a one-time get instead of a real-time listener to reduce reads
        const snapshot = await getDocs(loginQuery);

        if (snapshot.empty) {
            loginHistory.innerHTML = '<div class="no-activity">No login history</div>';
            return;
        }

        let html = '';
        snapshot.forEach(doc => {
            const data = doc.data();
            const timeAgo = getTimeAgo(data.timestamp?.toDate());
            const deviceInfo = data.deviceInfo || {};

            html += `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <div class="activity-details">
                        <h4>Login</h4>
                        <p>${deviceInfo.userAgent || 'Unknown device'}</p>
                        <small>${timeAgo}</small>
                    </div>
                </div>
            `;
        });
        loginHistory.innerHTML = html;

        // Mark login history as loaded
        updateLastLoaded(DATA_TYPES.LOGIN_HISTORY);

    } catch (error) {
        console.error('Error loading login history:', error);
        loginHistory.innerHTML = '<div class="no-activity">Error loading login history</div>';
    }
}

// Helper function to format time ago
function getTimeAgo(date) {
    if (!date) return 'Just now';

    const seconds = Math.floor((new Date() - date) / 1000);
    let interval = seconds / 31536000;

    if (interval > 1) return Math.floor(interval) + ' years ago';
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + ' months ago';
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + ' days ago';
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + ' hours ago';
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + ' minutes ago';
    return Math.floor(seconds) + ' seconds ago';
}

// Helper function to show alerts
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 3000);
}

// This function is no longer needed as QR code generation has been replaced by translations
// Keeping this comment for reference

// Transfer funds between wallets
async function handleTransfer(fromWallet, toWallet, amount) {
    if (!userData) return;

    if (fromWallet === 'earning' && amount < 50) {
        alert('Minimum transfer amount from earning wallet is ₹50');
        return;
    }

    if (userData.wallet[fromWallet] < amount) {
        alert('Insufficient funds');
        return;
    }

    try {
        // Use data service to update wallet balances
        await updateUserData(auth.currentUser.uid, {
            [`wallet.${fromWallet}`]: increment(-amount),
            [`wallet.${toWallet}`]: increment(amount)
        });

        // Create a transaction record using data service
        try {
            await addTransaction({
                type: 'wallet_transfer',
                amount: amount,
                description: `Transferred ₹${amount} from ${fromWallet} wallet to ${toWallet} wallet`,
                status: 'completed',
                fromWallet: fromWallet,
                toWallet: toWallet
            });
            console.log('Wallet transfer transaction created successfully');
        } catch (transactionError) {
            console.error('Error creating wallet transfer transaction:', transactionError);
            // Continue even if transaction creation fails - the wallet has already been updated
        }

        alert('Transfer successful!');
        // Refresh user data
        await loadUserData();
    } catch (error) {
        console.error('Transfer error:', error);
        alert('Transfer failed: ' + error.message);
    }
}

// Show withdrawal modal
function showWithdrawalModal() {
    const withdrawalModal = document.getElementById('withdrawalModal');
    if (withdrawalModal) {
        withdrawalModal.style.display = 'block';
    }
}

// Close withdrawal modal
function closeWithdrawalModal() {
    const withdrawalModal = document.getElementById('withdrawalModal');
    if (withdrawalModal) {
        withdrawalModal.style.display = 'none';
    }
}

// Handle withdrawal form submission
withdrawalForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const amount = parseFloat(withdrawalForm.withdrawalAmount.value);
    const bankDetails = {
        bankName: withdrawalForm.bankName.value,
        accountNumber: withdrawalForm.accountNumber.value,
        ifscCode: withdrawalForm.ifscCode.value,
        accountHolder: withdrawalForm.accountHolder.value
    };

    if (amount > userData.wallet.balance) {
        alert('Insufficient funds in main wallet');
        return;
    }

    try {
        // Import the Firebase functions we need directly
        const { collection, addDoc, serverTimestamp } = await import("https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js");
        const { db } = await import('./firebase-config.js');

        // Create withdrawal request
        await addDoc(collection(db, 'withdrawals'), {
            userId: auth.currentUser.uid,
            amount: amount,
            bankName: bankDetails.bankName,
            accountNumber: bankDetails.accountNumber,
            ifscCode: bankDetails.ifscCode,
            holder: bankDetails.accountHolder,
            status: 'Pending',
            createdAt: serverTimestamp()
        });

        // Update wallet balance using data service
        await updateUserData(auth.currentUser.uid, {
            'wallet.balance': increment(-amount)
        });

        // Add transaction record using data service
        try {
            await addTransaction({
                type: 'withdrawal',
                amount: -amount, // Negative amount since it's money going out
                description: `Withdrawal request of ₹${amount}`,
                status: 'pending'
            });
            console.log('Withdrawal transaction created successfully');
        } catch (transactionError) {
            console.error('Error creating withdrawal transaction:', transactionError);
            // Continue even if transaction creation fails - the withdrawal has already been created
        }

        closeWithdrawalModal();
        withdrawalForm.reset();

        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'Withdrawal Requested!',
            text: 'Your request has been submitted. Please wait for admin approval.',
            timer: 2000,
            showConfirmButton: false
        });

        // Refresh user data
        await loadUserData();
    } catch (error) {
        console.error('Withdrawal error:', error);
        alert('An error occurred. Please try again.');
    }
});

// Load user data with caching
async function loadUserData() {
    try {
        const user = auth.currentUser;
        if (!user) {
            console.error('No user is signed in');
            return null;
        }

        // Use data service to get user data
        const userData = await getUserData(user.uid);

        if (userData) {
            // Update UI with user data
            updateDashboardUI(userData);
            return userData;
        } else {
            console.error('No user data found');
            return null;
        }
    } catch (error) {
        console.error('Error getting user data:', error);
        return null;
    }
}

// Import the showPlanUpgradeDialog function from the shared module
import { showPlanUpgradeDialog } from './plan-upgrade-dialog.js';

// Make the function available to the dashboard
window.dashboardShowPlanUpgradeDialog = showPlanUpgradeDialog;

// This commented section has been removed as it was duplicating functionality

// Make the showPlanUpgradeDialog function globally accessible
window.showPlanUpgradeDialog = showPlanUpgradeDialog;

// Also store it with a different name to avoid conflicts with the wrapper
window.dashboardShowPlanUpgradeDialog = showPlanUpgradeDialog;

// Dispatch an event to notify that the dashboard functions are ready
console.log('Dispatching dashboardFunctionsReady event');
document.dispatchEvent(new Event('dashboardFunctionsReady'));
