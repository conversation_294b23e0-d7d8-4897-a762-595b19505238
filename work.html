<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watch Videos - MyTube</title>
    <meta name="description" content="MyTube - Watch videos and earn money. Complete your daily video watching tasks to earn rewards.">
    <link rel="icon" href="img/mytube-favicon.svg" type="image/svg+xml">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/pages.css">
    <link rel="stylesheet" href="css/work.css">
    <link rel="stylesheet" href="css/work-header.css">
    <link rel="stylesheet" href="css/admin-password.css">
    <link rel="stylesheet" href="css/youtube-theme.css">
    <link rel="stylesheet" href="css/leave-system.css">
    <link rel="stylesheet" href="css/leave-message-custom.css">

    <!-- SweetAlert2 for notifications -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Video Logout Warning CSS -->
    <link rel="stylesheet" href="css/video-logout-warning.css">

    <!-- Custom styles for loading spinner -->
    <style>
      /* Loading Overlay Styles */
      .loading-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
          /* Removed blur effect that was making text hard to read */
      }

      .loading-spinner {
          text-align: center;
          color: white;
      }

      .loading-spinner i {
          font-size: 3rem;
          margin-bottom: 1rem;
          color: #4CAF50;
      }

      .loading-spinner p {
          font-size: 1.2rem;
          font-weight: 500;
      }

      .loading-spinner .loading-text {
          margin-bottom: 5px;
      }

      .loading-spinner .loading-subtext {
          font-size: 0.9rem;
          font-weight: 400;
          opacity: 0.8;
      }

      .ready-message {
          position: fixed;
          top: 20px;
          right: 20px;
          background-color: rgba(76, 175, 80, 0.9);
          color: white;
          padding: 10px 20px;
          border-radius: 5px;
          z-index: 1000;
          animation: fadeOut 3s forwards;
          animation-delay: 3s;
      }

      @keyframes fadeOut {
          from { opacity: 1; }
          to { opacity: 0; visibility: hidden; }
      }

      /* Sample data notice */
      .sample-data-notice {
          position: fixed;
          bottom: 20px;
          right: 20px;
          background-color: rgba(255, 193, 7, 0.9);
          color: #333;
          padding: 10px 20px;
          border-radius: 5px;
          z-index: 1000;
          font-size: 0.9rem;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
          animation: fadeOut 2s forwards;
          animation-delay: 3s;
      }

      .sample-data-notice i {
          margin-right: 5px;
      }

      /* Custom loading spinner for translation */
      .swal2-popup .loading-spinner {
          display: inline-block;
          width: 80px;
          height: 80px;
          margin: 0 auto;
          color: inherit;
      }

      .swal2-popup .loading-spinner:after {
          content: " ";
          display: block;
          width: 64px;
          height: 64px;
          margin: 8px;
          border-radius: 50%;
          border: 6px solid #FF0000;
          border-color: #FF0000 transparent #FF0000 transparent;
          animation: loading-spinner 1.2s linear infinite;
      }

      @keyframes loading-spinner {
          0% {
              transform: rotate(0deg);
          }
          100% {
              transform: rotate(360deg);
          }
      }

      /* Custom SweetAlert styles */
      .swal2-popup.swal2-modal {
          background: rgba(255, 255, 255, 0.95);
          /* Removed blur effect */
          border-radius: 15px;
          box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
      }

      .swal2-title {
          color: #FF0000;
          font-weight: 600;
      }

      /* Video container styles */
      .video-container {
          position: relative;
          width: 100%;
          border-radius: 10px;
          overflow: hidden;
          margin-bottom: 15px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      #youtubeOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 10;
          cursor: default;
          background-color: transparent;
          transition: background-color 0.3s ease;
          display: flex;
          justify-content: center;
          align-items: center;
      }

      /* Video complete checkmark */
      .video-complete-checkmark {
          font-size: 4rem;
          color: white;
          text-align: center;
          animation: fadeIn 0.5s ease-in-out;
      }

      .video-complete-checkmark i {
          filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.5));
      }

      @keyframes fadeIn {
          from { opacity: 0; transform: scale(0.5); }
          to { opacity: 1; transform: scale(1); }
      }

      /* Highlight button animation */
      .highlight-button {
          animation: pulse-button 1s infinite;
          box-shadow: 0 0 15px rgba(255, 0, 0, 0.7);
      }

      @keyframes pulse-button {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
      }

      /* Timer layout styles */
      .timer-container {
          margin-bottom: 20px;
          width: 100%;
          position: relative;
      }

      .timer-row {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
      }

      .timer-label {
          font-weight: 500;
          margin-right: 10px;
          color: #555;
      }

      .timer-display {
          font-weight: 700;
          font-size: 1.5rem;
          color: #FF0000;
          min-width: 80px;
          text-align: center;
          transition: all 0.3s ease;
      }

      .timer-display.loading {
          color: #ff6600;
      }

      .timer-progress {
          height: 6px;
          background-color: rgba(0, 0, 0, 0.1);
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 15px;
      }

      .progress-bar {
          height: 100%;
          background: linear-gradient(90deg, #FF0000, #FF5722);
          width: 0%;
          transition: width 1s linear;
      }

      /* Video buttons styles */
      .video-buttons {
          display: flex;
          gap: 10px;
          margin-top: 0;
          margin-bottom: 20px;
          justify-content: center;
          padding-top: 0;
          position: relative;
          z-index: 5;
      }

      .video-buttons button {
          min-width: 120px;
          font-weight: 500;
          transition: all 0.3s ease;
      }

      .video-buttons button:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      /* Error styles */
      .error-border {
          border: 2px solid #ff3860 !important;
          box-shadow: 0 0 0 1px #ff3860 !important;
      }

      #languageError {
          color: #ff3860;
          font-size: 0.9rem;
          margin-top: 5px;
          font-weight: 500;
      }

      #checkResult.error {
          color: #ff3860;
          background-color: rgba(255, 56, 96, 0.1);
          border-left: 3px solid #ff3860;
      }

      #checkResult.success {
          color: #23d160;
          background-color: rgba(35, 209, 96, 0.1);
          border-left: 3px solid #23d160;
      }

      /* Info link styles */
      .info-link {
          font-size: 0.8rem;
          color: rgba(255, 255, 255, 0.7);
          margin-left: 10px;
          transition: color 0.3s ease;
          text-decoration: none;
      }

      .info-link:hover {
          color: #FF0000;
      }

      /* Permission badges */
      .permission-badges {
          margin-top: 15px;
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
      }

      .permission-badge {
          display: inline-flex;
          align-items: center;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 0.85rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
      }

      .permission-badge i {
          margin-right: 6px;
      }

      .permission-badge.success-badge {
          background-color: rgba(35, 209, 96, 0.15);
          color: #23d160;
          border: 1px solid rgba(35, 209, 96, 0.3);
      }

      .permission-badge.warning-badge {
          background-color: rgba(255, 184, 0, 0.15);
          color: #ffb800;
          border: 1px solid rgba(255, 184, 0, 0.3);
      }

      .permission-badge.error-badge {
          background-color: rgba(255, 56, 96, 0.15);
          color: #ff3860;
          border: 1px solid rgba(255, 56, 96, 0.3);
      }

      .permission-badge:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      /* Highlight permission badge when new */
      .permission-badge.highlight-permission {
          animation: pulse 1.5s infinite;
      }

      @keyframes pulse {
          0% { opacity: 0.7; }
          50% { opacity: 1; }
          100% { opacity: 0.7; }
      }

      /* Video instruction styles */
      .video-instruction {
          margin-bottom: 10px;
          font-size: 0.9rem;
          color: #555;
          text-align: center;
      }

      .start-instruction {
          color: #ff0000;
          font-weight: 500;
          background-color: rgba(255, 0, 0, 0.05);
          padding: 8px;
          border-radius: 5px;
          border-left: 3px solid #ff0000;
          margin: 15px 0;
          animation: pulse 2s infinite;
      }

      /* Limit reached message styles */
      .limit-reached-message {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          padding: 30px 20px;
          background-color: rgba(255, 255, 255, 0.9);
          border-radius: 10px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          margin: 20px 0;
      }

      .limit-reached-message i {
          font-size: 3rem;
          color: #4CAF50;
          margin-bottom: 15px;
      }

      .limit-reached-message h3 {
          font-size: 1.5rem;
          color: #333;
          margin-bottom: 10px;
      }

      .limit-reached-message p {
          color: #555;
          margin-bottom: 8px;
          font-size: 1rem;
      }

      /* Header logout button styles */
      .header-logout-btn {
          display: flex;
          align-items: center;
          background-color: transparent;
          color: #FF0000;
          border: 1px solid rgba(255, 0, 0, 0.3);
          border-radius: 20px;
          padding: 6px 12px;
          font-size: 0.9rem;
          cursor: pointer;
          transition: all 0.3s ease;
      }

      .header-logout-btn i {
          margin-right: 5px;
      }

      .header-logout-btn:hover {
          background-color: rgba(255, 0, 0, 0.1);
          transform: translateY(-2px);
      }

      /* Timer container styles */
      .timer-container {
          background: linear-gradient(145deg, #ffffff, #f0f0f0);
          border-radius: 12px;
          padding: 15px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
          margin: 20px 0;
          position: relative;
          overflow: hidden;
          border: 1px solid rgba(0, 0, 0, 0.05);
      }

      .timer-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;
      }

      .timer-label {
          font-size: 1rem;
          font-weight: 500;
          color: #333;
      }

      .timer-display {
          font-family: 'Roboto Mono', monospace;
          font-size: 1.5rem;
          font-weight: 700;
          color: #FF0000;
          background-color: #fff;
          padding: 8px 15px;
          border-radius: 8px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          min-width: 80px;
          text-align: center;
          position: relative;
          transition: all 0.3s ease;
      }

      .timer-progress {
          height: 10px;
          background-color: #f0f0f0;
          border-radius: 5px;
          overflow: hidden;
          margin-top: 10px;
          box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
          position: relative;
      }

      .progress-bar {
          height: 100%;
          background: linear-gradient(90deg, #FF0000, #FF5252);
          width: 0%;
          transition: width 1s linear;
          border-radius: 5px;
          position: relative;
          box-shadow: 0 1px 3px rgba(255, 0, 0, 0.3);
      }

      .progress-bar::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 0.2) 0%,
              rgba(255, 255, 255, 0.1) 50%,
              rgba(0, 0, 0, 0.1) 51%,
              rgba(0, 0, 0, 0.05) 100%
          );
          border-radius: 5px;
      }

      /* Timer loading state */
      #videoTimer.loading {
          background: linear-gradient(145deg, #ff5252, #ff0000);
          color: white;
          padding: 8px 15px;
          border-radius: 8px;
          animation: pulse-red 1.5s infinite;
          box-shadow: 0 0 15px rgba(255, 0, 0, 0.3);
      }

      @keyframes pulse-red {
          0% {
              box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
          }
          70% {
              box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
          }
          100% {
              box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
          }
      }

      /* Plan activation date styles */
      .plan-activation-date {
          display: block;
          font-size: 0.8rem;
          color: rgba(255, 255, 255, 0.8);
          margin-top: 5px;
          background-color: rgba(255, 0, 0, 0.2);
          padding: 3px 8px;
          border-radius: 10px;
          border: 1px solid rgba(255, 0, 0, 0.3);
      }

      /* Plan debug info styles */
      .plan-debug-info {
          font-family: 'Roboto Mono', monospace;
          font-size: 0.75rem !important;
          line-height: 1.4;
          color: rgba(255, 255, 255, 0.9);
          background-color: rgba(0, 0, 0, 0.2) !important;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 5px;
          padding: 8px !important;
          margin-top: 10px;
          overflow-x: auto;
          white-space: pre-wrap;
      }

      /* Video loading indicator */
      .video-loading-indicator {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.8);
          color: white;
          font-size: 1.2rem;
          z-index: 20;
          /* Removed blur effects */
      }

      .video-loading-indicator i {
          font-size: 3rem;
          margin-bottom: 15px;
          color: #FF0000;
          animation: spin-pulse 2s infinite;
      }

      .video-loading-indicator span {
          font-weight: 500;
          background-color: rgba(255, 0, 0, 0.2);
          padding: 8px 16px;
          border-radius: 20px;
          border: 1px solid rgba(255, 0, 0, 0.3);
      }

      @keyframes spin-pulse {
          0% {
              transform: rotate(0deg) scale(1);
          }
          50% {
              transform: rotate(180deg) scale(1.2);
          }
          100% {
              transform: rotate(360deg) scale(1);
          }
      }

      /* Video buttons styles */
      .video-buttons {
          display: flex;
          justify-content: center;
          gap: 15px;
          margin-top: 20px;
      }

      .video-btn {
          padding: 12px 24px;
          border-radius: 50px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }

      .video-btn i {
          font-size: 1.2rem;
      }

      .start-btn {
          background: linear-gradient(145deg, #ff0000, #cc0000);
          color: white;
          min-width: 180px;
      }

      .start-btn:hover {
          background: linear-gradient(145deg, #e60000, #b30000);
          transform: translateY(-2px);
          box-shadow: 0 6px 15px rgba(255, 0, 0, 0.2);
      }

      .start-btn:active {
          transform: translateY(1px);
          box-shadow: 0 2px 5px rgba(255, 0, 0, 0.2);
      }

      .next-btn {
          background: linear-gradient(145deg, #4285f4, #356ac3);
          color: white;
          min-width: 150px;
      }

      .next-btn:hover {
          background: linear-gradient(145deg, #3b78e7, #2d5cb3);
          transform: translateY(-2px);
          box-shadow: 0 6px 15px rgba(66, 133, 244, 0.2);
      }

      .next-btn:active {
          transform: translateY(1px);
          box-shadow: 0 2px 5px rgba(66, 133, 244, 0.2);
      }

      .next-btn.highlight-button {
          animation: highlight-pulse 2s infinite;
      }

      @keyframes highlight-pulse {
          0% {
              box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.7);
          }
          70% {
              box-shadow: 0 0 0 15px rgba(66, 133, 244, 0);
          }
          100% {
              box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
          }
      }

      /* Submit button styles */
      .submit-btn {
          background: linear-gradient(145deg, #4caf50, #388e3c);
          color: white;
          min-width: 200px;
          margin: 20px auto;
          display: flex;
      }

      .submit-btn:hover:not(:disabled) {
          background: linear-gradient(145deg, #43a047, #2e7d32);
          transform: translateY(-2px);
          box-shadow: 0 6px 15px rgba(76, 175, 80, 0.2);
      }

      .submit-btn:active:not(:disabled) {
          transform: translateY(1px);
          box-shadow: 0 2px 5px rgba(76, 175, 80, 0.2);
      }

      .submit-btn:disabled {
          background: linear-gradient(145deg, #9e9e9e, #757575);
          cursor: not-allowed;
          opacity: 0.7;
      }

      /* Disabled button styles */
      .video-btn:disabled,
      .video-btn.disabled-btn {
          background: linear-gradient(145deg, #9e9e9e, #757575);
          cursor: not-allowed;
          opacity: 0.7;
          box-shadow: none;
          transform: none;
      }

      .start-btn:disabled,
      .start-btn.disabled-btn {
          background: linear-gradient(145deg, #9e9e9e, #757575);
          color: #e0e0e0;
      }

      .start-btn:disabled:hover,
      .start-btn.disabled-btn:hover {
          transform: none;
          box-shadow: none;
      }
    </style>

    <!-- Firebase SDK -->
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js"></script>


    <!-- Load Firebase config first to ensure Firebase is initialized before other scripts -->
    <script type="module" src="js/firebase-config.js"></script>

    <!-- Wait for Firebase to initialize before loading other scripts -->
    <script>
      // Create a global flag to track Firebase initialization
      window.firebaseInitialized = false;

      // Listen for a custom event that firebase-config.js will dispatch
      document.addEventListener('firebaseInitialized', function() {
        console.log('Firebase initialization detected');
        window.firebaseInitialized = true;
      });

      // Add a fallback to ensure the loading overlay is hidden
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOMContentLoaded event fired');
        // Force hide the loading overlay after 20 seconds
        setTimeout(function() {
          var loadingOverlay = document.getElementById('loadingOverlay');
          if (loadingOverlay && loadingOverlay.style.display !== 'none') {
            console.warn('DOMContentLoaded fallback: Forcing loading overlay to hide');
            loadingOverlay.style.display = 'none';
            loadingOverlay.style.visibility = 'hidden';
            loadingOverlay.style.opacity = '0';

            // Show ready message
            var readyMessage = document.createElement('div');
            readyMessage.className = 'ready-message';
            readyMessage.innerHTML = '<i class="fas fa-check-circle"></i> Page is ready for video watching';
            document.body.appendChild(readyMessage);

            // Remove the message after animation completes
            setTimeout(function() {
              if (readyMessage && readyMessage.parentNode) {
                readyMessage.parentNode.removeChild(readyMessage);
              }
            }, 6000); // 3s display + 3s fade out
          }
        }, 20000);
      });
    </script>

    <!-- Load other scripts after Firebase config -->
    <script type="module" src="js/plan-manager.js"></script>
    <script type="module" src="js/plan-expiration.js"></script>
    <script type="module" src="js/video-limits.js"></script>
    <script type="module" src="js/logout-protection.js"></script>
    <script type="module" src="js/short-video-advantage.js"></script>
    <script type="module" src="js/video-player.js"></script>
    <script type="module" src="js/plan-upgrade-dialog.js"></script>
    <script type="module" src="js/work-video.js"></script>
</head>
<body>
    <!-- Animated Background -->
    <div class="animated-bg"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p class="loading-text">Loading video content...</p>
            <p class="loading-subtext">Please wait while we verify your account...</p>
        </div>
    </div>



    <!-- Header -->
    <header class="page-header work-page-header">
        <div class="header-content">
            <a href="dashboard.html" class="back-button">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="gradient-text">Video Watching</h1>
            <div class="header-actions">
                <button onclick="handleLogout()" class="header-logout-btn" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container">
        <!-- Trial Overlay -->
        <div class="trial-overlay"></div>

        <!-- Wallets Section (Moved to top) -->
        <div class="glass-card wallets-section">
            <div class="earnings-grid">
                <div class="earnings-card">
                    <h3>Earning Wallet</h3>
                    <div class="amount" id="earningWalletAmount">₹0.00</div>
                </div>
                <div class="earnings-card">
                    <h3>Bonus Wallet</h3>
                    <div class="amount" id="bonusWalletAmount">₹0.00</div>
                </div>
            </div>
        </div>

        <!-- Video Watching Plan Section -->
        <div class="glass-card trial-earnings">
            <div class="earnings-header">
                <h2 class="gradient-text">
                    <i class="fas fa-video"></i>
                    <span id="planDuration">Video Watching Plan</span>
                </h2>
                <div class="plan-badge">
                    <span class="active-plan" id="currentPlan">Loading...</span>
                    <span class="plan-info" id="daysLeft">Loading...</span>
                    <span class="plan-activation-date" id="planActivationDate" style="display: none;">Activated: Loading...</span>
                    <button class="upgrade-btn" title="Upgrade Plan">
                        <i class="fas fa-crown"></i>
                    </button>
                    <!-- Removed duplicate debug button -->
                </div>
                <!-- Debug info (hidden in production) -->
                <div id="planDebugInfo" class="plan-debug-info" style="display: none; font-size: 0.8rem; margin-top: 10px; padding: 5px; background: rgba(0,0,0,0.1); border-radius: 5px;"></div>
            </div>
            <div class="stats-container">
                <div class="progress-circle">
                    <div class="counter-wrapper">
                        <span class="day-number" id="counter">0</span>
                        <span class="target-count" id="targetCount">/100</span>
                    </div>
                </div>
                <div class="stats-list">
                    <div class="stat-item">
                        <span class="stat-label">Today's Videos</span>
                        <span class="stat-value" id="todayVideosValue">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Videos</span>
                        <span class="stat-value" id="totalVideosValue">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Videos Left</span>
                        <span class="stat-value" id="videosLeft">100</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Active Days</span>
                        <span class="stat-value" id="activeDaysValue">0/0</span>
                        <button id="debugActiveDaysBtn" style="display: none; margin-left: 5px; font-size: 0.8rem; padding: 2px 5px;" class="debug-btn">Debug</button>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Time Spent</span>
                        <span class="stat-value" id="timeSpentValue">0s</span>
                    </div>
                </div>
            </div>
            <button class="video-btn submit-btn" id="submitBtn" disabled>
                <i class="fas fa-check-circle"></i> Submit Videos
            </button>
        </div>

        <!-- Video Player Section -->
        <div class="glass-card work-form">
            <h2 class="gradient-text">Video Watching Task</h2>

            <!-- Timer Section (Now at the top) -->
            <div class="form-group timer-section" style="background: transparent !important;">
              <div class="timer-container" style="background: transparent !important; box-shadow: none !important; border: none !important; padding: 0 !important;">
                <div class="timer-display-container" style="background: transparent !important;">
                  <span class="timer-label" style="color: white !important; font-weight: bold !important;">Time Remaining:</span>
                  <span id="videoTimer" class="timer-display" style="color: white !important; font-weight: bold !important; background: transparent !important; box-shadow: none !important;">5:00</span>
                </div>
                <div class="timer-progress" style="background: transparent !important; box-shadow: none !important;">
                  <div id="videoProgressBar" class="progress-bar"></div>
                </div>
              </div>
            </div>

            <!-- Video Container (Now in the middle) -->
            <div class="form-group">
              <div class="video-container">
                <div class="video-loading-overlay" id="videoLoadingOverlay">
                  <div class="spinner-container">
                    <div class="spinner"></div>
                    <div class="loading-text">Loading video...</div>
                  </div>
                </div>
                <video id="videoPlayer" controls width="100%" poster="images/video-placeholder.jpg">
                  <source src="" type="video/mp4">
                  Your browser does not support the video tag.
                </video>
              </div>
            </div>

            <!-- Video Control Buttons (Below video) -->
            <div class="video-buttons">
              <button class="video-btn start-btn" id="startVideoBtn">
                <i class="fas fa-play-circle"></i> Start Watching
              </button>
              <button class="video-btn next-btn hidden" id="nextVideoBtn">
                <i class="fas fa-forward"></i> Next Video
              </button>
            </div>

            <!-- Video Info (Now at the bottom) -->
            <div class="form-group video-info">
              <p class="video-instruction">Please watch the entire video to earn credit. The timer will pause if you leave this page.</p>
              <p class="video-instruction start-instruction"><i class="fas fa-info-circle"></i> Click the <strong>"Start Watching"</strong> button to begin playing the video.</p>

              <!-- Short Video Advantage Status -->
              <div class="short-video-advantage">
                <div class="advantage-icon">
                  <i class="fas fa-bolt"></i>
                </div>
                <div class="advantage-text">
                  <p id="shortVideoAdvantageMsg">You have short video advantage! Videos only need to be watched for 30 seconds.</p>
                </div>
              </div>

              <div class="permission-badges">
                <span id="shortVideoEnabledMsg" class="permission-badge success-badge" style="display: none;">
                  <i class="fas fa-check-circle"></i> Short videos enabled (30 seconds)
                </span>
                <span id="shortVideoDisabledMsg" class="permission-badge warning-badge" style="display: none;">
                  <i class="fas fa-exclamation-circle"></i> Regular videos (5 min) - Click for info
                </span>
              </div>
            </div>
          </div>
      </div>
    </main>

    <!-- Toggle button removed as requested -->

    <!-- Plan display is now handled by work-video.js updatePlanInfo() function -->
    <script type="module">
        // This script adds a fallback check for plan display
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Plan display fallback check loaded');

            // Set up a periodic check to ensure the plan is displayed correctly
            setTimeout(() => {
                const currentPlanElement = document.getElementById('currentPlan');
                if (currentPlanElement && (currentPlanElement.textContent === 'Loading...')) {
                    console.log('Plan still showing as Loading, triggering refresh...');
                    // Dispatch a custom event to trigger plan refresh in work-video.js
                    document.dispatchEvent(new CustomEvent('refreshPlanDisplay'));
                }
            }, 5000); // Check after 5 seconds
        });
    </script>


    <!-- Logout Function -->
    <script>
        // Logout function
        window.handleLogout = async () => {
            try {
                console.log('Logout button clicked, checking for unsubmitted videos...');

                // Import required modules
                const { getAuth, signOut } = await import("https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js");
                const { clearAllStorageData } = await import("./js/storage-cleanup.js");

                // Import video logout protection - this will only show warnings on the work.html page
                const { hasUnsubmittedVideos, handleVideoLogout } = await import("./js/video-logout-protection.js");

                // Check if there are unsubmitted videos
                // This is just for logging - the actual check is done in handleVideoLogout
                const hasVideos = hasUnsubmittedVideos();
                console.log('Has unsubmitted videos (pre-check):', hasVideos);

                // Define the actual logout function
                const performLogout = async () => {
                    console.log('Performing logout, clearing all local storage data...');

                    // First clear all locally stored data
                    clearAllStorageData();

                    // Double-check that video-specific data is cleared
                    localStorage.removeItem('instra_video_count');
                    localStorage.removeItem('instra_daily_video_date');
                    localStorage.removeItem('mytube_watched_videos');
                    localStorage.removeItem('mytube_cached_videos');
                    localStorage.removeItem('instra_videos_submitted');

                    console.log('All local storage data cleared');

                    // Then sign out from Firebase Auth
                    const auth = getAuth();
                    await signOut(auth);

                    // Redirect to index page
                    window.location.href = 'index.html';
                    return true;
                };

                // Use the video logout protection handler
                await handleVideoLogout(performLogout);
            } catch (error) {
                console.error('Logout error:', error);
                alert('Failed to logout. Please try again.');
            }
        };
    </script>
  </body>
</html>
