<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Profile - Instra</title>
</head>
<body>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"/>

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>

  <!-- Custom CSS -->
  <link rel="stylesheet" href="css/pages.css"/>
  <link rel="stylesheet" href="css/profile.css"/>
  <link rel="stylesheet" href="css/profile-info.css"/>
  <link rel="stylesheet" href="css/admin-password.css"/>
  <link rel="stylesheet" href="css/youtube-theme.css"/>

  <!-- SweetAlert -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
  <!-- Animated Background -->
  <div class="animated-bg"></div>

  <!-- Header -->
  <header class="page-header">
    <div class="header-content">
      <a href="dashboard.html" class="back-button">
        <i class="fas fa-arrow-left"></i>
      </a>
      <h1 class="gradient-text">Profile</h1>
      <div class="header-actions">
        <button onclick="handleLogout()" class="header-logout-btn" title="Logout">
          <i class="fas fa-sign-out-alt"></i>
          <span>Logout</span>
        </button>
      </div>
    </div>
  </header>

  <!-- Logout Script -->
  <script type="module">
    import { getAuth, signOut } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";

    window.handleLogout = async () => {
      try {
        // Import the video logout protection and storage cleanup utility
        const { handleVideoLogout } = await import('./js/video-logout-protection.js');
        const { clearAllStorageData } = await import('./js/storage-cleanup.js');

        // Define the actual logout function
        const performLogout = async () => {
          // First clear all locally stored data
          clearAllStorageData();

          // Then sign out from Firebase Auth
          const auth = getAuth();
          await signOut(auth);

          // Redirect to index page
          window.location.href = 'index.html';
          return true;
        };

        // Use the video logout protection handler
        await handleVideoLogout(performLogout);
      } catch (error) {
        console.error('Logout error:', error);
        alert('Failed to logout. Please try again.');
      }
    };
  </script>

  <!-- Main Content -->
  <main class="container">
<!-- Profile Info -->
<div class="glass-card profile-info">
    <div class="profile-header">
      <div class="profile-avatar" id="profileAvatar">
        <i class="fas fa-user"></i>
      </div>
      <div class="profile-details">
        <h2 id="user-name">Loading...</h2>
        <p id="user-email">Loading...</p>
        <p id="currentPlan">Loading...</p>
        <p id="joinDate">Loading...</p>
      </div>
    </div>
    <div class="profile-stats">
      <div class="stat-item">
        <i class="fas fa-video"></i>
        <div class="stat-details">
          <h3>Total Videos</h3>
          <div class="stat-value" id="total-videos">0</div>
        </div>
      </div>
      <div class="stat-item">
        <i class="fas fa-wallet"></i>
        <div class="stat-details">
          <h3>Total Earnings</h3>
          <div class="stat-value" id="total-earnings">₹0.00</div>
        </div>
      </div>
      <div class="stat-item">
        <i class="fas fa-money-check-alt"></i>
        <div class="stat-details">
          <h3>Total Withdrawals</h3>
          <div class="stat-value" id="total-withdrawals">₹0.00</div>
        </div>
      </div>
    </div>
  </div>

    <!-- Settings Grid -->
    <div class="settings-grid">
      <!-- Personal Information -->
      <div class="glass-card settings-section">
        <h2 class="gradient-text">Personal Information</h2>
        <form id="personalInfoForm">
          <div class="form-group">
            <label><i class="fas fa-user"></i> Full Name</label>
            <input type="text" name="fullName" id="fullNameInput" required>
          </div>
          <div class="form-group">
            <label><i class="fas fa-phone"></i> Phone Number</label>
            <input type="tel" name="phoneNumber" id="phoneNumberInput" readonly>
          </div>
          <div class="form-group">
            <label><i class="fas fa-envelope"></i> Email</label>
            <input type="email" name="email" id="emailInput" readonly>
          </div>
          <div class="form-group">
            <label><i class="fas fa-city"></i> City</label>
            <input type="text" name="city" id="cityInput">
          </div>
          <button type="submit" class="btn-primary">
            <i class="fas fa-save"></i> Save Changes
          </button>
        </form>
      </div>

      <!-- Change Password -->
      <div class="glass-card settings-section">
        <h2 class="gradient-text">Change Password</h2>
        <form id="passwordForm">
          <div class="form-group">
            <label for="currentPassword"><i class="fas fa-lock"></i> Current Password</label>
            <div class="password-wrapper">
              <input type="password" id="currentPassword" name="currentPassword" required>
              <span class="toggle-password" onclick="togglePassword(this)">
                <i class="fas fa-eye"></i>
              </span>
            </div>
          </div>
          <div class="form-group">
            <label for="newPassword"><i class="fas fa-key"></i> New Password</label>
            <div class="password-wrapper">
              <input type="password" id="newPassword" name="newPassword" required>
              <span class="toggle-password" onclick="togglePassword(this)">
                <i class="fas fa-eye"></i>
              </span>
            </div>
          </div>
          <div class="form-group">
            <label for="confirmPassword"><i class="fas fa-check"></i> Confirm Password</label>
            <div class="password-wrapper">
              <input type="password" id="confirmPassword" name="confirmPassword" required>
              <span class="toggle-password" onclick="togglePassword(this)">
                <i class="fas fa-eye"></i>
              </span>
            </div>
          </div>
          <button type="submit" class="btn-primary">
            <i class="fas fa-key"></i> Update Password
          </button>
        </form>
      </div>

      <!-- Bank Details -->
      <div class="glass-card settings-section">
        <h2 class="gradient-text">Bank Details</h2>

        <button id="toggleBankDetails" class="btn-secondary" style="margin-bottom: 15px;">
          View Bank Details
        </button>

        <div id="bankDetailsDisplay" style="display: none; margin-bottom: 20px;">
          <p><strong>Bank Name:</strong> <span id="displayBankName">-</span></p>
          <p><strong>Account Holder:</strong> <span id="displayAccountHolder">-</span></p>
          <p><strong>Account Number:</strong> <span id="displayAccountNumber">-</span></p>
          <p><strong>IFSC Code:</strong> <span id="displayIfscCode">-</span></p>
        </div>

        <form id="bankDetailsForm" onsubmit="return false;">
            <div class="form-group">
              <label><i class="fas fa-university"></i> Bank Name</label>
              <input type="text" name="bankName" id="edit-bank" required>
            </div>
            <div class="form-group">
              <label><i class="fas fa-user"></i> Account Holder Name</label>
              <input type="text" name="accountHolder" id="edit-holder" required>
            </div>
            <div class="form-group">
              <label><i class="fas fa-hashtag"></i> Account Number</label>
              <input type="text" name="accountNumber" id="edit-account" required>
            </div>
            <div class="form-group">
              <label><i class="fas fa-code"></i> IFSC Code</label>
              <input type="text" name="ifscCode" id="edit-ifsc" required>
            </div>
            <button type="submit" class="btn-primary" id="saveBankDetailsBtn">
              <i class="fas fa-save"></i> Save Bank Details
            </button>
          </form>

          <!-- Hidden debug info -->
          <div id="bankDetailsDebug" style="display: none; margin-top: 15px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-size: 12px;">
            <p><strong>Debug Info:</strong> <span id="bankDetailsDebugInfo">No data yet</span></p>
            <button id="showDebugBtn" style="font-size: 12px; padding: 5px 10px; background-color: #ddd; border: none; border-radius: 4px; cursor: pointer;">Show Debug Info</button>
          </div>
      </div>
    </div>
  </main>

  <!-- Firebase SDK -->
  <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js"></script>
  <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js"></script>
  <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js"></script>
  <script type="module" src="js/firebase-config.js"></script>

  <!-- Custom Script for Profile -->
  <script type="module" src="js/profile.js"></script>

  <!-- Debug functionality is now handled by profile.js -->
</body>
</html>
