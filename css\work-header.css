/* Custom header styles for work page */
.work-page-header {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 10000; /* Increased z-index to ensure header stays on top of loading overlay */
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 10001; /* Higher z-index than parent */
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-align: center;
    color: #16a34a;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Enhanced text shadow for better visibility */
}

/* Override the gradient text with solid color for better clarity */
.header-content .gradient-text {
    background: none;
    -webkit-background-clip: initial;
    -webkit-text-fill-color: initial;
    background-clip: initial;
    color: #16a34a;
    position: relative;
    font-weight: 700;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); /* Enhanced shadow for better visibility */
}

/* Add a gradient underline instead */
.header-content .gradient-text::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 3px;
}

/* Header Actions */
.header-actions {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    z-index: 10002; /* Higher than header-content */
}

.header-logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
    z-index: 10003;
}

.header-logout-btn i {
    margin-right: 0.5rem;
}

.header-logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Responsive styles */
@media (max-width: 768px) {
    .work-page-header {
        padding: 1rem 0.5rem;
        margin-bottom: 1rem;
    }

    .header-content h1 {
        font-size: 1.5rem;
    }

    .back-button {
        width: 35px;
        height: 35px;
    }

    .header-logout-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .header-logout-btn span {
        display: none; /* Hide text on mobile, show only icon */
    }

    .header-logout-btn i {
        margin-right: 0;
    }
}
