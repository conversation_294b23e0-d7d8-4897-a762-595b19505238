// Admin Settings Module
import { auth, db, showLoading, hideLoading, createThemeSelector } from './admin-auth.js';
import {
    collection,
    query,
    getDocs,
    orderBy,
    limit,
    where,
    Timestamp,
    doc,
    getDoc,
    updateDoc,
    addDoc,
    deleteDoc,
    serverTimestamp,
    setDoc
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import {
    updateProfile,
    updateEmail,
    updatePassword,
    EmailAuthProvider,
    reauthenticateWithCredential,
    createUserWithEmailAndPassword
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import {
    formatDate,
    formatDateTime,
    formatCurrency,
    showModal,
    hideModal
} from './admin.js';

// DOM Elements - Tabs
const tabButtons = document.querySelectorAll('.admin-tab-btn');
const tabContents = document.querySelectorAll('.admin-tab-content');

// DOM Elements - Appearance Settings
const themeContainer = document.getElementById('theme-selector-container');
const primaryColorInput = document.getElementById('primary-color');
const secondaryColorInput = document.getElementById('secondary-color');
const accentColorInput = document.getElementById('accent-color');
const saveAppearanceBtn = document.getElementById('save-appearance-settings');

// DOM Elements - General Settings
const siteNameInput = document.getElementById('site-name');
const siteTaglineInput = document.getElementById('site-tagline');
const contactEmailInput = document.getElementById('contact-email');
const supportPhoneInput = document.getElementById('support-phone');
const dailyLimitInput = document.getElementById('daily-limit');
const minWithdrawalInput = document.getElementById('min-withdrawal');
const availableLanguagesInput = document.getElementById('available-languages');
const saveGeneralSettingsBtn = document.getElementById('save-general-settings');

// DOM Elements - Payment Settings
const paymentGatewaySelect = document.getElementById('payment-gateway');
const razorpayKeyInput = document.getElementById('razorpay-key');
const razorpaySecretInput = document.getElementById('razorpay-secret');
const withdrawalFeeInput = document.getElementById('withdrawal-fee');
const minWithdrawalAmountInput = document.getElementById('min-withdrawal-amount');
const withdrawalProcessingDaysInput = document.getElementById('withdrawal-processing-days');
const savePaymentSettingsBtn = document.getElementById('save-payment-settings');

// DOM Elements - Notification Settings
const notifyNewUserCheckbox = document.getElementById('notify-new-user');
const notifyWithdrawalCheckbox = document.getElementById('notify-withdrawal');
const notifyPlanPurchaseCheckbox = document.getElementById('notify-plan-purchase');
const notifyVideoCompleteCheckbox = document.getElementById('notify-translation-complete');

// Short video advantage elements
const shortVideoDurationInput = document.getElementById('short-video-duration');
const regularVideoDurationInput = document.getElementById('regular-video-duration');
const trialAdvantageDaysInput = document.getElementById('trial-advantage-days');
const starterAdvantageDaysInput = document.getElementById('starter-advantage-days');
const premiumAdvantageDaysInput = document.getElementById('premium-advantage-days');
const requiredReferralsInput = document.getElementById('required-referrals');
const welcomeEmailInput = document.getElementById('welcome-email');
const withdrawalEmailInput = document.getElementById('withdrawal-email');
const saveNotificationSettingsBtn = document.getElementById('save-notification-settings');

// DOM Elements - Admin Account
const adminNameInput = document.getElementById('admin-name-input');
const adminEmailInput = document.getElementById('admin-email-input');
const adminCurrentPasswordInput = document.getElementById('admin-current-password');
const adminNewPasswordInput = document.getElementById('admin-new-password');
const adminConfirmPasswordInput = document.getElementById('admin-confirm-password');
const updateAdminAccountBtn = document.getElementById('update-admin-account');

// DOM Elements - Admin Users
const adminsTable = document.getElementById('admins-table');
const addAdminBtn = document.getElementById('add-admin-btn');

// DOM Elements - Plans
const plansTable = document.getElementById('plans-table');
const addPlanBtn = document.getElementById('add-plan-btn');
const saveNewPlanBtn = document.getElementById('save-new-plan');
const newPlanNameInput = document.getElementById('new-plan-name');
const newPlanPriceInput = document.getElementById('new-plan-price');
const newPlanDurationInput = document.getElementById('new-plan-duration');
const newPlanVideoQualityInput = document.getElementById('new-plan-video-quality');
const newPlanEarningsInput = document.getElementById('new-plan-earnings');
const newPlanReferralBonusInput = document.getElementById('new-plan-referral-bonus');
const newPlanReferralVideosInput = document.getElementById('new-plan-referral-videos');
const newPlanReferralWalletInput = document.getElementById('new-plan-referral-wallet');
const newPlanShortVideoDaysInput = document.getElementById('new-plan-short-video-days');
const newPlanDescriptionInput = document.getElementById('new-plan-description');

// DOM Elements - Edit Plan
const editPlanIdInput = document.getElementById('edit-plan-id');
const editPlanNameInput = document.getElementById('edit-plan-name');
const editPlanPriceInput = document.getElementById('edit-plan-price');
const editPlanDurationInput = document.getElementById('edit-plan-duration');
const editPlanVideoQualityInput = document.getElementById('edit-plan-video-quality');
const editPlanEarningsInput = document.getElementById('edit-plan-earnings');
const editPlanReferralBonusInput = document.getElementById('edit-plan-referral-bonus');
const editPlanReferralVideosInput = document.getElementById('edit-plan-referral-videos');
const editPlanReferralWalletInput = document.getElementById('edit-plan-referral-wallet');
const editPlanShortVideoDaysInput = document.getElementById('edit-plan-short-video-days');
const editPlanDescriptionInput = document.getElementById('edit-plan-description');
const editPlanActiveInput = document.getElementById('edit-plan-active');
const updatePlanBtn = document.getElementById('update-plan');

// DOM Elements - Add Admin Modal
const saveNewAdminBtn = document.getElementById('save-new-admin');
const newAdminNameInput = document.getElementById('new-admin-name');
const newAdminEmailInput = document.getElementById('new-admin-email');
const newAdminRoleInput = document.getElementById('new-admin-role');
const newAdminPasswordInput = document.getElementById('new-admin-password');
const newAdminConfirmPasswordInput = document.getElementById('new-admin-confirm-password');

// Initialize settings page
async function initSettingsPage() {
    showLoading('Loading settings...');

    try {
        // Setup tabs
        setupTabs();

        // Add event listeners
        setupEventListeners();

        // Load settings data
        await loadSettings();

        // Load admin users
        await loadAdminUsers();

        // Load plans
        await loadPlans();

        hideLoading();
    } catch (error) {
        console.error('Error initializing settings page:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load settings',
        });
    }
}

// Setup tabs
function setupTabs() {
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(`${tabId}-tab`).classList.add('active');
        });
    });
}

// Setup event listeners
function setupEventListeners() {
    // General settings
    if (saveGeneralSettingsBtn) {
        saveGeneralSettingsBtn.addEventListener('click', saveGeneralSettings);
    }

    // Payment settings
    if (savePaymentSettingsBtn) {
        savePaymentSettingsBtn.addEventListener('click', savePaymentSettings);
    }

    // Notification settings
    if (saveNotificationSettingsBtn) {
        saveNotificationSettingsBtn.addEventListener('click', saveNotificationSettings);
    }

    // Appearance settings
    if (saveAppearanceBtn) {
        saveAppearanceBtn.addEventListener('click', saveAppearanceSettings);
    }

    // Initialize theme selector
    if (themeContainer) {
        createThemeSelector('theme-selector-container');
    }

    // Admin account
    if (updateAdminAccountBtn) {
        updateAdminAccountBtn.addEventListener('click', updateAdminAccount);
    }

    // Add admin
    if (addAdminBtn) {
        addAdminBtn.addEventListener('click', () => showModal('add-admin-modal'));
    }

    if (saveNewAdminBtn) {
        saveNewAdminBtn.addEventListener('click', addNewAdmin);
    }

    // Add plan
    if (addPlanBtn) {
        addPlanBtn.addEventListener('click', () => showModal('add-plan-modal'));
    }

    if (saveNewPlanBtn) {
        saveNewPlanBtn.addEventListener('click', addNewPlan);
    }

    // Edit plan
    if (updatePlanBtn) {
        updatePlanBtn.addEventListener('click', updatePlan);
    }
}

// Load settings data
async function loadSettings() {
    try {
        // Default settings
        const defaultSettings = {
            siteName: 'MyTube',
            siteTagline: 'Earn by watching videos',
            contactEmail: '<EMAIL>',
            supportPhone: '+************',
            dailyLimit: 50,
            minWithdrawal: 100,
            availableLanguages: 'Spanish, French, German, Italian, Portuguese, Russian, Arabic, Hindi, Chinese (Simplified), Japanese, Korean, Turkish, Dutch, Swedish, Polish, Ukrainian, Greek, Hebrew, Vietnamese, Thai',
            paymentGateway: 'razorpay',
            razorpayKey: '',
            razorpaySecret: '',
            withdrawalFee: 0,
            minWithdrawalAmount: 100,
            withdrawalProcessingDays: 3,
            notifyNewUser: true,
            notifyWithdrawal: true,
            notifyPlanPurchase: true,
            notifyTranslationComplete: false,
            welcomeEmail: 'Welcome to MyTube! We\'re excited to have you join our video watching community. Your account has been successfully created and you can now start watching videos to earn money.',
            withdrawalEmail: 'Your withdrawal request for ₹{{amount}} has been received. We will process your request within 3 business days. Thank you for your patience.',
            // Short video advantage settings
            shortVideoDuration: 30,
            regularVideoDuration: 300,
            trialAdvantageDays: 2,
            starterAdvantageDays: 7,
            premiumAdvantageDays: 7,
            requiredReferrals: 3
        };

        // Get settings document
        let settingsDoc = await getDoc(doc(db, 'settings', 'app'));

        // If settings document doesn't exist, create it
        if (!settingsDoc.exists()) {
            console.log('Settings document does not exist. Creating with default values...');
            await setDoc(doc(db, 'settings', 'app'), {
                ...defaultSettings,
                createdAt: serverTimestamp(),
                createdBy: auth.currentUser.uid
            });

            // Get the newly created document
            settingsDoc = await getDoc(doc(db, 'settings', 'app'));
        }

        // Get settings data
        const settings = settingsDoc.exists() ? settingsDoc.data() : defaultSettings;

        // Populate general settings
        if (siteNameInput) siteNameInput.value = settings.siteName || defaultSettings.siteName;
        if (siteTaglineInput) siteTaglineInput.value = settings.siteTagline || defaultSettings.siteTagline;
        if (contactEmailInput) contactEmailInput.value = settings.contactEmail || defaultSettings.contactEmail;
        if (supportPhoneInput) supportPhoneInput.value = settings.supportPhone || defaultSettings.supportPhone;
        if (dailyLimitInput) dailyLimitInput.value = settings.dailyLimit || defaultSettings.dailyLimit;
        if (minWithdrawalInput) minWithdrawalInput.value = settings.minWithdrawal || defaultSettings.minWithdrawal;
        if (availableLanguagesInput) availableLanguagesInput.value = settings.availableLanguages || defaultSettings.availableLanguages;

        // Populate short video advantage settings
        if (shortVideoDurationInput) shortVideoDurationInput.value = settings.shortVideoDuration || defaultSettings.shortVideoDuration;
        if (regularVideoDurationInput) regularVideoDurationInput.value = settings.regularVideoDuration || defaultSettings.regularVideoDuration;
        if (trialAdvantageDaysInput) trialAdvantageDaysInput.value = settings.trialAdvantageDays || defaultSettings.trialAdvantageDays;
        if (starterAdvantageDaysInput) starterAdvantageDaysInput.value = settings.starterAdvantageDays || defaultSettings.starterAdvantageDays;
        if (premiumAdvantageDaysInput) premiumAdvantageDaysInput.value = settings.premiumAdvantageDays || defaultSettings.premiumAdvantageDays;
        if (requiredReferralsInput) requiredReferralsInput.value = settings.requiredReferrals || defaultSettings.requiredReferrals;

        // Populate payment settings
        if (paymentGatewaySelect) paymentGatewaySelect.value = settings.paymentGateway || defaultSettings.paymentGateway;
        if (razorpayKeyInput) razorpayKeyInput.value = settings.razorpayKey || defaultSettings.razorpayKey;
        if (razorpaySecretInput && settings.razorpaySecret) razorpaySecretInput.value = '••••••••••••••••';
        if (withdrawalFeeInput) withdrawalFeeInput.value = settings.withdrawalFee || defaultSettings.withdrawalFee;
        if (minWithdrawalAmountInput) minWithdrawalAmountInput.value = settings.minWithdrawalAmount || defaultSettings.minWithdrawalAmount;
        if (withdrawalProcessingDaysInput) withdrawalProcessingDaysInput.value = settings.withdrawalProcessingDays || defaultSettings.withdrawalProcessingDays;

        // Populate notification settings
        if (notifyNewUserCheckbox) notifyNewUserCheckbox.checked = settings.notifyNewUser !== false;
        if (notifyWithdrawalCheckbox) notifyWithdrawalCheckbox.checked = settings.notifyWithdrawal !== false;
        if (notifyPlanPurchaseCheckbox) notifyPlanPurchaseCheckbox.checked = settings.notifyPlanPurchase !== false;
        if (notifyVideoCompleteCheckbox) notifyVideoCompleteCheckbox.checked = settings.notifyTranslationComplete === true;
        if (welcomeEmailInput) welcomeEmailInput.value = settings.welcomeEmail || defaultSettings.welcomeEmail;
        if (withdrawalEmailInput) withdrawalEmailInput.value = settings.withdrawalEmail || defaultSettings.withdrawalEmail;

        // Load current admin info
        if (auth.currentUser) {
            if (adminNameInput) adminNameInput.value = auth.currentUser.displayName || '';
            if (adminEmailInput) adminEmailInput.value = auth.currentUser.email || '';
        }

    } catch (error) {
        console.error('Error loading settings:', error);
        throw error;
    }
}

// Load admin users
async function loadAdminUsers() {
    if (!adminsTable) return;

    try {
        const adminsQuery = query(collection(db, 'admins'));
        const snapshot = await getDocs(adminsQuery);

        if (snapshot.empty) {
            adminsTable.innerHTML = `
                <tr>
                    <td colspan="6" class="admin-empty-cell">No admin users found</td>
                </tr>
            `;
            return;
        }

        let tableHTML = '';

        for (const doc of snapshot.docs) {
            const admin = doc.data();

            // Format last login
            const lastLogin = admin.lastLogin ? formatDateTime(admin.lastLogin) : 'Never';

            // Create table row
            tableHTML += `
                <tr>
                    <td>${admin.name || 'Admin'}</td>
                    <td>${admin.email || 'N/A'}</td>
                    <td>${admin.role || 'Admin'}</td>
                    <td>${lastLogin}</td>
                    <td><span class="admin-badge ${admin.status === 'inactive' ? 'error' : 'success'}">${admin.status || 'active'}</span></td>
                    <td>
                        <button class="admin-btn-small admin-btn-secondary" onclick="editAdmin('${doc.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${admin.email !== auth.currentUser.email ? `
                            <button class="admin-btn-small admin-btn-danger" onclick="deleteAdmin('${doc.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </td>
                </tr>
            `;
        }

        adminsTable.innerHTML = tableHTML;

        // Add global functions for buttons
        window.editAdmin = editAdmin;
        window.deleteAdmin = deleteAdmin;

    } catch (error) {
        console.error('Error loading admin users:', error);
        adminsTable.innerHTML = `
            <tr>
                <td colspan="6" class="admin-error-cell">Error loading admin users</td>
            </tr>
        `;
    }
}

// Save general settings
async function saveGeneralSettings() {
    if (!siteNameInput) return;

    try {
        showLoading('Saving general settings...');

        // Prepare settings data
        const settingsData = {
            siteName: siteNameInput.value,
            siteTagline: siteTaglineInput ? siteTaglineInput.value : '',
            contactEmail: contactEmailInput ? contactEmailInput.value : '',
            supportPhone: supportPhoneInput ? supportPhoneInput.value : '',
            dailyLimit: dailyLimitInput ? parseInt(dailyLimitInput.value) : 50,
            minWithdrawal: minWithdrawalInput ? parseInt(minWithdrawalInput.value) : 100,
            availableLanguages: availableLanguagesInput ? availableLanguagesInput.value : '',
            // Short video advantage settings
            shortVideoDuration: shortVideoDurationInput ? parseInt(shortVideoDurationInput.value) : 30,
            regularVideoDuration: regularVideoDurationInput ? parseInt(regularVideoDurationInput.value) : 300,
            trialAdvantageDays: trialAdvantageDaysInput ? parseInt(trialAdvantageDaysInput.value) : 2,
            starterAdvantageDays: starterAdvantageDaysInput ? parseInt(starterAdvantageDaysInput.value) : 7,
            premiumAdvantageDays: premiumAdvantageDaysInput ? parseInt(premiumAdvantageDaysInput.value) : 7,
            requiredReferrals: requiredReferralsInput ? parseInt(requiredReferralsInput.value) : 3,
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid
        };

        // Check if settings document exists
        const settingsDocRef = doc(db, 'settings', 'app');
        const settingsDocSnapshot = await getDoc(settingsDocRef);

        // Update or create settings document
        if (settingsDocSnapshot.exists()) {
            await updateDoc(settingsDocRef, settingsData);
        } else {
            await setDoc(settingsDocRef, settingsData);
        }

        hideLoading();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'General settings have been saved',
        });
    } catch (error) {
        console.error('Error saving general settings:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to save general settings',
        });
    }
}

// Save payment settings
async function savePaymentSettings() {
    if (!paymentGatewaySelect) return;

    try {
        showLoading('Saving payment settings...');

        // Get existing settings to preserve razorpaySecret if not changed
        const settingsDoc = await getDoc(doc(db, 'settings', 'app'));
        const existingSettings = settingsDoc.exists() ? settingsDoc.data() : {};

        // Prepare settings data
        const settingsData = {
            paymentGateway: paymentGatewaySelect.value,
            razorpayKey: razorpayKeyInput ? razorpayKeyInput.value : '',
            withdrawalFee: withdrawalFeeInput ? parseFloat(withdrawalFeeInput.value) : 0,
            minWithdrawalAmount: minWithdrawalAmountInput ? parseInt(minWithdrawalAmountInput.value) : 100,
            withdrawalProcessingDays: withdrawalProcessingDaysInput ? parseInt(withdrawalProcessingDaysInput.value) : 3,
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid
        };

        // Only update razorpaySecret if it's changed (not masked)
        if (razorpaySecretInput && razorpaySecretInput.value && razorpaySecretInput.value !== '••••••••••••••••') {
            settingsData.razorpaySecret = razorpaySecretInput.value;
        } else if (existingSettings.razorpaySecret) {
            settingsData.razorpaySecret = existingSettings.razorpaySecret;
        }

        // Check if settings document exists
        const settingsDocRef = doc(db, 'settings', 'app');
        const settingsDocSnapshot = await getDoc(settingsDocRef);

        // Update or create settings document
        if (settingsDocSnapshot.exists()) {
            await updateDoc(settingsDocRef, settingsData);
        } else {
            await setDoc(settingsDocRef, settingsData);
        }

        hideLoading();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Payment settings have been saved',
        });
    } catch (error) {
        console.error('Error saving payment settings:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to save payment settings',
        });
    }
}

// Save notification settings
async function saveNotificationSettings() {
    try {
        showLoading('Saving notification settings...');

        // Prepare settings data
        const settingsData = {
            notifyNewUser: notifyNewUserCheckbox ? notifyNewUserCheckbox.checked : true,
            notifyWithdrawal: notifyWithdrawalCheckbox ? notifyWithdrawalCheckbox.checked : true,
            notifyPlanPurchase: notifyPlanPurchaseCheckbox ? notifyPlanPurchaseCheckbox.checked : true,
            notifyTranslationComplete: notifyVideoCompleteCheckbox ? notifyVideoCompleteCheckbox.checked : false,
            welcomeEmail: welcomeEmailInput ? welcomeEmailInput.value : '',
            withdrawalEmail: withdrawalEmailInput ? withdrawalEmailInput.value : '',
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid
        };

        // Check if settings document exists
        const settingsDocRef = doc(db, 'settings', 'app');
        const settingsDocSnapshot = await getDoc(settingsDocRef);

        // Update or create settings document
        if (settingsDocSnapshot.exists()) {
            await updateDoc(settingsDocRef, settingsData);
        } else {
            await setDoc(settingsDocRef, settingsData);
        }

        hideLoading();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Notification settings have been saved',
        });
    } catch (error) {
        console.error('Error saving notification settings:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to save notification settings',
        });
    }
}

// Save appearance settings
async function saveAppearanceSettings() {
    try {
        showLoading('Saving appearance settings...');

        // Get the current theme name from localStorage
        const currentThemeName = localStorage.getItem('admin-theme') || 'default';

        // Get the custom colors
        const primaryColor = primaryColorInput ? primaryColorInput.value : '#8b5cf6';
        const secondaryColor = secondaryColorInput ? secondaryColorInput.value : '#4f46e5';
        const accentColor = accentColorInput ? accentColorInput.value : '#10b981';

        // Prepare settings data
        const settingsData = {
            appearance: {
                theme: currentThemeName,
                customColors: {
                    primary: primaryColor,
                    secondary: secondaryColor,
                    accent: accentColor
                },
                updatedAt: serverTimestamp(),
                updatedBy: auth.currentUser.uid
            }
        };

        // Check if settings document exists
        const settingsDocRef = doc(db, 'settings', 'app');
        const settingsDocSnapshot = await getDoc(settingsDocRef);

        // Update or create settings document
        if (settingsDocSnapshot.exists()) {
            await updateDoc(settingsDocRef, settingsData);
        } else {
            await setDoc(settingsDocRef, settingsData);
        }

        // Apply the custom colors
        document.documentElement.style.setProperty('--primary-color', primaryColor);
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);
        document.documentElement.style.setProperty('--accent-color', accentColor);

        hideLoading();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Appearance settings have been saved',
        });
    } catch (error) {
        console.error('Error saving appearance settings:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to save appearance settings',
        });
    }
}

// Update admin account
async function updateAdminAccount() {
    if (!adminNameInput) return;

    try {
        // Validate inputs
        if (adminNewPasswordInput.value && adminNewPasswordInput.value !== adminConfirmPasswordInput.value) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'New passwords do not match',
            });
            return;
        }

        showLoading('Updating account...');

        // Update display name
        if (adminNameInput.value !== auth.currentUser.displayName) {
            await updateProfile(auth.currentUser, {
                displayName: adminNameInput.value
            });
        }

        // Update password if provided
        if (adminNewPasswordInput.value && adminCurrentPasswordInput.value) {
            // Re-authenticate user
            const credential = EmailAuthProvider.credential(
                auth.currentUser.email,
                adminCurrentPasswordInput.value
            );

            await reauthenticateWithCredential(auth.currentUser, credential);

            // Update password
            await updatePassword(auth.currentUser, adminNewPasswordInput.value);

            // Clear password fields
            adminCurrentPasswordInput.value = '';
            adminNewPasswordInput.value = '';
            adminConfirmPasswordInput.value = '';
        }

        // Update admin document
        await updateDoc(doc(db, 'admins', auth.currentUser.uid), {
            name: adminNameInput.value,
            updatedAt: serverTimestamp()
        });

        hideLoading();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Your account has been updated',
        });
    } catch (error) {
        console.error('Error updating admin account:', error);
        hideLoading();

        let errorMessage = 'Failed to update account';

        if (error.code === 'auth/wrong-password') {
            errorMessage = 'Current password is incorrect';
        } else if (error.code === 'auth/weak-password') {
            errorMessage = 'New password is too weak';
        }

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: errorMessage,
        });
    }
}

// Add new admin
async function addNewAdmin() {
    if (!newAdminEmailInput || !newAdminPasswordInput) return;

    try {
        // Validate inputs
        if (!newAdminNameInput.value || !newAdminEmailInput.value || !newAdminPasswordInput.value) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please fill in all required fields',
            });
            return;
        }

        if (newAdminPasswordInput.value !== newAdminConfirmPasswordInput.value) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Passwords do not match',
            });
            return;
        }

        showLoading('Adding new admin...');

        // Check if email already exists in admins collection
        const email = newAdminEmailInput.value.trim().toLowerCase();
        const adminsQuery = query(collection(db, 'admins'), where('email', '==', email));
        const existingAdmins = await getDocs(adminsQuery);

        if (!existingAdmins.empty) {
            hideLoading();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An admin with this email already exists',
            });
            return;
        }

        try {
            // Create new user
            const userCredential = await createUserWithEmailAndPassword(
                auth,
                email,
                newAdminPasswordInput.value
            );

            const newUser = userCredential.user;

            // Update user profile
            await updateProfile(newUser, {
                displayName: newAdminNameInput.value
            });

            // Add admin document
            await setDoc(doc(db, 'admins', newUser.uid), {
                name: newAdminNameInput.value,
                email: email,
                role: newAdminRoleInput.value,
                status: 'active',
                createdAt: serverTimestamp(),
                createdBy: auth.currentUser.uid
            });

            // Sign back in as current admin
            // Note: In a real implementation, you would use a different approach
            // such as using Firebase Admin SDK or Cloud Functions

            hideLoading();
            hideModal('add-admin-modal');

            // Clear form
            newAdminNameInput.value = '';
            newAdminEmailInput.value = '';
            newAdminPasswordInput.value = '';
            newAdminConfirmPasswordInput.value = '';

            // Reload admin users
            await loadAdminUsers();

            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'New admin has been added',
            });
        } catch (authError) {
            console.error('Error creating admin user:', authError);

            let errorMessage = 'Failed to add new admin';

            if (authError.code === 'auth/email-already-in-use') {
                errorMessage = 'This email is already registered. Please use a different email address.';
            } else if (authError.code === 'auth/invalid-email') {
                errorMessage = 'Invalid email address';
            } else if (authError.code === 'auth/weak-password') {
                errorMessage = 'Password is too weak';
            }

            throw new Error(errorMessage);
        }
    } catch (error) {
        console.error('Error adding new admin:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: error.message || 'Failed to add new admin',
        });
    }
}

// Edit admin
async function editAdmin(adminId) {
    // This would typically open a modal with admin details for editing
    // For simplicity, we'll just show an alert
    Swal.fire({
        icon: 'info',
        title: 'Edit Admin',
        text: 'This feature is not implemented in this demo',
    });
}

// Delete admin
async function deleteAdmin(adminId) {
    try {
        const result = await Swal.fire({
            title: 'Delete Admin',
            text: 'Are you sure you want to delete this admin? This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#d33'
        });

        if (result.isConfirmed) {
            showLoading('Deleting admin...');

            // Delete admin document
            await deleteDoc(doc(db, 'admins', adminId));

            // Note: In a real implementation, you would also delete the user
            // from Firebase Authentication using Firebase Admin SDK or Cloud Functions

            hideLoading();

            // Reload admin users
            await loadAdminUsers();

            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Admin has been deleted',
            });
        }
    } catch (error) {
        console.error('Error deleting admin:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to delete admin',
        });
    }
}

// Load plans
async function loadPlans() {
    if (!plansTable) return;

    try {
        // Clear loading message
        plansTable.innerHTML = '<tr><td colspan="9" class="admin-loading-cell">Loading plans...</td></tr>';

        // Get plans from Firestore
        const plansQuery = query(collection(db, 'plans'), orderBy('name'));
        const snapshot = await getDocs(plansQuery);

        if (snapshot.empty) {
            plansTable.innerHTML = '<tr><td colspan="9" class="admin-empty-cell">No plans found</td></tr>';
            return;
        }

        // Add table header
        let tableHTML = `
            <tr>
                <th>Name</th>
                <th>Price</th>
                <th>Duration</th>
                <th>Video Quality</th>
                <th>Earnings/50</th>
                <th>Referral Bonus</th>
                <th>Short Video Days</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        `;

        // Process and display plans
        snapshot.forEach(doc => {
            const plan = { id: doc.id, ...doc.data() };

            // Format price and earnings
            const price = formatCurrency(plan.price || 0);
            const earnings = formatCurrency(plan.earningsPerBatch || 0);
            const referralBonus = formatCurrency(plan.referralBonus || 0);
            const referralWallet = formatCurrency(plan.referralWalletBonus || 0);

            // Format video quality
            const videoQuality = plan.videoQuality || 'Standard';

            // Status badge
            const statusClass = plan.active !== false ? 'success' : 'error';
            const statusText = plan.active !== false ? 'Active' : 'Inactive';

            tableHTML += `
                <tr>
                    <td>${plan.name || 'Unnamed Plan'}</td>
                    <td>${price}</td>
                    <td>${plan.duration || 0} days</td>
                    <td>${videoQuality}</td>
                    <td>${earnings}</td>
                    <td>${referralBonus} + ${plan.referralVideosCount || 0} videos</td>
                    <td>${plan.shortVideoDays || 0} days</td>
                    <td><span class="admin-badge ${statusClass}">${statusText}</span></td>
                    <td>
                        <button class="admin-btn-small admin-btn-secondary" onclick="editPlan('${plan.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        plansTable.innerHTML = tableHTML;

        // Add global function for edit button
        window.editPlan = editPlan;

    } catch (error) {
        console.error('Error loading plans:', error);
        plansTable.innerHTML = `<tr><td colspan="7" class="admin-error-cell">Error loading plans: ${error.message}</td></tr>`;
    }
}

// Edit plan
async function editPlan(planId) {
    try {
        showLoading('Loading plan details...');

        // Get plan document
        const planDoc = await getDoc(doc(db, 'plans', planId));

        if (!planDoc.exists()) {
            hideLoading();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Plan not found',
            });
            return;
        }

        const plan = planDoc.data();

        // Populate form fields
        editPlanIdInput.value = planId;
        editPlanNameInput.value = plan.name || '';
        editPlanPriceInput.value = plan.price || 0;
        editPlanDurationInput.value = plan.duration || 0;
        editPlanVideoQualityInput.value = plan.videoQuality || 'Standard';
        editPlanEarningsInput.value = plan.earningsPerBatch || 0;
        editPlanReferralBonusInput.value = plan.referralBonus || 0;
        editPlanReferralVideosInput.value = plan.referralVideosCount || 0;
        editPlanReferralWalletInput.value = plan.referralWalletBonus || 0;
        editPlanShortVideoDaysInput.value = plan.shortVideoDays || 0;
        editPlanDescriptionInput.value = plan.description || '';
        editPlanActiveInput.checked = plan.active !== false;

        hideLoading();
        showModal('edit-plan-modal');

    } catch (error) {
        console.error('Error loading plan details:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load plan details',
        });
    }
}

// Update plan
async function updatePlan() {
    if (!editPlanIdInput || !editPlanIdInput.value) return;

    try {
        // Validate inputs
        if (!editPlanNameInput.value || !editPlanPriceInput.value || !editPlanDurationInput.value) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please fill in all required fields',
            });
            return;
        }

        showLoading('Updating plan...');

        // Calculate plan rate based on earnings per batch
        const earningsPerBatch = parseFloat(editPlanEarningsInput.value) || 0;
        let planRate = 0.2; // Default for Trial

        // Set plan rate based on earnings per batch
        if (earningsPerBatch === 10) {
            planRate = 0.1; // Trial: ₹10 for 100 videos
        } else if (earningsPerBatch === 25) {
            planRate = 0.25; // Starter: ₹25 for 100 videos
        } else if (earningsPerBatch === 150) {
            planRate = 1.5; // Premium: ₹150 for 100 videos
        } else if (earningsPerBatch === 250) {
            planRate = 2.5; // Elite: ₹250 for 100 videos
        } else if (earningsPerBatch === 500) {
            planRate = 5; // Ultimate: ₹500 for 100 videos
        } else {
            // Calculate rate from earnings per batch
            // All plans now have 100 videos
            planRate = earningsPerBatch / 100;
        }

        // Update plan document
        await updateDoc(doc(db, 'plans', editPlanIdInput.value), {
            name: editPlanNameInput.value,
            price: parseFloat(editPlanPriceInput.value),
            duration: parseInt(editPlanDurationInput.value),
            videoQuality: editPlanVideoQualityInput.value,
            earningsPerBatch: earningsPerBatch,
            rate: planRate, // Add plan rate
            referralBonus: parseFloat(editPlanReferralBonusInput.value) || 0,
            referralVideosCount: parseInt(editPlanReferralVideosInput.value) || 0,
            referralWalletBonus: 0, // No referral wallet bonus
            shortVideoDays: parseInt(editPlanShortVideoDaysInput.value) || 0,
            description: editPlanDescriptionInput.value || '',
            active: editPlanActiveInput.checked,
            updatedAt: serverTimestamp(),
            updatedBy: auth.currentUser.uid
        });

        hideLoading();
        hideModal('edit-plan-modal');

        // Reload plans
        await loadPlans();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Plan has been updated',
        });
    } catch (error) {
        console.error('Error updating plan:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to update plan',
        });
    }
}

// Add new plan
async function addNewPlan() {
    if (!newPlanNameInput || !newPlanPriceInput) return;

    try {
        // Validate inputs
        if (!newPlanNameInput.value || !newPlanPriceInput.value || !newPlanDurationInput.value) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please fill in all required fields',
            });
            return;
        }

        showLoading('Adding new plan...');

        // Calculate plan rate based on earnings per batch
        const earningsPerBatch = parseFloat(newPlanEarningsInput.value) || 0;
        let planRate = 0.2; // Default for Trial

        // Set plan rate based on earnings per batch
        if (earningsPerBatch === 10) {
            planRate = 0.1; // Trial: ₹10 for 100 videos
        } else if (earningsPerBatch === 25) {
            planRate = 0.25; // Starter: ₹25 for 100 videos
        } else if (earningsPerBatch === 150) {
            planRate = 1.5; // Premium: ₹150 for 100 videos
        } else if (earningsPerBatch === 250) {
            planRate = 2.5; // Elite: ₹250 for 100 videos
        } else if (earningsPerBatch === 500) {
            planRate = 5; // Ultimate: ₹500 for 100 videos
        } else {
            // Calculate rate from earnings per batch
            // All plans now have 100 videos
            planRate = earningsPerBatch / 100;
        }

        // Add plan document
        await addDoc(collection(db, 'plans'), {
            name: newPlanNameInput.value,
            price: parseFloat(newPlanPriceInput.value),
            duration: parseInt(newPlanDurationInput.value),
            videoQuality: newPlanVideoQualityInput.value,
            earningsPerBatch: earningsPerBatch,
            rate: planRate, // Add plan rate
            referralBonus: parseFloat(newPlanReferralBonusInput.value) || 0,
            referralVideosCount: parseInt(newPlanReferralVideosInput.value) || 0,
            referralWalletBonus: 0, // No referral wallet bonus
            shortVideoDays: parseInt(newPlanShortVideoDaysInput.value) || 0,
            description: newPlanDescriptionInput.value || '',
            active: true,
            createdAt: serverTimestamp(),
            createdBy: auth.currentUser.uid
        });

        hideLoading();
        hideModal('add-plan-modal');

        // Clear form
        newPlanNameInput.value = '';
        newPlanPriceInput.value = '';
        newPlanDurationInput.value = '';
        newPlanVideoQualityInput.value = 'Standard';
        newPlanEarningsInput.value = '';
        newPlanReferralBonusInput.value = '';
        newPlanReferralVideosInput.value = '';
        newPlanReferralWalletInput.value = '';
        newPlanShortVideoDaysInput.value = '';
        newPlanDescriptionInput.value = '';

        // Reload plans
        await loadPlans();

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'New plan has been added',
        });
    } catch (error) {
        console.error('Error adding new plan:', error);
        hideLoading();

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to add new plan',
        });
    }
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', initSettingsPage);
