// Referral Bonus System
import {
    getFirestore,
    doc,
    getDoc,
    updateDoc,
    collection,
    addDoc,
    query,
    where,
    getDocs,
    serverTimestamp
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";

// Initialize Firebase services
const db = getFirestore();
const auth = getAuth();

// Referral bonus amounts by plan type
const REFERRAL_BONUS = {
    // Standard plan names from admin settings
    'Starter': 50,    // ₹499 plan
    'Premium': 300,   // ₹2999 plan
    'Elite': 500,     // ₹4999 plan
    'Ultimate': 1000, // ₹9999 plan

    // Trial plan - No bonus for trial users
    // Note: Users on trial plans cannot receive referral bonuses (both as referrer and referred)
    'Trial': 0
};

/**
 * Normalize plan name to ensure consistency
 * @param {string} planName - The plan name to normalize
 * @returns {string} - The normalized plan name
 */
export function normalizePlanName(planName) {
    if (!planName) return 'Trial';

    // Convert to string and trim
    const name = String(planName).trim();

    // Direct mapping for known variations
    const planMap = {
        // Trial plan variations
        'trial': 'Trial',
        'free': 'Trial',
        'free plan': 'Trial',

        // Starter plan variations
        'starter': 'Starter',
        'basic': 'Starter',
        'junior': 'Starter',  // Map Junior to Starter

        // Premium plan variations
        'premium': 'Premium',
        'pro': 'Premium',
        'executive': 'Premium',
        'expert': 'Premium',
        'senior': 'Premium'   // Map Senior to Premium
    };

    // Check for exact match in lowercase
    const normalizedName = planMap[name.toLowerCase()];
    if (normalizedName) {
        console.log(`Normalized plan name from "${name}" to "${normalizedName}"`);
        return normalizedName;
    }

    // If no match found, check if it's already a standard plan name
    if (REFERRAL_BONUS.hasOwnProperty(name)) {
        console.log(`Plan name "${name}" is already a standard plan name`);
        return name;
    }

    // If still no match found, return the original name
    console.warn(`Could not normalize plan name "${name}". Using as is.`);
    return planName;
}

/**
 * Process referral bonus when a user upgrades their plan
 * Note: Referral bonuses will NOT be credited if the referring user is on a trial plan.
 *
 * @param {string} userId - The ID of the user who upgraded their plan
 * @param {object} planDetails - Details of the plan the user upgraded to
 * @returns {Promise<boolean>} - Whether the referral bonus was processed successfully
 */
export async function processReferralBonus(userId, planDetails) {
    try {
        console.log('=== REFERRAL BONUS PROCESSING START ===');
        console.log('Processing referral bonus for user:', userId);
        console.log('Plan details:', JSON.stringify(planDetails));
        console.log('Available bonus amounts:', JSON.stringify(REFERRAL_BONUS));

        // Get the user's data
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);

        if (!userSnap.exists()) {
            console.error('User not found');
            return false;
        }

        const userData = userSnap.data();
        console.log('User data retrieved:', userData.email, 'Referred by:', userData.referredBy);

        // Check if the user was referred by someone
        if (!userData.referredBy) {
            console.log('User was not referred by anyone');
            return false;
        }

        console.log('Looking for referrer with code:', userData.referredBy);

        // Normalize the referral code to handle potential case or whitespace issues
        const normalizedReferralCode = userData.referredBy.trim();
        console.log('Normalized referral code:', normalizedReferralCode);

        // Standard query to find the referrer
        const referrerQuery = query(
            collection(db, 'users'),
            where('referralCode', '==', normalizedReferralCode)
        );

        const referrerSnap = await getDocs(referrerQuery);
        console.log('Referrer query results:', referrerSnap.size, 'users found');

        // Check if we found a referrer
        if (!referrerSnap || referrerSnap.empty) {
            console.error('Referrer not found for code:', userData.referredBy);
            return false;
        }

        const referrerDoc = referrerSnap.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();
        console.log('Referrer found:', referrerData.email, 'ID:', referrerId);

        // Check if the referrer is on a trial plan
        const referrerPlanName = referrerData.plan?.name || 'Trial';
        const normalizedReferrerPlan = normalizePlanName(referrerPlanName);
        console.log('Referrer plan:', referrerPlanName, 'Normalized:', normalizedReferrerPlan);

        if (normalizedReferrerPlan === 'Trial') {
            console.log('Referrer is on a Trial plan. Referral bonus will not be credited.');
            return false;
        }

        // Check if ANY referral bonus has already been processed for this user
        // We only want to credit the bonus once, regardless of which plan they upgrade to
        try {
            console.log('Checking if referral bonus has already been processed for user:', userId);
            const referralProcessedQuery = query(
                collection(db, 'referrals'),
                where('referredId', '==', userId),
                where('status', '==', 'completed')
            );

            const referralProcessedSnap = await getDocs(referralProcessedQuery);
            console.log('Referral processed check results:', referralProcessedSnap.size, 'records found');

            if (!referralProcessedSnap.empty) {
                console.log('Referral bonus already processed for this user previously - no additional bonus will be credited');
                return false;
            }

            // Also check if the user has a referral record in their own document
            // This is a fallback in case the referrals collection query fails
            if (userData.hasReceivedReferralBonus === true) {
                console.log('User document indicates referral bonus already received');
                return false;
            }

            console.log('No existing referral bonus found, proceeding with processing');
        } catch (checkError) {
            console.error('Error checking for existing referral bonus:', checkError);
            console.error('Error details:', JSON.stringify(checkError));

            // If we can't check the referrals collection, fall back to checking the user document
            if (userData.hasReceivedReferralBonus === true) {
                console.log('User document indicates referral bonus already received (fallback check)');
                return false;
            }

            // If we can't determine for sure, proceed with caution
            console.log('Could not verify if referral bonus was already processed, proceeding anyway');
        }

        // Get the bonus amount based on the plan
        let planName = planDetails.name;
        console.log('Processing bonus for plan:', planName);

        // Always normalize the plan name to ensure consistency
        const normalizedName = normalizePlanName(planName);
        if (normalizedName !== planName) {
            planName = normalizedName;
            console.log('Using normalized plan name:', planName);
        }

        // Check if the plan name exists in the REFERRAL_BONUS object
        if (!(planName in REFERRAL_BONUS)) {
            console.warn(`Plan name "${planName}" not found in REFERRAL_BONUS object. Available plans:`, Object.keys(REFERRAL_BONUS));

            // Try to find a similar plan name
            const planNames = Object.keys(REFERRAL_BONUS);
            const similarPlan = planNames.find(p =>
                p.toLowerCase().includes(planName.toLowerCase()) ||
                planName.toLowerCase().includes(p.toLowerCase())
            );

            if (similarPlan) {
                console.log(`Found similar plan name: "${similarPlan}". Using this instead.`);
                planName = similarPlan;
            } else {
                // Default to Starter for paid plans if we can't determine the plan
                planName = 'Starter';
                console.log(`Could not find a similar plan. Defaulting to "${planName}".`);
            }
        }

        const bonusAmount = REFERRAL_BONUS[planName] || 0;
        console.log('Plan name:', planName, 'Bonus amount:', bonusAmount);

        if (bonusAmount <= 0) {
            console.log('No bonus for this plan type:', planName);
            return false;
        }

        // Update the referrer's wallet
        const currentBonus = referrerData.wallet?.bonus || 0;
        const newBonus = currentBonus + bonusAmount;
        console.log('Updating referrer wallet. Current bonus:', currentBonus, 'New bonus:', newBonus);

        // Get current referral earnings
        const currentReferralEarnings = referrerData.referralEarnings || 0;
        const newReferralEarnings = currentReferralEarnings + bonusAmount;
        console.log('Updating referral earnings. Current:', currentReferralEarnings, 'New:', newReferralEarnings);

        // Get the referrer document reference
        const referrerRef = doc(db, 'users', referrerId);

        // Mark the referred user as having received a referral bonus
        try {
            console.log('Marking user as having received a referral bonus');
            await updateDoc(doc(db, 'users', userId), {
                hasReceivedReferralBonus: true,
                referralBonusProcessedAt: serverTimestamp(),
                referralBonusAmount: bonusAmount,
                referralBonusReferrerId: referrerId
            });
            console.log('User marked as having received a referral bonus');
        } catch (markError) {
            console.error('Error marking user as having received a referral bonus:', markError);
            // Continue even if this update fails
        }

        // Ensure wallet structure exists
        if (!referrerData.wallet) {
            console.log('Creating wallet structure for referrer');
            // Break this into multiple smaller updates

            // Step 1: Create the wallet structure
            console.log('Step 1: Creating wallet structure...');
            try {
                await updateDoc(referrerRef, {
                    'wallet.bonus': bonusAmount,
                    'wallet.balance': 0,
                    'wallet.earning': 0, // No earning wallet credit
                    'referralEarnings': bonusAmount // Add referralEarnings field
                });
                console.log('Wallet structure and referralEarnings created successfully');
            } catch (walletError) {
                console.error('Error creating wallet structure:', walletError);
                // Try an alternative approach
                console.log('Trying alternative approach for wallet creation...');

                await updateDoc(referrerRef, {
                    'wallet': { bonus: bonusAmount, balance: 0, earning: 0 }, // No earning wallet credit
                    'referralEarnings': bonusAmount // Add referralEarnings field
                });
                console.log('Wallet structure and referralEarnings created with alternative approach');
            }

            // Step 2: Update the stats
            console.log('Step 2: Updating referral stats...');
            try {
                await updateDoc(referrerRef, {
                    'stats.referrals': 1,
                    'stats.totalVideosCompleted': 100 // Add 100 videos
                });
                console.log('Referral stats updated successfully');
            } catch (statsError) {
                console.error('Error updating referral stats:', statsError);
                // Try an alternative approach
                console.log('Trying alternative approach for stats update...');
                await updateDoc(referrerRef, {
                    'stats': {
                        referrals: 1,
                        totalVideosCompleted: 100 // Add 100 videos
                    }
                });
                console.log('Referral stats updated with alternative approach');
            }
        } else {
            // Update the referrer's document
            console.log('Updating existing wallet for referrer');

            // Step 1: Update the bonus wallet only (no earning wallet credit)
            console.log('Step 1: Updating bonus wallet...');
            try {
                await updateDoc(referrerRef, {
                    'wallet.bonus': newBonus,
                    'referralEarnings': newReferralEarnings // Update referralEarnings field
                });
                console.log('Bonus wallet and referralEarnings updated successfully');
            } catch (walletError) {
                console.error('Error updating wallets:', walletError);
                // Try an alternative approach
                console.log('Trying alternative approach for wallet update...');
                const currentWallet = referrerData.wallet || {};

                await updateDoc(referrerRef, {
                    'wallet': {
                        ...currentWallet,
                        bonus: newBonus
                    },
                    'referralEarnings': newReferralEarnings // Update referralEarnings field
                });
                console.log('Bonus wallet and referralEarnings updated with alternative approach');
            }

            // Step 2: Update the referral stats and video count
            console.log('Step 2: Updating referral stats and video count...');
            const currentReferrals = referrerData.stats?.referrals || 0;
            const currentVideos = referrerData.stats?.totalVideosCompleted || 0;
            try {
                await updateDoc(referrerRef, {
                    'stats.referrals': currentReferrals + 1,
                    'stats.totalVideosCompleted': currentVideos + 100 // Add 100 videos
                });
                console.log('Referral stats and video count updated successfully');
            } catch (statsError) {
                console.error('Error updating referral stats and video count:', statsError);
                // Try an alternative approach
                console.log('Trying alternative approach for stats update...');
                const currentStats = referrerData.stats || {};
                await updateDoc(referrerRef, {
                    'stats': {
                        ...currentStats,
                        referrals: currentReferrals + 1,
                        totalVideosCompleted: (currentStats.totalVideosCompleted || 0) + 100 // Add 100 videos
                    }
                });
                console.log('Referral stats and video count updated with alternative approach');
            }
        }

        // Create a transaction record for the referrer's bonus wallet
        try {
            const transactionData = {
                userId: referrerId,
                type: 'referral', // Changed from 'referral_bonus' to 'referral' for consistency
                amount: bonusAmount,
                description: `Referral bonus for ${userData.fullName || userData.email} upgrading to ${planName} plan`,
                status: 'completed',
                timestamp: serverTimestamp()
            };
            console.log('Creating bonus wallet transaction record:', transactionData);

            try {
                // Try to create the transaction record
                console.log('Creating transaction record for referral bonus:', transactionData);
                const transactionRef = await addDoc(collection(db, 'transactions'), transactionData);
                console.log('Bonus wallet transaction record created with ID:', transactionRef.id);
            } catch (transactionError) {
                console.error('Error creating bonus wallet transaction record:', transactionError);
                console.error('Transaction error details:', JSON.stringify(transactionError));
                console.log('This error is expected due to security rules - the bonus has still been credited to the wallet');
                // Continue even if transaction creation fails - the wallet has already been updated
            }
        } catch (transactionDataError) {
            console.error('Error preparing bonus wallet transaction data:', transactionDataError);
            // Continue even if transaction preparation fails
        }

        // Create transaction record for the video credit
        try {
            // Create a transaction record for the video credit
            const videoTransactionData = {
                userId: referrerId,
                type: 'video_credit',
                amount: 0, // No direct monetary value
                description: `100 videos credited as referral bonus for ${userData.fullName || userData.email}`,
                status: 'completed',
                timestamp: serverTimestamp(),
                count: 100 // Number of videos credited
            };

            console.log('Creating video credit transaction record:', videoTransactionData);

            try {
                console.log('Creating transaction record for video credit:', videoTransactionData);
                const videoTransactionRef = await addDoc(collection(db, 'transactions'), videoTransactionData);
                console.log('Video credit transaction record created with ID:', videoTransactionRef.id);
            } catch (videoTransactionError) {
                console.error('Error creating video credit transaction record:', videoTransactionError);
                console.error('Transaction error details:', JSON.stringify(videoTransactionError));
                // Continue even if transaction creation fails - the videos have already been credited
            }
        } catch (videoDataError) {
            console.error('Error preparing video transaction data:', videoDataError);
            // Continue even if video transaction preparation fails
        }

        // Create a referral record
        try {
            // Create a well-structured referral data object
            const referralData = {
                referrerId: referrerId,
                referredId: userId,
                referredBy: userData.referredBy,
                referredEmail: userData.email,
                referredName: userData.fullName || '',
                referredPlan: planName,
                bonusAmount: bonusAmount,
                status: 'completed',
                timestamp: serverTimestamp(),
                // Add additional fields that might be required by security rules
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp(),
                type: 'referral_bonus'
            };
            console.log('Creating referral record with data:', JSON.stringify(referralData, null, 2));

            try {
                // Ensure the referrals collection exists by explicitly referencing it
                const referralsCollection = collection(db, 'referrals');
                console.log('Referrals collection reference created');

                // Try to create the referral record
                const referralRef = await addDoc(referralsCollection, referralData);
                console.log('Referral record created successfully with ID:', referralRef.id);

                // Also store the referral in the referrer's user document
                try {
                    // Get current referrals array or create a new one
                    const currentReferrals = referrerData.referrals || [];

                    // Add the new referral to the array with the document ID
                    const updatedReferrals = [...currentReferrals, {...referralData, id: referralRef.id}];

                    // Update the referrer's document
                    await updateDoc(referrerRef, {
                        'referrals': updatedReferrals
                    });
                    console.log('Stored referral in referrer\'s user document');
                } catch (userUpdateError) {
                    console.error('Error storing referral in user document:', userUpdateError);
                    console.error('Error details:', JSON.stringify(userUpdateError));
                    // Continue even if user document update fails
                }
            } catch (referralError) {
                console.error('Error creating referral record:', referralError);
                console.error('Error code:', referralError.code);
                console.error('Error message:', referralError.message);
                console.error('Error details:', JSON.stringify(referralError));

                // Try an alternative approach with a different structure
                try {
                    console.log('Attempting alternative approach to create referral record...');

                    // Simplify the data structure to ensure it meets security rules
                    const simplifiedReferralData = {
                        referrerId: referrerId,
                        referredId: userId,
                        referredBy: userData.referredBy,
                        referredEmail: userData.email,
                        referredName: userData.fullName || '',
                        referredPlan: planName,
                        bonusAmount: bonusAmount,
                        status: 'completed',
                        timestamp: serverTimestamp()
                    };

                    // Try creating the record again with simplified data
                    const alternativeRef = await addDoc(collection(db, 'referrals'), simplifiedReferralData);
                    console.log('Referral record created with alternative approach, ID:', alternativeRef.id);

                    // Even if creating the referral record fails, still try to store it in the user document
                    try {
                        // Get current referrals array or create a new one
                        const currentReferrals = referrerData.referrals || [];

                        // Add the new referral to the array with the document ID
                        const updatedReferrals = [...currentReferrals, {...simplifiedReferralData, id: alternativeRef.id}];

                        // Update the referrer's document
                        await updateDoc(referrerRef, {
                            'referrals': updatedReferrals
                        });
                        console.log('Stored referral in referrer\'s user document with alternative ID');
                    } catch (userUpdateError) {
                        console.error('Error storing referral in user document (alternative):', userUpdateError);
                        // Continue even if user document update fails
                    }
                } catch (alternativeError) {
                    console.error('Alternative approach also failed:', alternativeError);
                    console.error('Alternative error details:', JSON.stringify(alternativeError));

                    // Even if both approaches fail, still try to store it in the user document
                    try {
                        // Get current referrals array or create a new one
                        const currentReferrals = referrerData.referrals || [];

                        // Add the new referral to the array with a local ID
                        const localId = `local-${Date.now()}`;
                        const updatedReferrals = [...currentReferrals, {...referralData, id: localId}];

                        // Update the referrer's document
                        await updateDoc(referrerRef, {
                            'referrals': updatedReferrals
                        });
                        console.log('Stored referral in referrer\'s user document with local ID');
                    } catch (userUpdateError) {
                        console.error('Error storing referral in user document (local):', userUpdateError);
                        // Continue even if user document update fails
                    }
                }
            }
        } catch (referralDataError) {
            console.error('Error preparing referral data:', referralDataError);
            console.error('Error details:', JSON.stringify(referralDataError));
            // Continue even if referral preparation fails
        }

        console.log(`Referral bonus of ₹${bonusAmount} processed successfully for referrer ${referrerData.email}`);
        console.log('=== REFERRAL BONUS PROCESSING END ===');
        return true;

    } catch (error) {
        console.error('Error processing referral bonus:', error);
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);
        console.log('=== REFERRAL BONUS PROCESSING FAILED ===');

        // Try to log the specific step where the error occurred
        if (error.message && error.message.includes('wallet')) {
            console.error('Error occurred while updating wallet');
        } else if (error.message && error.message.includes('transaction')) {
            console.error('Error occurred while creating transaction record');
        } else if (error.message && error.message.includes('referral')) {
            console.error('Error occurred while creating referral record');
        }

        return false;
    }
}

/**
 * Check if a user has a pending referral bonus
 * Note: Users referred by someone on a trial plan are not considered to have a pending bonus,
 * as referral bonuses are not credited if the referring user is on a trial plan.
 *
 * @param {string} userId - The ID of the user to check
 * @returns {Promise<boolean>} - Whether the user has a pending referral bonus
 */
export async function hasPendingReferralBonus(userId) {
    try {
        // Get the user's data
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);

        if (!userSnap.exists()) {
            return false;
        }

        const userData = userSnap.data();

        // Check if the user was referred by someone
        if (!userData.referredBy) {
            return false;
        }

        // Get the referrer's data
        const referrerQuery = query(
            collection(db, 'users'),
            where('referralCode', '==', userData.referredBy)
        );

        const referrerSnap = await getDocs(referrerQuery);

        if (referrerSnap.empty) {
            return false;
        }

        // Check if the referrer is on a trial plan
        const referrerDoc = referrerSnap.docs[0];
        const referrerData = referrerDoc.data();
        const referrerPlanName = referrerData.plan?.name || 'Trial';
        const normalizedReferrerPlan = normalizePlanName(referrerPlanName);

        // If referrer is on a trial plan, there's no pending bonus
        if (normalizedReferrerPlan === 'Trial') {
            return false;
        }

        // We don't need the referrer ID anymore since we're checking for any completed referral
        // Check if ANY referral bonus has already been processed for this user
        const referralProcessedQuery = query(
            collection(db, 'referrals'),
            where('referredId', '==', userId),
            where('status', '==', 'completed')
        );

        const referralProcessedSnap = await getDocs(referralProcessedQuery);

        // If no completed referral is found, there's a pending bonus
        return referralProcessedSnap.empty;

    } catch (error) {
        console.error('Error checking pending referral bonus:', error);
        return false;
    }
}
