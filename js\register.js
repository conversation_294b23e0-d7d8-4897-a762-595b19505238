// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
import {
    getAuth,
    createUserWithEmailAndPassword
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import {
    getFirestore,
    doc,
    setDoc,
    collection,
    getCountFromServer,
    serverTimestamp,
    addDoc,
    Timestamp
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

// Firebase Config
const firebaseConfig = {
    apiKey: "AIzaSyAMMabAMVSrNzKdTEVOcn4Ffh7oDVxD2EM",
    authDomain: "mytube-earnings.firebaseapp.com",
    projectId: "mytube-earnings",
    storageBucket: "mytube-earnings.firebasestorage.app",
    messagingSenderId: "116772605182",
    appId: "1:116772605182:web:a5e9875d48867cca03e9af",
    measurementId: "G-MR5HFN8J4V"
};

// Initialize Firebase
console.log("Initializing Firebase in register.js");
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
console.log("Firebase initialized in register.js");

// DOM Elements
const registerForm = document.getElementById('registerForm');
const registerBtn = document.getElementById('registerBtn');
const passwordInput = document.getElementById('registerPassword');
const passwordStrengthBar = document.getElementById('passwordStrengthBar');
const passwordStrengthText = document.getElementById('passwordStrengthText');

// Password validation function
function validatePassword(password) {
    if (!password) {
        return 'Password is required';
    }

    if (password.length < 8) {
        return 'Password must be at least 8 characters long';
    }

    return null; // No error
}

// Password strength checker
function checkPasswordStrength(password) {
    if (!password) {
        return { strength: 'none', score: 0 };
    }

    let score = 0;

    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 2;

    // Character type checks
    if (/[a-z]/.test(password)) score += 1; // lowercase
    if (/[A-Z]/.test(password)) score += 1; // uppercase
    if (/[0-9]/.test(password)) score += 1; // numbers
    if (/[^a-zA-Z0-9]/.test(password)) score += 1; // special characters

    // Determine strength based on score
    let strength = 'weak';
    if (score >= 4) strength = 'strong';
    else if (score >= 2) strength = 'medium';

    return { strength, score };
}

// Update password strength meter
if (passwordInput && passwordStrengthBar && passwordStrengthText) {
    passwordInput.addEventListener('input', function() {
        const password = this.value;

        // First check for validation errors
        const passwordError = validatePassword(password);

        if (passwordError && password.length > 0) {
            // Show validation error
            passwordStrengthBar.className = 'strength-bar weak';
            passwordStrengthText.textContent = passwordError;
            passwordStrengthText.style.color = '#ff3860';
            return;
        } else {
            // Reset text color if no error
            passwordStrengthText.style.color = '';
        }

        // If no validation errors, show strength
        const { strength, score } = checkPasswordStrength(password);

        // Update the strength bar
        passwordStrengthBar.className = 'strength-bar';
        if (strength !== 'none') {
            passwordStrengthBar.classList.add(strength);
        }

        // Update the strength text
        let strengthText = 'Password strength: ';
        switch (strength) {
            case 'weak':
                strengthText += 'Weak';
                break;
            case 'medium':
                strengthText += 'Medium';
                break;
            case 'strong':
                strengthText += 'Strong';
                break;
            default:
                strengthText += 'Enter a password';
        }

        passwordStrengthText.textContent = strengthText;
    });
}

// Generate Referral Code
async function generateReferralCode() {
    try {
        console.log("Generating referral code...");
        const usersCollection = collection(db, 'users');
        const snapshot = await getCountFromServer(usersCollection);
        const count = snapshot.data().count + 1;
        const code = `MY${String(count).padStart(4, '0')}`;
        console.log(`Generated referral code: ${code}`);
        return code;
    } catch (error) {
        console.error("Error generating referral code:", error);
        return `MY${Math.floor(1000 + Math.random() * 9000)}`; // Fallback
    }
}

// Show error modal
function showErrorModal(message) {
    const errorModal = document.getElementById('errorModal');
    const errorMessage = document.getElementById('errorMessage');

    if (errorMessage) errorMessage.textContent = message;
    if (errorModal) errorModal.classList.add('show');

    console.error("Registration error:", message);
}

// Close error modal
window.closeErrorModal = () => {
    const errorModal = document.getElementById('errorModal');
    if (errorModal) errorModal.classList.remove('show');
};

// Toggle password visibility
window.togglePasswordVisibility = (inputId) => {
    const input = document.getElementById(inputId);
    const icon = document.querySelector(`[data-input="${inputId}"] i`);

    if (input && icon) {
        input.type = input.type === 'password' ? 'text' : 'password';
        icon.classList.toggle('fa-eye-slash');
        icon.classList.toggle('fa-eye');
    }
};

// Handle Registration
if (registerForm) {
    console.log("Registration form found, adding event listener");

    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        console.log("Registration form submitted");

        // Get form values
        const name = document.getElementById('registerName')?.value.trim();
        const email = document.getElementById('registerEmail')?.value.trim().toLowerCase();
        const phone = document.getElementById('registerPhone')?.value.trim();
        const city = document.getElementById('registerCity')?.value.trim();
        const password = document.getElementById('registerPassword')?.value;
        const confirmPassword = document.getElementById('registerConfirmPassword')?.value;
        const referredBy = document.getElementById('registerReferredBy')?.value.trim() || null;

        console.log("Form data collected:", { name, email, phone, city, referredBy });

        // Validate form
        if (!name || !email || !phone || !city || !password || !confirmPassword) {
            showErrorModal('Please fill in all required fields');
            return;
        }

        if (password !== confirmPassword) {
            showErrorModal('Passwords do not match');
            return;
        }

        // Enhanced password validation
        const passwordError = validatePassword(password);
        if (passwordError) {
            showErrorModal(passwordError);
            // Also update the password strength text to show the error
            if (passwordStrengthText) {
                passwordStrengthText.textContent = passwordError;
                passwordStrengthText.style.color = '#ff3860';
            }
            return;
        }

        // Disable button and show loading state
        if (registerBtn) {
            registerBtn.disabled = true;
            registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
        }

        try {
            // Check if phone number is already in use
            console.log("Checking if phone number is already in use:", phone);
            const usersRef = collection(db, "users");
            const phoneQuery = query(usersRef, where("phoneNumber", "==", phone));
            const phoneQuerySnapshot = await getDocs(phoneQuery);

            if (!phoneQuerySnapshot.empty) {
                showErrorModal('This phone number is already registered. Please use a different phone number.');
                if (registerBtn) {
                    registerBtn.disabled = false;
                    registerBtn.innerHTML = 'Register';
                }
                return;
            }

            console.log("Phone number is available, checking email...");

            // Check if email is already in use
            console.log("Checking if email is already in use:", email);
            const emailQuery = query(usersRef, where("email", "==", email.toLowerCase()));
            const emailQuerySnapshot = await getDocs(emailQuery);

            if (!emailQuerySnapshot.empty) {
                showErrorModal('This email address is already registered. Please use a different email address.');
                if (registerBtn) {
                    registerBtn.disabled = false;
                    registerBtn.innerHTML = 'Register';
                }
                return;
            }

            console.log("Email is available, proceeding with registration");
        } catch (error) {
            console.error("Error checking existing accounts:", error);
            // Continue with registration if there's an error checking
            // This is to prevent blocking registration if the query fails
        }


        try {
            // Update loading state to show registration is in progress
            if (registerBtn) {
                registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Registering...';
            }

            console.log("Creating user with email and password");
            // Create user in Firebase Auth
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            console.log("User created in Firebase Auth:", user.uid);

            // Generate referral code
            const referralCode = await generateReferralCode();

            // Prepare user data
            const userData = {
                fullName: name,
                email: email,
                phoneNumber: phone,
                city: city,
                createdAt: serverTimestamp(),
                referralCode,
                referredBy,
                status: 'active',
                wallet: {
                    earning: 0,
                    bonus: 0,
                    balance: 0
                },
                plan: {
                    name: 'Trial',
                    dailyLimit: 50,
                    rate: 0.2, // ₹10 for 50 videos = ₹0.2 per video
                    duration: 7, // 7 days trial period
                    earningsPerBatch: 10 // ₹10 for 50 videos
                },
                planActivatedAt: serverTimestamp(),
                planExpiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days from now
                stats: {
                    totalEarnings: 0,
                    referrals: 0,
                    totalVideosCompleted: 0,
                    dailyVideos: {
                        date: '',
                        count: 0,
                        submitted: false,
                        lastUpdated: serverTimestamp()
                    }
                },
                bankDetails: {
                    bank: "",
                    account: "",
                    ifsc: "",
                    holder: ""
                }
            };

            console.log("Saving user data to Firestore:", userData);

            // Save user data to Firestore
            try {
                const userDocRef = doc(db, "users", user.uid);
                await setDoc(userDocRef, userData);
                console.log("User data saved to Firestore successfully");

                // Create a transaction for Free Plan activation
                const transactionData = {
                    userId: user.uid,
                    type: 'plan',
                    description: 'Trial Plan Activated',
                    amount: 0,
                    status: 'completed',
                    timestamp: serverTimestamp(),
                    planDetails: {
                        name: 'Trial',
                        dailyLimit: 50,
                        rate: 0.2, // ₹10 for 50 videos = ₹0.2 per video
                        duration: 7, // 7 days trial period
                        earningsPerBatch: 10, // ₹10 for 50 videos
                        description: '7-Day Access — Explore and test your video earning potential'
                    }
                };

                // Add the transaction to Firestore
                await addDoc(collection(db, "transactions"), transactionData);
                console.log("Free Plan activation transaction created");
            } catch (firestoreError) {
                console.error("Error saving to Firestore:", firestoreError);
                showErrorModal(`Database error: ${firestoreError.message}. Please try again.`);
                throw firestoreError;
            }

            // Show success message
            if (typeof Swal !== 'undefined') {
                await Swal.fire({
                    icon: 'success',
                    title: 'Registration Complete!',
                    text: `Welcome, ${name}! Your Referral Code is: ${referralCode}`,
                    timer: 3000,
                    showConfirmButton: true
                });
            } else {
                alert(`Registration successful! Your referral code is: ${referralCode}`);
            }

            // Redirect to dashboard
            window.location.href = 'dashboard.html';

        } catch (error) {
            console.error("Registration error:", error);

            // Handle specific Firebase Auth errors
            const errorMap = {
                'auth/email-already-in-use': 'Email already in use',
                'auth/invalid-email': 'Invalid email address',
                'auth/weak-password': 'Password must be at least 8 characters long',
                'auth/operation-not-allowed': 'Registration is currently disabled'
            };

            showErrorModal(errorMap[error.code] || `Registration failed: ${error.message}`);

        } finally {
            // Reset button state
            if (registerBtn) {
                registerBtn.disabled = false;
                registerBtn.innerHTML = 'Register';
            }
        }
    });
} else {
    console.warn("Registration form not found on this page");
}

// Log when script is loaded
console.log("register.js loaded successfully");
