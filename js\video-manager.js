// Video Manager for handling video fetching and tracking
// This module loads videos from mytube.json and manages which videos have been watched

/**
 * VideoManager class to handle video loading and tracking
 */
class VideoManager {
    constructor() {
        this.allVideos = []; // All videos from mytube.json
        this.watchedVideos = []; // Videos already watched by the user
        this.currentBatch = []; // Current batch of videos to watch
        this.batchSize = 100; // Number of videos to fetch at once
        this.initialized = false;
    }

    /**
     * Initialize the video manager by loading videos from mytube.json
     * @returns {Promise<boolean>} - True if initialization was successful
     */
    async initialize() {
        try {
            // Check if we already have videos in localStorage
            const storedVideos = localStorage.getItem('mytube_videos');
            const storedWatchedVideos = localStorage.getItem('mytube_watched_videos');

            if (storedVideos) {
                // Use the stored videos
                this.allVideos = JSON.parse(storedVideos);
                console.log(`Loaded ${this.allVideos.length} videos from localStorage`);

                // Load watched videos if available
                if (storedWatchedVideos) {
                    this.watchedVideos = JSON.parse(storedWatchedVideos);
                    console.log(`Loaded ${this.watchedVideos.length} watched videos from localStorage`);
                }

                // Prepare the first batch
                this.prepareNextBatch();
                this.initialized = true;
                return true;
            }

            // If no stored videos, fetch from mytube.json
            const response = await fetch('mytube.json');
            if (!response.ok) {
                throw new Error(`Failed to fetch videos: ${response.status} ${response.statusText}`);
            }

            // Try to parse as JSON array first
            let videoUrls = [];
            try {
                const jsonData = await response.json();

                if (Array.isArray(jsonData)) {
                    // Handle JSON array format
                    console.log('Processing mytube.json as JSON array...');
                    for (const obj of jsonData) {
                        // Extract the target URL (value) from each object
                        const targetUrl = Object.values(obj)[0];
                        if (targetUrl && typeof targetUrl === 'string') {
                            // Validate and fix the YouTube URL
                            const validUrl = this.validateAndFixYouTubeUrl(targetUrl);
                            if (validUrl) {
                                videoUrls.push(validUrl);
                            } else {
                                console.warn('Invalid YouTube URL skipped:', targetUrl);
                            }
                        }
                    }
                } else {
                    console.warn('mytube.json is not in expected array format');
                }
            } catch (jsonError) {
                console.log('Failed to parse as JSON, trying line-by-line format...');

                // Fallback: Read as text and process line by line
                const text = await response.text();
                const lines = text.split('\n').filter(line => line.trim() !== '');

                for (const line of lines) {
                    try {
                        // Parse each line as a separate JSON object
                        const obj = JSON.parse(line);
                        // Extract the target URL (value) from each object
                        const targetUrl = Object.values(obj)[0];
                        if (targetUrl && typeof targetUrl === 'string') {
                            // Validate and fix the YouTube URL
                            const validUrl = this.validateAndFixYouTubeUrl(targetUrl);
                            if (validUrl) {
                                videoUrls.push(validUrl);
                            } else {
                                console.warn('Invalid YouTube URL skipped:', targetUrl);
                            }
                        }
                    } catch (e) {
                        console.warn('Error parsing line:', line, e);
                    }
                }
            }

            // Filter out known problematic video IDs and add reliable samples if needed
            this.allVideos = this.filterAndEnhanceVideoList(videoUrls);

            // Store videos in localStorage for future use
            localStorage.setItem('mytube_videos', JSON.stringify(this.allVideos));

            // Prepare the first batch
            this.prepareNextBatch();
            this.initialized = true;

            console.log(`Loaded ${this.allVideos.length} videos from mytube.json`);
            return true;
        } catch (error) {
            console.error('Error initializing VideoManager:', error);
            return false;
        }
    }

    /**
     * Process the video data from mytube.json
     * @param {Object} data - The data from mytube.json
     */
    processVideoData(data) {
        // The mytube.json file has a specific format with each line being a JSON object
        // Each line is in the format: {"source_url":"target_url"}
        if (Array.isArray(data)) {
            // If it's already an array, use it directly
            this.allVideos = data;
        } else {
            // Convert the object format to an array of URLs
            const videoUrls = [];
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    videoUrls.push(data[key]);
                }
            }
            this.allVideos = videoUrls;
        }
    }

    /**
     * Prepare the next batch of videos to watch
     * @returns {Array} - The next batch of videos
     */
    prepareNextBatch() {
        // Filter out already watched videos
        const unwatchedVideos = this.allVideos.filter(
            video => !this.watchedVideos.includes(video)
        );

        // If all videos have been watched, reset the watched list
        if (unwatchedVideos.length === 0) {
            console.log('All videos have been watched, resetting watched list');
            this.watchedVideos = [];
            this.currentBatch = this.getRandomVideos(this.allVideos, this.batchSize);
        } else {
            // Get a random batch of unwatched videos
            this.currentBatch = this.getRandomVideos(unwatchedVideos, this.batchSize);
        }

        return this.currentBatch;
    }

    /**
     * Get a random selection of videos from the provided list
     * @param {Array} videoList - The list of videos to select from
     * @param {number} count - The number of videos to select
     * @returns {Array} - The randomly selected videos
     */
    getRandomVideos(videoList, count) {
        // If the list is smaller than the requested count, return the whole list
        if (videoList.length <= count) {
            return [...videoList];
        }

        // Shuffle the array and take the first 'count' elements
        const shuffled = [...videoList].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }

    /**
     * Get the next unwatched video
     * @returns {string} - The URL of the next video to watch
     */
    getNextVideo() {
        // If the current batch is empty, prepare a new batch
        if (this.currentBatch.length === 0) {
            this.prepareNextBatch();
        }

        // Get the next video from the batch
        const nextVideo = this.currentBatch.shift();

        // Mark the video as watched
        if (nextVideo && !this.watchedVideos.includes(nextVideo)) {
            this.watchedVideos.push(nextVideo);

            // Update localStorage
            localStorage.setItem('mytube_watched_videos', JSON.stringify(this.watchedVideos));
        }

        return nextVideo;
    }

    /**
     * Get the current progress (how many videos have been watched)
     * @returns {Object} - Object containing progress information
     */
    getProgress() {
        return {
            totalVideos: this.allVideos.length,
            watchedVideos: this.watchedVideos.length,
            remainingInBatch: this.currentBatch.length
        };
    }

    /**
     * Reset the watched videos list
     */
    resetWatchedVideos() {
        this.watchedVideos = [];
        localStorage.removeItem('mytube_watched_videos');
        this.prepareNextBatch();
    }

    /**
     * Validate if a URL is a valid YouTube URL
     * @param {string} url - URL to validate
     * @returns {boolean} - True if valid YouTube URL
     */
    isValidYouTubeUrl(url) {
        if (!url || typeof url !== 'string') return false;

        // Check for YouTube domain and video ID pattern
        const youtubeRegex = /^https?:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
        return youtubeRegex.test(url);
    }

    /**
     * Validate and fix YouTube URL format
     * @param {string} url - URL to validate and fix
     * @returns {string|null} - Fixed URL or null if invalid
     */
    validateAndFixYouTubeUrl(url) {
        if (!url || typeof url !== 'string') return null;

        // Extract video ID from various YouTube URL formats
        let videoId = null;

        // Standard YouTube URL: https://www.youtube.com/watch?v=VIDEO_ID
        let match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
        if (match) {
            videoId = match[1];
        }

        // If no valid video ID found, try to extract from the end of the URL
        if (!videoId) {
            match = url.match(/([a-zA-Z0-9_-]{11})$/);
            if (match) {
                videoId = match[1];
            }
        }

        // If still no video ID, check if the URL itself might be a video ID
        if (!videoId && url.length === 11 && /^[a-zA-Z0-9_-]+$/.test(url)) {
            videoId = url;
        }

        // Validate video ID format (YouTube video IDs are 11 characters)
        if (videoId && videoId.length === 11 && /^[a-zA-Z0-9_-]+$/.test(videoId)) {
            // Return properly formatted YouTube URL
            return `https://www.youtube.com/watch?v=${videoId}`;
        }

        return null;
    }

    /**
     * Get a specified number of videos
     * @param {number} count - Number of videos to return
     * @returns {Array} - Array of video URLs
     */
    getVideos(count = 50) {
        if (!this.initialized) {
            throw new Error('VideoManager not initialized. Call initialize() first.');
        }

        // If we don't have enough videos, return what we have
        if (this.allVideos.length <= count) {
            return [...this.allVideos];
        }

        // Return a random selection of videos
        return this.getRandomVideos(this.allVideos, count);
    }

    /**
     * Filter out problematic video IDs and enhance the video list
     * @param {Array} videoUrls - Array of video URLs to filter
     * @returns {Array} - Filtered and enhanced video list
     */
    filterAndEnhanceVideoList(videoUrls) {
        // Known problematic video ID patterns (too short, invalid characters, etc.)
        const problematicPatterns = [
            /^.{1,10}$/, // Too short (less than 11 characters)
            /^.{12,}$/, // Too long (more than 11 characters)
            /[^a-zA-Z0-9_-]/, // Invalid characters
            /^video_\d+$/, // Generic placeholder pattern like "video_1234"
        ];

        // Filter out problematic URLs
        const filteredUrls = videoUrls.filter(url => {
            const videoId = this.extractVideoId(url);
            if (!videoId) return false;

            // Check against problematic patterns
            for (const pattern of problematicPatterns) {
                if (pattern.test(videoId)) {
                    console.warn('Filtered out problematic video ID:', videoId);
                    return false;
                }
            }

            return true;
        });

        // Add reliable sample videos if we don't have enough valid videos
        const reliableSamples = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Rick Roll (always available)
            'https://www.youtube.com/watch?v=jNQXAC9IVRw', // Me at the zoo (first YouTube video)
            'https://www.youtube.com/watch?v=kJQP7kiw5Fk', // Despacito
            'https://www.youtube.com/watch?v=9bZkp7q19f0', // Gangnam Style
            'https://www.youtube.com/watch?v=OPf0YbXqDm0', // Uptown Funk
            'https://www.youtube.com/watch?v=fJ9rUzIMcZQ', // Bohemian Rhapsody
            'https://www.youtube.com/watch?v=YQHsXMglC9A', // Hello - Adele
            'https://www.youtube.com/watch?v=hTWKbfoikeg', // Smells Like Teen Spirit
            'https://www.youtube.com/watch?v=L_jWHffIx5E', // Smells Like Teen Spirit (alternative)
            'https://www.youtube.com/watch?v=rYEDA3JcQqw', // Rolling in the Deep
        ];

        // If we have very few valid videos, add reliable samples
        if (filteredUrls.length < 50) {
            console.log(`Only ${filteredUrls.length} valid videos found, adding reliable samples`);
            filteredUrls.push(...reliableSamples);
        }

        console.log(`Filtered video list: ${videoUrls.length} → ${filteredUrls.length} videos`);
        return filteredUrls;
    }

    /**
     * Extract video ID from YouTube URL
     * @param {string} url - YouTube URL
     * @returns {string|null} - Video ID or null if not found
     */
    extractVideoId(url) {
        if (!url || typeof url !== 'string') return null;

        const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
        return match ? match[1] : null;
    }
}

// Create and export a singleton instance
const videoManager = new VideoManager();

export default videoManager;
