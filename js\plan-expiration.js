// Import Firebase modules
import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import {
    getFirestore,
    doc,
    getDoc,
    updateDoc,
    serverTimestamp
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

// Import leave system functions
import { isLeaveDay } from "./leave-system.js";

// Import active days utility function
import { updateActiveDaysIfNeeded } from "./active-days-utils.js";

// Use the existing Firebase app from firebase-config.js
// This avoids duplicate initialization errors
let auth, db;

// Function to safely get Firebase instances
function getFirebaseInstances() {
    // Try to get from window.firebase first
    if (window.firebase) {
        console.log('Using existing Firebase app from window.firebase in plan-expiration.js');
        auth = window.firebase.auth;
        db = window.firebase.db;
        return true;
    }

    // Try to get from global scope if available
    if (typeof getAuth === 'function') {
        try {
            console.log('Attempting to initialize Firebase auth in plan-expiration.js');
            auth = getAuth();
            db = getFirestore();
            return true;
        } catch (error) {
            console.warn('Failed to initialize Firebase in plan-expiration.js:', error);
        }
    }

    return false;
}

// Try to get Firebase instances immediately
getFirebaseInstances();

// Wait for the firebase-config.js to initialize Firebase
document.addEventListener('firebaseInitialized', (event) => {
    console.log('Firebase initialized event received in plan-expiration.js');
    auth = event.detail.auth;
    db = event.detail.db;
});

// Also check on DOMContentLoaded in case the event was already fired
document.addEventListener('DOMContentLoaded', () => {
    if (!auth || !db) {
        console.log('Trying to get Firebase instances on DOMContentLoaded');
        getFirebaseInstances();
    }
});

/**
 * Check if the user's plan has expired
 * @returns {Promise<Object>} - Object containing expiration status and details
 */
export async function checkPlanExpiration() {
    try {
        console.log('Checking plan expiration...');

        // Make sure auth is initialized
        if (!auth) {
            console.log('Auth not initialized yet, trying to get from window.firebase');
            if (window.firebase && window.firebase.auth) {
                auth = window.firebase.auth;
                db = window.firebase.db;
                console.log('Successfully got auth from window.firebase');
            } else {
                console.warn('Auth still not available, returning default plan');
                return {
                    expired: false,
                    message: 'Auth not initialized: Using default plan',
                    planType: 'Trial',
                    daysLeft: 2,
                    translationsLeft: 50
                };
            }
        }

        // Get current user safely
        let user = null;
        try {
            // Check if auth is defined and has currentUser property
            if (auth && typeof auth.currentUser !== 'undefined') {
                user = auth.currentUser;
            } else if (window.firebase && window.firebase.auth) {
                // Try to get from window.firebase as a fallback
                user = window.firebase.auth.currentUser;
            } else {
                console.warn('Auth object is not properly initialized');
            }
        } catch (innerError) {
            console.error('Error accessing auth.currentUser:', innerError);
        }
        if (!user) {
            console.warn('User not authenticated, but allowing access for testing');
            return {
                expired: false,
                message: 'Test mode: No authentication required',
                planType: 'Trial',
                daysLeft: 2,
                translationsLeft: 50
            };
        }

        // Get user data from Firestore
        if (!db) {
            console.warn('Firestore db is not initialized, trying to get from window.firebase');
            if (window.firebase && window.firebase.db) {
                db = window.firebase.db;
            } else {
                console.error('Firestore db is not available');
                return {
                    expired: false,
                    message: 'Database not initialized: Using default plan',
                    planType: 'Trial',
                    daysLeft: 2,
                    translationsLeft: 50
                };
            }
        }

        try {
            const userRef = doc(db, 'users', user.uid);
            const userSnap = await getDoc(userRef);

            if (!userSnap.exists()) {
                console.warn('User document not found, creating default plan for testing');

                // For testing purposes, we'll just return a non-expired trial plan
                return {
                    expired: false,
                    message: 'Test mode: Default trial plan',
                    planType: 'Trial',
                    daysLeft: 2,
                    translationsLeft: 50
                };
            }

            const userData = userSnap.data();

            // Enhanced plan extraction to handle different plan structures
            let plan;
            let planName = 'Trial';

            // Check if plan exists and extract the name properly
            if (userData.plan) {
                // Handle both object and string formats
                if (typeof userData.plan === 'object') {
                    plan = userData.plan;
                    planName = userData.plan.name || 'Trial';
                } else if (typeof userData.plan === 'string') {
                    planName = userData.plan;
                    plan = { name: planName, dailyLimit: 50 };
                } else {
                    plan = { name: 'Trial', dailyLimit: 50 };
                }
            } else {
                plan = { name: 'Trial', dailyLimit: 50 };
            }

            console.log('Plan information extracted:', {
                planObject: plan,
                planName: planName,
                originalPlan: userData.plan
            });

            // Use planActivatedAt if available, otherwise fall back to planUpdatedAt
            const planUpdatedAt = userData.planActivatedAt ?
                userData.planActivatedAt.toDate() :
                (userData.planUpdatedAt ? userData.planUpdatedAt.toDate() : new Date());

            const dailyLimit = plan.dailyLimit || 50;
            const stats = userData.stats || {};

            // Use translationsCount if available, otherwise fall back to stats.totalTranslationsCompleted
            const translationsCompleted = userData.translationsCount || stats.totalTranslationsCompleted || 0;

            // Get active days from user data with proper validation
            let activeDays = 1; // Default to 1 if not set
            if (userData.activeDays !== undefined && userData.activeDays !== null) {
                // Ensure it's a number and at least 1
                activeDays = Math.max(1, parseInt(userData.activeDays, 10) || 1);
                console.log('Retrieved active days from user data:', activeDays);
            } else {
                console.log('No active days found in user data, using default:', activeDays);
            }

            // Get the last active day update timestamp with validation
            let lastActiveDayUpdate;
            if (userData.lastActiveDayUpdate) {
                try {
                    lastActiveDayUpdate = userData.lastActiveDayUpdate.toDate();
                    console.log('Last active day update:', lastActiveDayUpdate);
                } catch (error) {
                    console.warn('Error parsing lastActiveDayUpdate, using planUpdatedAt:', error);
                    lastActiveDayUpdate = planUpdatedAt;
                }
            } else {
                console.log('No lastActiveDayUpdate found, using planUpdatedAt:', planUpdatedAt);
                lastActiveDayUpdate = planUpdatedAt;
            }

            // Use the shared utility function to update active days
            // This will calculate active days from plan activation date regardless of login status
            console.log('Using shared updateActiveDaysIfNeeded utility function to calculate active days from plan activation date');
            const updatedUserData = await updateActiveDaysIfNeeded(user.uid, userData);

            // Update activeDays with the value from the utility function
            activeDays = updatedUserData.activeDays || activeDays;
            console.log('Active days calculated from plan activation date:', activeDays);

            // If planUpdatedAt is not set, update it now
            if (!userData.planUpdatedAt) {
                try {
                    await updateDoc(userRef, {
                        planUpdatedAt: serverTimestamp()
                    });
                    console.log('Updated plan start date');
                } catch (error) {
                    console.warn('Could not update plan start date:', error);
                    // Continue anyway
                }

                return {
                    expired: false,
                    message: 'Plan start date updated',
                    planType: planName,
                    daysLeft: planName === 'Trial' ? 2 : 30, // 30 earning days for paid plans
                    translationsLeft: dailyLimit
                };
            }

            console.log(`Plan: ${planName}, Active days: ${activeDays}, Daily limit: ${dailyLimit}`);

            // Get plan validity days based on plan type
            // Users can earn for 30 active days, plan expires from 31st active day onwards
            const validDays = planName === 'Trial' ? 2 : 30;

            // Calculate days left with validation to prevent negative values
            const daysLeft = Math.max(0, validDays - activeDays + 1);

            // Get plan activation date for display purposes
            let planActivationDate = '';
            if (userData.planActivatedAt) {
                try {
                    const activatedDate = userData.planActivatedAt.toDate();
                    planActivationDate = activatedDate.toISOString().split('T')[0]; // YYYY-MM-DD format
                } catch (error) {
                    console.warn('Error parsing plan activation date:', error);
                }
            }

            console.log(`Plan check: ${planName} plan, active days: ${activeDays}, valid days: ${validDays}, days left: ${daysLeft}`);

            // Check if plan has expired based on active days vs valid days
            // Plan expires from 31st active day onwards (activeDays > 30)
            if (activeDays > validDays) {
                console.log(`${planName} plan has expired after ${activeDays} active days (earning limit: ${validDays} days)`);
                return {
                    expired: true,
                    message: `Your ${validDays}-day ${planName} plan has expired. Please ${planName === 'Trial' ? 'upgrade' : 'renew'} to continue.`,
                    planType: planName,
                    daysLeft: 0,
                    translationsLeft: 0,
                    activeDays: activeDays,
                    validDays: validDays,
                    planActivationDate: planActivationDate
                };
            } else {
                // Plan is still active, check translation limits for non-trial plans
                if (planName !== 'Trial') {
                    // For non-trial plans, also check if they've completed their translation quota
                    const totalTranslationLimit = dailyLimit * validDays;
                    if (translationsCompleted >= totalTranslationLimit) {
                        console.log('Translation limit reached');
                        return {
                            expired: true,
                            message: `You've completed your ${totalTranslationLimit} translations limit. Please contact customer support to continue.`,
                            planType: planName,
                            daysLeft: daysLeft,
                            translationsLeft: 0,
                            activeDays: activeDays,
                            validDays: validDays,
                            planActivationDate: planActivationDate
                        };
                    } else {
                        console.log(`${planName} plan is active, ${daysLeft} days left`);
                        return {
                            expired: false,
                            message: `Your ${planName} plan will expire in ${daysLeft} days.`,
                            planType: planName,
                            daysLeft: daysLeft,
                            translationsLeft: totalTranslationLimit - translationsCompleted,
                            activeDays: activeDays,
                            validDays: validDays,
                            planActivationDate: planActivationDate
                        };
                    }
                } else {
                    // Trial plan is active
                    console.log(`Trial plan is active, ${daysLeft} days left`);
                    return {
                        expired: false,
                        message: `Your Trial plan will expire in ${daysLeft} days.`,
                        planType: 'Trial',
                        daysLeft: daysLeft,
                        translationsLeft: dailyLimit,
                        activeDays: activeDays,
                        validDays: validDays,
                        planActivationDate: planActivationDate
                    };
                }
            }
        } catch (dbError) {
            console.error('Error accessing Firestore data:', dbError);
            return {
                expired: false,
                message: 'Database error: Using default plan',
                planType: 'Trial',
                daysLeft: 2,
                translationsLeft: 50
            };
        }

    } catch (error) {
        console.error('Error checking plan expiration:', error);

        // For testing purposes, we'll return a non-expired plan even on error
        console.warn('Returning default plan due to error');
        return {
            expired: false,
            message: 'Test mode: Error occurred, using default plan',
            planType: 'Trial',
            daysLeft: 2,
            translationsLeft: 50
        };
    }
}

// Make the function available globally
window.checkPlanExpiration = checkPlanExpiration;

// Export the function
export default checkPlanExpiration;
