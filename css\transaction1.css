/* Global Variables */
:root {
    /* Main Gradients */
    --primary-gradient: linear-gradient(135deg, #8b5cf6 0%, #d946ef 100%);
    --secondary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --accent-gradient: linear-gradient(135deg, #d946ef 0%, #ec4899 100%);

    /* Solid Colors */
    --primary-purple: #8b5cf6;
    --secondary-purple: #6366f1;
    --accent-pink: #d946ef;

    /* Background Colors */
    --bg-dark: #1a1a2e;
    --bg-card: #242444;
    --bg-light: #2a2a5a;

    /* Text Colors */
    --text-light: #ffffff;
    --text-gray: #e2e8f0;
    --text-dark: #f8fafc;

    /* Glass Effects */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: 1px solid rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Common Page Styles */
body {
    background: var(--bg-dark);
    color: var(--text-light);
    font-family: 'Poppins', sans-serif;
    margin: 0;
    min-height: 100vh;
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--bg-dark);
    overflow: hidden;
}

.animated-bg::before,
.animated-bg::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: var(--primary-gradient);
    opacity: 0.3;
    filter: blur(100px);
}

.animated-bg::before {
    top: -150px;
    left: -150px;
    animation: float 8s infinite;
}

.animated-bg::after {
    bottom: -150px;
    right: -150px;
    animation: float 8s infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0); }
    50% { transform: translate(100px, 100px); }
}

/* Header Styles */
.page-header {
    padding: 1rem;
    margin-bottom: 2rem;
    position: relative;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Changed from center to flex-start */
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    height: 50px; /* Added fixed height to maintain spacing */
}

.back-button {
    position: fixed;
    left: 2rem;
    top: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-light);
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    z-index: 100;
}

.back-button i {
    font-size: 1.2rem;
    color: var(--text-light);
}

.back-button:hover {
    transform: translateX(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.8rem;
    text-align: center;
    margin: 0 auto;
}

/* Card Styles */
.glass-card {
    background: var(--bg-card);
    border: var(--glass-border);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s, box-shadow 0.3s;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--glass-shadow);
}

/* Button Styles */
.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-light);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s, box-shadow 0.3s;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-light);
    border: var(--glass-border);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s;
}

.btn-secondary:hover {
    transform: translateY(-2px);
}

/* Text Styles */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 600;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Status Colors */
.status-success { color: #000000; } /* Changed to black */
.status-pending { color: #000000; } /* Changed to black */
.status-error { color: #000000; } /* Changed to black */

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .grid {
        grid-template-columns: 1fr;
    }

    .glass-card {
        padding: 1rem;
    }
}

/* Common Page Layout */
.page-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    min-height: 100vh;
    background: var(--bg-dark);
    color: var(--text-primary);
    animation: fadeIn 0.5s ease-out;
}

.page-content {
    margin-left: 280px;
    padding: 30px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-radius: 20px;
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 1.8rem;
    margin: 0;
}

/* Work Page */
.work-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.work-form {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.work-form h2 {
    font-size: 1.3rem;
    margin: 0 0 20px 0;
}



/* Wallet Page */
.wallet-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.wallet-card {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    text-align: center;
}

.wallet-card h3 {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0 0 15px 0;
}

.balance {
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 20px 0;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Transactions Page */
.transactions-list {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 15px;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
}

.transaction-info {
    flex: 1;
}

.transaction-amount {
    font-weight: 600;
    color: #000000; /* Changed to black */
}

/* Refer Page */
.refer-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.refer-info {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.referral-link {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.refer-stats {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

/* Profile Page */
.profile-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.profile-card {
    padding: 25px;
    border-radius: 20px;
    background: var(--glass-bg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.profile-pic {
    text-align: center;
    margin-bottom: 30px;
}

.profile-pic img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.1);
    padding: 5px;
}

.danger-zone {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .wallet-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1024px) {
    .work-container,
    .refer-container,
    .profile-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .page-container {
        grid-template-columns: 1fr;
    }

    .page-content {
        margin-left: 0;
        padding: 20px;
    }

    .wallet-grid {
        grid-template-columns: 1fr;
    }
}