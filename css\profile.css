/* Global Styles */
:root {
    /* Main Gradients - <PERSON> and White Theme */
    --primary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --secondary-gradient: linear-gradient(135deg, #15803d 0%, #22c55e 100%);
    --accent-gradient: linear-gradient(135deg, #16a34a 0%, #059669 100%);

    /* Solid Colors - Green Theme */
    --primary-green: #22c55e;
    --secondary-green: #16a34a;
    --accent-green: #15803d;

    /* Background Colors - White and Light Green */
    --bg-dark: #ffffff;
    --bg-card: #f8fffe;
    --bg-light: #f0fdf4;

    /* Text Colors */
    --text-light: #1f2937;
    --text-gray: #4b5563;
    --text-dark: #111827;

    /* Glass Effects */
    --glass-bg: rgba(34, 197, 94, 0.1);
    --glass-border: 1px solid rgba(34, 197, 94, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(34, 197, 94, 0.15);
    --glass-backdrop: blur(10px);

    /* Shadows - Green Tinted */
    --shadow-sm: 0 2px 4px rgba(34, 197, 94, 0.1);
    --shadow-md: 0 4px 6px rgba(34, 197, 94, 0.1);
    --shadow-lg: 0 10px 15px rgba(34, 197, 94, 0.1);
    --shadow-xl: 0 20px 25px rgba(34, 197, 94, 0.15);

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #22c55e;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    font-family: 'Poppins', sans-serif;
}

html, body {
    height: 100%;
    overflow-x: hidden;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-light);
}

body {
    background: var(--bg-dark);
    color: var(--text-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background-color: var(--bg-light);
    box-shadow: var(--shadow-sm);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    height: 60px;
    display: flex;
    align-items: center;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-purple);
}

.logo img {
    width: 32px;
    height: 32px;
}

.nav-links {
    display: flex;
    gap: 20px;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-light);
    font-weight: 500;
    transition: color 0.3s;
    font-size: 0.9rem;
    opacity: 0.9;
}

.nav-links a:hover {
    color: var(--text-light);
    opacity: 1;
}

.btn-login {
    background-color: var(--primary-purple);
    color: var(--text-light);
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
    font-size: 0.9rem;
}

/* Hero Section */
.hero {
    padding-top: 40px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.hero .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 30px;
    max-width: 1200px;
    width: 100%;
    padding: 0 20px;
}

.hero-content {
    max-width: 800px;
    width: 100%;
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 40px;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 40px;
}

.feature-card {
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    background: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-card i {
    font-size: 2rem;
    margin-bottom: 15px;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.feature-card h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.feature-card p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.cta-button {
    background: var(--primary-gradient);
    color: var(--text-light);
    border: none;
    padding: 15px 40px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.hero-image img {
    max-width: 100%;
    height: auto;
    max-height: 300px;
}

/* Plans Section */
.plans {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 60px;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    padding: 40px 0;
}

.plan-box {
    background: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--glass-shadow);
    backdrop-filter: var(--glass-backdrop);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.plan-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    font-size: 24px;
    color: white;
    background: var(--primary-gradient);
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.plan-price {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    color: var(--text-light);
}

.plan-price .currency {
    font-size: 1.5rem;
    color: var(--text-light);
}

.plan-price .period {
    font-size: 1rem;
    color: var(--text-light);
    margin-left: 5px;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 0 0 30px;
    color: var(--text-light);
}

.plan-features li {
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.plan-features li i {
    color: var(--success);
    font-size: 1.2rem;
}

.plan-box.popular {
    border: 2px solid var(--accent-pink);
    background: linear-gradient(var(--glass-bg), var(--glass-bg)) padding-box,
                var(--primary-gradient) border-box;
}

.plan-box.popular::before {
    content: 'Popular';
    position: absolute;
    top: 12px;
    right: -30px;
    background: var(--primary-gradient);
    color: white;
    padding: 5px 30px;
    font-size: 0.875rem;
    transform: rotate(45deg);
}

.plan-box.popular:hover {
    transform: scale(1.05) translateY(-10px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-card);
    margin: 10% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 400px;
    position: relative;
    box-shadow: var(--shadow-lg);
    border: var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.modal h2 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-light);
    font-size: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 15px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-light);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    font-size: 1rem;
}

.submit-button {
    width: 100%;
    padding: 12px;
    background: var(--primary-gradient);
    color: var(--text-light);
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.modal-footer-text {
    text-align: center;
    margin-top: 15px;
    color: var(--text-light);
}

.modal-footer-text a {
    color: var(--primary-purple);
    text-decoration: none;
    font-weight: 500;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    color: var(--text-light);
    cursor: pointer;
}

/* Responsive Modal */
@media (max-width: 480px) {
    .modal-content {
        margin: 20% auto;
        width: 95%;
        padding: 15px;
    }
    
    .modal h2 {
        font-size: 1.2rem;
    }
    
    .form-group input {
        padding: 8px;
    font-size: 0.9rem;
    }
    
    .submit-button {
        padding: 10px;
        font-size: 0.9rem;
    }
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .plans-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .hero-features {
        grid-template-columns: 1fr;
    }

    .gradient-text {
        font-size: 2.5rem;
    }

    .plans-grid {
        grid-template-columns: 1fr;
        padding: 20px 0;
    }

    .plan-box.popular {
        transform: scale(1);
    }

    .plan-box.popular:hover {
        transform: translateY(-10px);
    }

    .plan-price {
        font-size: 2rem;
    }

    .feature-card {
        padding: 20px;
    }

    .modal-content {
        padding: 30px 20px;
    }
    
    .form-group input {
        font-size: 16px; /* Prevents zoom on mobile */
    }
}

@media (max-width: 480px) {
    .header .container {
        flex-direction: column;
        gap: 20px;
    }

    .nav-links {
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        background-color: var(--bg-light);
        flex-direction: column;
        padding: 20px;
        box-shadow: var(--shadow-sm);
        transform: translateY(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .nav-links.active {
        transform: translateY(0);
    }

    .nav-links a {
        width: 100%;
        text-align: center;
        padding: 10px 0;
    }

    .hero {
        padding: 120px 0 60px;
    }

    .gradient-text {
        font-size: 2rem;
    }
}

/* App-like Scrolling */
html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Add to Home Screen Prompt */
.add-to-home {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--bg-light);
    padding: 15px;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
}

.add-to-home p {
    margin-bottom: 10px;
    color: var(--text-dark);
}

.add-to-home button {
    background-color: var(--primary-purple);
    color: var(--text-light);
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.add-to-home button:hover {
    background-color: #45a049;
}

/* About Section */
.about {
    padding: 100px 0;
    background-color: var(--bg-dark);
}

.about h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.about-text {
    flex: 1;
}

.about-text p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: var(--text-light);
}

.features-list {
    list-style: none;
}

.features-list li {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.features-list i {
    color: var(--primary-purple);
}

.about-image {
    flex: 1;
    text-align: center;
}

.about-image img {
    max-width: 100%;
    height: auto;
}

/* FAQ Section */
.faq {
    padding: 100px 0;
    background-color: var(--bg-dark);
}

.faq h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.faq-item {
    background: var(--bg-card);
    padding: 30px;
    border-radius: 10px;
    transition: transform 0.3s;
}

.faq-item:hover {
    transform: translateY(-5px);
}

.faq-item h3 {
    margin-bottom: 15px;
    color: var(--primary-purple);
}

.faq-item p {
    color: var(--text-light);
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background-color: var(--bg-light);
}

.contact h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
}

.contact-content {
    display: flex;
    gap: 50px;
}

.contact-info {
    flex: 1;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-purple);
}

.contact-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-form input,
.contact-form textarea {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: inherit;
}

.contact-form textarea {
    height: 150px;
    resize: vertical;
}

/* Footer */
.footer {
    background-color: var(--bg-card);
    color: var(--text-light);
    padding: 50px 0 20px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-links {
    display: flex;
    gap: 30px;
}

.footer-links a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--primary-purple);
}

.social-links {
    display: flex;
    gap: 20px;
}

.social-links a {
    color: var(--text-light);
    font-size: 1.5rem;
    transition: color 0.3s;
}

.social-links a:hover {
    color: var(--primary-purple);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--bg-dark);
    overflow: hidden;
}

.animated-bg::before,
.animated-bg::after {
    content: '';
    position: absolute;
    width: 600px;
    height: 600px;
    border-radius: 50%;
    filter: blur(100px);
    opacity: 0.3;
    animation: move 20s infinite alternate;
}

.animated-bg::before {
    background: var(--primary-gradient);
    top: -300px;
    left: -300px;
}

.animated-bg::after {
    background: var(--accent-gradient);
    bottom: -300px;
    right: -300px;
    animation-delay: -10s;
}

@keyframes move {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(100px, 50px);
    }
}

/* Glass Card Effect */
.glass-card {
    background: var(--glass-bg);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border-radius: 20px;
    padding: 25px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 5px 0;
    background: var(--bg-card);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 10px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.9rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.logo i {
    font-size: 1rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-light);
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.glass-button {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 6px;
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Input Styles */
.input-group {
    position: relative;
    margin-bottom: 20px;
}

.input-field {
    width: 100%;
    padding: 12px 16px;
    background: var(--bg-light);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--text-light);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
}

.input-label {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    transition: all 0.3s ease;
    pointer-events: none;
}

.input-field:focus + .input-label,
.input-field:not(:placeholder-shown) + .input-label {
    top: 0;
    transform: translateY(-100%);
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Loading Animation */
.loading {
    width: 40px;
    height: 40px;
    border: 3px solid var(--glass-bg);
    border-radius: 50%;
    border-top-color: #6366f1;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Utility Classes */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: 8px;
}

.gap-4 {
    gap: 16px;
}

.mb-4 {
    margin-bottom: 16px;
}

.mb-8 {
    margin-bottom: 32px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn {
        padding: 10px 20px;
        font-size: 0.875rem;
    }
    
    .input-field {
        padding: 10px 14px;
        font-size: 0.875rem;
    }
}

/* Option Boxes */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.option-box {
    background: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--glass-shadow);
    backdrop-filter: var(--glass-backdrop);
    border-radius: 15px;
    padding: 25px;
    transition: all 0.3s ease;
}

.option-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.option-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    font-size: 20px;
    color: white;
    background: var(--primary-gradient);
}

.option-title {
        font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.option-description {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 20px;
}

/* Buttons */
.btn-choose {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-light);
    background: var(--primary-gradient);
    box-shadow: var(--shadow-md);
}

.btn-choose:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
}

.btn-choose.basic {
    background: var(--primary-gradient);
}

.btn-choose.standard {
    background: var(--accent-gradient);
}

.btn-choose.premium {
    background: var(--primary-gradient);
}

/* Responsive Design for Plans and Options */
@media (max-width: 1200px) {
    .plans-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .plans-grid {
        grid-template-columns: 1fr;
        padding: 20px 0;
    }

    .plan-box.popular {
        transform: scale(1);
    }

    .plan-box.popular:hover {
        transform: translateY(-10px);
    }

    .plan-price {
        font-size: 2rem;
    }

    .options-grid {
        grid-template-columns: 1fr;
    }
}

/* Demo Video */
.demo-video {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
    border-radius: 15px;
    background: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 15px;
}

/* Work Trial */
.work-trial {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    background: var(--bg-card);
    padding: 30px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.trial-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 20px;
}

/* About Grid */
.about-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin: 40px 0;
}

/* WhatsApp Support Button */
.whatsapp-support {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: #25D366;
    color: white;
    padding: 15px 25px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
}

.whatsapp-support i {
    font-size: 24px;
}

.whatsapp-support:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 16px rgba(37, 211, 102, 0.4);
}

/* Responsive Design Updates */
@media (max-width: 768px) {
    .about-grid {
        grid-template-columns: 1fr;
    }

    .trial-buttons {
        flex-direction: column;
    }

    .whatsapp-support {
        bottom: 20px;
        right: 20px;
        padding: 12px 20px;
    }

    .whatsapp-support span {
        display: none;
    }
}

/* Pricing Section */
.pricing-section {
    padding: 40px 15px;
    background-color: var(--dark-bg);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
    padding: 0 10px;
}

.pricing-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px 20px;
    text-align: center;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.popular-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    color: white;
}

.plan-icon {
    width: 50px;
    height: 50px;
    background: rgba(139, 92, 246, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.plan-icon i {
    font-size: 20px;
    color: #8b5cf6;
}

.pricing-card h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: white;
}

.price {
    font-size: 32px;
    font-weight: bold;
    color: white;
    margin-bottom: 20px;
}

.price .currency {
    font-size: 20px;
    vertical-align: super;
}

.price .duration {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.features {
    list-style: none;
    padding: 0;
    margin: 0 0 20px;
}

.features li {
    margin: 12px 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.features i {
    color: #10b981;
    margin-right: 8px;
    font-size: 12px;
}

.btn-primary {
    background: #8b5cf6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
    width: 100%;
}

/* Media Queries */
@media screen and (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        max-width: 320px;
        margin: 30px auto 0;
    }

    .pricing-card {
        padding: 20px 15px;
    }

    .price {
        font-size: 28px;
    }

    .features li {
        font-size: 13px;
    }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
    .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media screen and (max-width: 768px) {
    .header {
        padding: 3px 0;
    }
    
    .logo {
        font-size: 0.8rem;
    }
    
    .logo i {
        font-size: 0.9rem;
    }
    
    .nav-link, .glass-button {
        padding: 3px 6px;
        font-size: 0.7rem;
    }
}

/* Minimal Navigation */
.minimal-nav {
    position: fixed;
    top: 0;
    right: 0;
    padding: 1rem;
    z-index: 1000;
    display: flex;
    justify-content: flex-end;
    width: 100%;
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(10px);
}

.nav-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-link.glass-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: var(--glass-bg);
    border: var(--glass-border);
    border-radius: 8px;
    color: var(--text-light);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: var(--glass-backdrop);
}

.nav-link.glass-button i {
    font-size: 1rem;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-link.glass-button:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .minimal-nav {
        padding: 0.75rem;
    }

    .nav-buttons {
        gap: 0.75rem;
    }
    
    .nav-link.glass-button {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }
    
    .nav-link.glass-button i {
        font-size: 0.9rem;
        width: 0.9rem;
        height: 0.9rem;
    }
}

@media (max-width: 480px) {
    .minimal-nav {
        padding: 0.5rem;
    }
    
    .nav-buttons {
        gap: 0.5rem;
    }
    
    .nav-link.glass-button {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .nav-link.glass-button i {
        font-size: 0.85rem;
        width: 0.85rem;
        height: 0.85rem;
    }
}

/* Fix for orientation changes */
@media (orientation: landscape) {
    .minimal-nav {
        padding: 0.75rem;
    }
    
    .nav-link.glass-button {
        padding: 0.6rem 1rem;
    }
}

@media (orientation: portrait) {
    .minimal-nav {
        padding: 0.5rem;
    }
    
    .nav-link.glass-button {
        padding: 0.5rem 0.75rem;
    }
}

/* Logo in Hero */
.logo-hero {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
}

.logo-hero i {
    font-size: 4rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.logo-hero span {
    font-size: 3.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Update Hero Section */
.hero {
    padding-top: 40px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

@media (max-width: 768px) {
    .minimal-nav {
        top: 20px;
        right: 20px;
    }

    .nav-link.glass-button {
        padding: 25px 50px;
        font-size: 3.6rem;
    }

    .nav-link.glass-button i {
        font-size: 4.5rem;
    }

    .logo-hero i {
        font-size: 3rem;
    }

    .logo-hero span {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .minimal-nav {
        top: 15px;
        right: 15px;
    }

    .nav-buttons {
        gap: 20px;
    }

    .nav-link.glass-button {
        padding: 20px 40px;
        font-size: 3.15rem;
    }

    .nav-link.glass-button i {
        font-size: 4.05rem;
    }

    .logo-hero i {
        font-size: 2.5rem;
    }

    .logo-hero span {
        font-size: 2rem;
    }
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 1rem;
    transition: transform 0.2s;
}

.activity-item:hover {
    transform: translateX(5px);
}

.activity-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-icon i {
        font-size: 1.2rem;
    color: #fff;
}

.activity-details {
    flex: 1;
}

.activity-details h4 {
    margin: 0;
    color: #fff;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.activity-details p {
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.activity-details small {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8rem;
}

/* Scrollbar styles for activity list */
.activity-list::-webkit-scrollbar {
    width: 6px;
}

.activity-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    transition: color 0.3s ease;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

.stat-change i {
    font-size: 0.9rem;
}

/* Update stat card hover effect */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-info p {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 10px 0;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
} 
.password-wrapper {
  position: relative;
}

.password-wrapper input {
  width: 100%;
  padding-right: 40px; /* space for eye icon */
}

.toggle-password {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--text-light);
}
.password-wrapper {
    position: relative;
  }
  
  .password-wrapper input {
    width: 100%;
    padding-right: 40px; /* space for eye icon */
  }
  
  .toggle-password {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--text-light);
  }
  