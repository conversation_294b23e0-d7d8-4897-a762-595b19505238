/* Dashboard Layout */
.dashboard-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    min-height: 100vh;
    background: #ffffff;
    color: #1f2937;
}

/* Sidebar */
.sidebar {
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    position: fixed;
    width: 280px;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    margin-bottom: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.logo i {
    font-size: 2rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 15px;
    background: rgba(34, 197, 94, 0.1);
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar i {
    font-size: 1.5rem;
    color: white;
}

.user-details h3 {
    font-size: 1.1rem;
    margin: 0;
}

.user-details p {
    margin: 5px 0 0;
    font-size: 0.9rem;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    border-radius: 12px;
    color: rgba(31, 41, 55, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-item:hover {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    transform: translateX(5px);
}

.nav-item.active {
    background: var(--primary-gradient);
    color: white;
}

.nav-item i {
    font-size: 1.2rem;
}

.logout-button {
    margin-top: auto;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: rgba(239, 68, 68, 0.2);
    color: #ffffff;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-button i {
    color: #ef4444;
}

/* Sidebar Footer Links */
.sidebar-footer {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 15px;
    padding: 0 15px;
}

.footer-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    text-decoration: none;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
}

.footer-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    margin-left: 280px;
    padding: 30px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-radius: 20px;
    margin-bottom: 30px;
}

.content-header h1 {
    font-size: 1.8rem;
    margin: 0;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.header-icon:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
}

.header-icon i {
    font-size: 1.2rem;
}

.header-logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: rgba(239, 68, 68, 0.2);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.header-logout-btn:hover {
    background: rgba(239, 68, 68, 0.4);
    transform: translateY(-2px);
}

.header-logout-btn i {
    color: #ef4444;
}

.notification-bell {
    position: relative;
    cursor: pointer;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 10px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    padding: 25px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-icon i {
    font-size: 1.8rem;
    color: white;
}

.stat-info h3 {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 5px 0;
}

.stat-info p {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.stat-change {
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

/* Upgrade Button in Stats Card */
.upgrade-stat-card .stat-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
}

.upgrade-btn {
    margin-top: 10px;
    padding: 8px 15px;
    background: linear-gradient(135deg, #FF9966 0%, #FF5E62 100%);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    width: 100%;
}

.upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 94, 98, 0.3);
}

#userPlanDisplay {
    font-weight: 600;
    background: linear-gradient(135deg, #FF9966 0%, #FF5E62 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    /* Adjusted for removal of Recent Activities and Daily Progress sections */
}

/* Current Plan Card Styles */
.current-plan-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
}

#currentPlanCard {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

#currentPlanCard.plan-card-hover,
#currentPlanCard:hover {
    transform: translateY(-5px);
    border-color: var(--accent-pink);
    box-shadow: 0 8px 25px rgba(217, 70, 239, 0.3);
}

#currentPlanCard.plan-card-hover::before,
#currentPlanCard:hover::before {
    opacity: 1;
    height: 7px;
}

.plan-status {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.active-days, .days-left {
    font-size: 0.9rem;
    padding: 5px 10px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
}

.active-days {
    color: var(--accent-pink);
}

.days-left {
    color: var(--success);
}

.upgrade-plan-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    margin-top: 15px;
    box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
}

.upgrade-plan-btn:hover {
    background: var(--accent-gradient);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(217, 70, 239, 0.4);
}

/* Plan-specific styling */
.plan-trial::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(135deg, #64748b 0%, #94a3b8 100%);
    opacity: 0.7;
}

/* Plan Selection Dialog Styles */
.plan-selection-text {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: var(--text-light);
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.plan-card {
    padding: 20px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.plan-card:hover, .plan-card-hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
}

.plan-header {
    margin-bottom: 15px;
}

.plan-header h4 {
    font-size: 1.3rem;
    margin: 0 0 5px 0;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.plan-price {
    font-size: 0.9rem;
    color: var(--accent-pink);
}

.plan-features {
    margin-bottom: 20px;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.plan-features li i {
    color: var(--accent-pink);
}

.select-plan-btn {
    width: 100%;
    padding: 10px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.select-plan-btn:hover {
    background: var(--accent-gradient);
    transform: translateY(-2px);
}

.plan-junior::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(135deg, #6366f1 0%, #a5b4fc 100%);
    opacity: 0.7;
}

.plan-senior::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(135deg, #8b5cf6 0%, #c4b5fd 100%);
    opacity: 0.7;
}

.plan-executive::before,
.plan-expert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(135deg, #d946ef 0%, #f0abfc 100%);
    opacity: 0.7;
}

.dashboard-card {
    padding: 25px;
    border-radius: 20px;
    height: 100%;
}

.dashboard-card h2 {
    font-size: 1.3rem;
    margin: 0 0 20px 0;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-button:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
}

/* Activity List - Commented out as Recent Activities section was removed */
/*
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-details h4 {
    font-size: 1rem;
    margin: 0 0 5px 0;
}

.activity-details p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 5px 0;
}

.activity-details small {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
}
*/

/* Progress Section - Commented out as Daily Progress section was removed */
/*
.progress-info {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
}

.progress-stats {
    margin-bottom: 20px;
}

.progress-stat h4 {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 5px 0;
}

.progress-stat p {
    font-size: 1.2rem;
    margin: 0;
    text-align: center;
}

.progress-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: width 0.3s ease;
}
*/

/* Referral Stats */
.referral-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.referral-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.referral-stat i {
    font-size: 1.5rem;
    color: #10b981;
}

.stat-details h4 {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 5px 0;
}

.stat-details p {
    font-size: 1.2rem;
    margin: 0;
}

.btn-referral {
    width: 100%;
    padding: 12px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-referral:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        grid-template-columns: 1fr;
        padding-top: 85px;
    }

    .sidebar {
        display: none;
    }

    .main-content {
        margin-left: 0;
        padding: 15px;
        height: calc(100vh - 85px);
        overflow-y: auto;
    }

    .content-header {
        padding-top: 0;
    }

    .content-header h1 {
        font-size: 1.5rem;
    }

    .header-actions {
        gap: 10px;
    }

    .header-icon {
        width: 35px;
        height: 35px;
    }

    .header-icon i {
        font-size: 1rem;
    }

    .header-logout-btn {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    .header-logout-btn span {
        display: none;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 15px;
    }

    .stat-card {
        padding: 15px;
        gap: 10px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-icon i {
        font-size: 1.2rem;
    }

    .stat-info h3 {
        font-size: 0.8rem;
    }

    .stat-info p {
        font-size: 1.2rem;
    }

    .stat-change {
        font-size: 0.8rem;
    }

    .dashboard-grid {
        gap: 10px;
    }

    .dashboard-card {
        padding: 15px;
    }

    .dashboard-card h2 {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    /* Make action buttons smaller on mobile */
    .action-button {
        padding: 10px;
        font-size: 0.85rem;
    }

    .action-button i {
        font-size: 1rem;
    }

    .activity-item {
        padding: 10px;
    }

    .activity-icon {
        width: 30px;
        height: 30px;
    }

    .activity-details h4 {
        font-size: 0.9rem;
    }

    .activity-details p {
        font-size: 0.8rem;
    }

    .activity-details small {
        font-size: 0.7rem;
    }

    .mobile-menu {
        display: block;
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: 15px;
    }

    .content-header h1 {
        font-size: 1.2rem;
    }

    .header-actions {
        gap: 8px;
    }

    .header-icon {
        width: 30px;
        height: 30px;
    }

    .header-logout-btn {
        padding: 5px 10px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .stat-card {
        padding: 12px;
        gap: 8px;
    }

    .stat-icon {
        width: 35px;
        height: 35px;
    }

    .stat-info h3 {
        font-size: 0.75rem;
    }

    .stat-info p {
        font-size: 1rem;
    }

    .stat-change {
        font-size: 0.7rem;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .action-button {
        padding: 8px;
        font-size: 0.8rem;
    }

    .mobile-nav-item.logout-btn i {
        font-size: 20px;
    }

    .mobile-nav-item.logout-btn span {
        font-size: 10px;
        color: #000;
    }
}

/* Plan Selection Dialog Styles */
.plans-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin: 1rem 0;
}

.plan-selection-text {
    color: var(--text-light);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.plan-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    color: var(--text-light);
    backdrop-filter: blur(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-gradient);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.plan-card-hover,
.plan-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-pink);
    box-shadow: 0 8px 25px rgba(217, 70, 239, 0.3);
}

.plan-card-hover::before,
.plan-card:hover::before {
    opacity: 1;
    height: 7px;
}

.plan-header {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--text-light);
    font-size: 1.5rem;
    font-weight: 600;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.plan-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-top: 5px;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 15px 0;
    text-align: left;
}

.plan-features ul li {
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    color: var(--text-light);
    font-size: 0.95rem;
}

.plan-features ul li i {
    margin-right: 10px;
    color: var(--accent-pink);
    font-size: 1rem;
    margin-top: 3px;
}

.plan-features ul li i.fa-check {
    color: var(--success);
}

.plan-features ul li i.fa-certificate {
    color: #FFD700; /* Gold color for certificates */
}

.plan-features ul li i.fa-info-circle {
    color: var(--info);
}

.select-plan-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    margin-top: 15px;
    box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
}

.select-plan-btn:hover {
    background: var(--accent-gradient);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(217, 70, 239, 0.4);
}

.select-plan-btn i {
    font-size: 1.2rem;
}

/* SweetAlert2 custom styling */
.swal2-cancel-button-themed {
    background: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-light) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.swal2-cancel-button-themed:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.swal2-title.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    font-weight: 700;
}

.swal2-popup.glass-card {
    background: var(--bg-card) !important;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    backdrop-filter: blur(10px);
}

/* Responsive Design for Plan Selection */
@media (max-width: 768px) {
    .plans-grid {
        grid-template-columns: 1fr;
    }
}

/* SweetAlert2 Custom Styles */
.swal-popup {
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(106, 17, 203, 0.2);
}

.swal-title {
    color: #6A11CB;
    font-weight: 600;
}

.swal2-actions button {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 24px;
}

.swal2-cancel {
    background: rgba(106, 17, 203, 0.1) !important;
    color: #6A11CB !important;
    border: 1px solid rgba(106, 17, 203, 0.3) !important;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-container {
    animation: fadeIn 0.5s ease-out;
}

/* Mobile Menu Styles */
.mobile-menu {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #1a1f2e;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    padding: 5px 0;
    border-top: 1px solid #2d3748;
    height: 60px;
}

.mobile-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 0 auto;
    padding: 0 5px;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
}

.mobile-nav::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #ffffff;
    font-size: 0.7rem;
    padding: 6px 1px;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 40px;
    flex: 1;
    text-align: center;
}

.mobile-nav-item i {
    font-size: 1.1rem;
    margin-bottom: 2px;
    color: #ffffff;
}

.mobile-nav-item span {
    font-weight: 500;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0;
    white-space: nowrap;
    font-size: 0.55rem;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.mobile-nav-item.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.mobile-nav-item:hover {
    transform: translateY(-2px);
}

.mobile-nav-item.logout-btn {
    background: none;
    border: none;
    font-family: inherit;
    font-size: inherit;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    width: 20%;
}

.mobile-nav-item.logout-btn i {
    font-size: 24px;
    color: #ef4444;
}

.mobile-nav-item.logout-btn span {
    font-size: 12px;
    color: #ffffff;
    font-weight: 600;
}

@media screen and (orientation: portrait) {
    .mobile-menu {
        display: block;
    }

    .sidebar {
        display: none;
    }

    .main-content {
        margin-left: 0;
        padding-bottom: 80px;
    }

    .dashboard-container {
        grid-template-columns: 1fr;
    }
}

@media screen and (min-width: 361px) and (max-width: 480px) {
    .mobile-nav-item {
        min-width: 35px;
        padding: 5px 1px;
    }

    .mobile-nav-item i {
        font-size: 1.05rem;
    }

    .mobile-nav-item span {
        font-size: 0.5rem;
    }
}

@media screen and (max-width: 360px) {
    .mobile-nav {
        padding: 0 1px;
    }

    .mobile-nav-item {
        min-width: 32px;
        padding: 5px 0;
    }

    .mobile-nav-item i {
        font-size: 1rem;
        margin-bottom: 1px;
    }

    .mobile-nav-item span {
        font-size: 0.45rem;
        letter-spacing: 0;
    }
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #0f172a, #1e293b);
    z-index: -1;
    overflow: hidden;
}

.animated-bg::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}