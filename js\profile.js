import { initializeApp, getApps } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
import {
  getAuth,
  EmailAuthProvider,
  reauthenticateWithCredential,
  updatePassword,
  onAuthStateChanged,
  updateProfile,
  updateEmail,
  signOut
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import {
  getFirestore,
  doc,
  getDoc,
  setDoc,
  serverTimestamp
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

// Import cache utilities
import {
  cacheUserData,
  getCachedUserData,
  CACHE_DURATIONS
} from './cache-utils.js';

// Import cross-page data sharing utilities
import { setupPageTransitionHandlers } from './page-transition.js';
import {
  updateLastLoaded,
  wasRecentlyLoaded,
  cameFromPage,
  DATA_TYPES,
  MAX_AGE
} from './shared-data-store.js';

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyAMMabAMVSrNzKdTEVOcn4Ffh7oDVxD2EM",
  authDomain: "mytube-earnings.firebaseapp.com",
  projectId: "mytube-earnings",
  storageBucket: "mytube-earnings.firebasestorage.app",
  messagingSenderId: "116772605182",
  appId: "1:116772605182:web:a5e9875d48867cca03e9af",
  measurementId: "G-MR5HFN8J4V"
};

// Init Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const auth = getAuth(app);
const db = getFirestore(app);

// SweetAlert helper
function showAlert(msg, success = true) {
  Swal.fire({
    icon: success ? 'success' : 'error',
    title: success ? 'Success' : 'Error',
    text: msg,
    confirmButtonColor: success ? '#3085d6' : '#d33'
  });
}

document.addEventListener('DOMContentLoaded', () => {
  // Set up page transition handlers
  setupPageTransitionHandlers('profile');

  const personalInfoForm = document.getElementById('personalInfoForm');
  const bankDetailsForm = document.getElementById('bankDetailsForm');
  const passwordForm = document.getElementById('passwordForm');
  const logoutButton = document.getElementById('logout');

  const userName = document.getElementById('user-name');
  const userEmail = document.getElementById('user-email');
  const currentPlanElement = document.getElementById('currentPlan');
  const joinDateElement = document.getElementById('joinDate');

  const totalEarnings = document.getElementById('total-earnings');
  const totalWithdrawals = document.getElementById('total-withdrawals');
  const totalVideos = document.getElementById('total-videos');

  const displayBankName = document.getElementById('displayBankName');
  const displayAccountHolder = document.getElementById('displayAccountHolder');
  const displayAccountNumber = document.getElementById('displayAccountNumber');
  const displayIfscCode = document.getElementById('displayIfscCode');

  const toggleBankDetails = document.getElementById('toggleBankDetails');
  const bankDetailsDisplay = document.getElementById('bankDetailsDisplay');

  // Toggle show/hide bank details
  if (toggleBankDetails && bankDetailsDisplay) {
    toggleBankDetails.addEventListener('click', () => {
      bankDetailsDisplay.style.display = bankDetailsDisplay.style.display === 'none' ? 'block' : 'none';
    });
  }

  onAuthStateChanged(auth, async (user) => {
    if (!user) return (window.location.href = 'index.html');

    // Check if we recently came from the dashboard page
    if (cameFromPage('dashboard', MAX_AGE.MEDIUM)) {
      console.log('Recently came from dashboard page, using cached data');

      // Try to get cached user data
      const cachedData = getCachedUserData(user.uid);
      if (cachedData) {
        console.log('Using cached user data from dashboard');

        // Update UI immediately with cached data
        updateProfileUI(cachedData, user);

        // Mark user data as loaded
        updateLastLoaded(DATA_TYPES.USER_DATA);

        // No need to refresh from Firebase
        return;
      }
    }

    // Check if user data was recently loaded
    if (wasRecentlyLoaded(DATA_TYPES.USER_DATA, MAX_AGE.MEDIUM)) {
      console.log('User data was recently loaded, using cached data');

      // Try to get cached user data
      const cachedData = getCachedUserData(user.uid);
      if (cachedData) {
        console.log('Using recently loaded cached user data');

        // Update UI immediately with cached data
        updateProfileUI(cachedData, user);

        // No need to refresh from Firebase
        return;
      }
    }

    // Try to get user data from cache first
    let data = null;
    try {
      const cachedData = getCachedUserData(user.uid);

      if (cachedData) {
        console.log('Using cached user data in profile page');
        data = cachedData;

        // Update UI immediately with cached data
        updateProfileUI(data, user);

        // Refresh data in background
        refreshUserData(user.uid);

        // Mark user data as loaded
        updateLastLoaded(DATA_TYPES.USER_DATA);
      } else {
        // No cache, load from Firebase
        console.log('No cached data in profile page, loading from Firebase');
        const userRef = doc(db, "users", user.uid);
        const docSnap = await getDoc(userRef);

        if (!docSnap.exists()) return showAlert("User data not found.", false);

        data = docSnap.data();

        // Check if createdAt field is missing and add it if needed
        if (!data.createdAt) {
          console.log('createdAt field missing, adding it now');

          // Try to use user metadata for creation time
          if (user.metadata && user.metadata.creationTime) {
            const creationTime = new Date(user.metadata.creationTime);

            // Update the user document with the createdAt field
            try {
              await setDoc(userRef, {
                createdAt: serverTimestamp(),
                joinDate: creationTime
              }, { merge: true });

              // Add to local data object
              data.createdAt = serverTimestamp();
              data.joinDate = creationTime;

              console.log('Added createdAt and joinDate fields to user document');
            } catch (error) {
              console.error('Error updating user document with createdAt:', error);
            }
          }
        }

        // Cache the user data for future use
        cacheUserData(user.uid, data);

        // Mark user data as loaded
        updateLastLoaded(DATA_TYPES.USER_DATA);

        // Update UI
        updateProfileUI(data, user);
      }
    } catch (error) {
      console.error('Error loading user data:', error);

      // Fallback to direct Firebase load if caching fails
      const userRef = doc(db, "users", user.uid);
      const docSnap = await getDoc(userRef);
      if (!docSnap.exists()) return showAlert("User data not found.", false);

      data = docSnap.data();

      // Mark user data as loaded
      updateLastLoaded(DATA_TYPES.USER_DATA);
    }

    // Function to update profile UI with user data
    function updateProfileUI(userData, user) {
      try {
        // Debug user data structure to help identify fields
        console.log('User data structure:', JSON.stringify(userData, null, 2));

        // Top display
        if (userName) userName.textContent = userData.fullName || '';
        if (userEmail) userEmail.textContent = user.email || '';
        if (currentPlanElement && userData.plan?.name) currentPlanElement.textContent = `Plan: ${userData.plan.name}`;
        // Handle join date with multiple fallbacks
        if (joinDateElement) {
          try {
            let joinDate;

            // Check if createdAt is a Firestore timestamp
            if (userData.createdAt && typeof userData.createdAt.toDate === 'function') {
              joinDate = userData.createdAt.toDate();
            }
            // Check if createdAt is a regular Date object or timestamp number
            else if (userData.createdAt && (userData.createdAt instanceof Date || typeof userData.createdAt === 'number')) {
              joinDate = new Date(userData.createdAt);
            }
            // Check if createdAt is a string date
            else if (userData.createdAt && typeof userData.createdAt === 'string') {
              joinDate = new Date(userData.createdAt);
            }
            // Check if joinDate field exists
            else if (userData.joinDate) {
              if (typeof userData.joinDate.toDate === 'function') {
                joinDate = userData.joinDate.toDate();
              } else {
                joinDate = new Date(userData.joinDate);
              }
            }
            // Check if metadata.creationTime exists
            else if (userData.metadata && userData.metadata.creationTime) {
              joinDate = new Date(userData.metadata.creationTime);
            }
            // Use auth user's metadata as last resort
            else if (user.metadata && user.metadata.creationTime) {
              joinDate = new Date(user.metadata.creationTime);
            }
            // Default to current date if all else fails
            else {
              joinDate = new Date();
              console.warn('No join date found, using current date as fallback');
            }

            // Format the date
            joinDateElement.textContent = `Joined: ${joinDate.toLocaleDateString()}`;
          } catch (error) {
            console.error('Error formatting join date:', error);
            joinDateElement.textContent = 'Joined: Unknown';
          }
        }

        // Use stats.totalVideosCompleted as the source of truth for video counts
        if (totalVideos) totalVideos.textContent = userData.stats?.totalVideosCompleted || 0;

        // Get wallet data with multiple fallbacks
        const wallet = userData.wallet || {};

        // Log wallet data for debugging
        console.log('Wallet data:', wallet);

        // Check if wallet data exists, if not, create it
        if (!userData.wallet) {
          console.log('Wallet data not found, will create it');
          // We'll update Firebase in a separate function
          ensureWalletData(user.uid, userData);
        }

        // Get earnings with multiple fallbacks
        const earnings = wallet.earning || wallet.earnings || wallet.balance || userData.totalEarnings || 0;
        // Get withdrawals with multiple fallbacks
        const withdrawals = userData.totalWithdrawals || wallet.withdrawals || 0;

        // Format earnings with proper currency symbol and decimal places
        if (totalEarnings) {
          totalEarnings.textContent = `₹${parseFloat(earnings).toFixed(2)}`;
          console.log('Total earnings updated:', earnings);
        }

        // Format withdrawals with proper currency symbol and decimal places
        if (totalWithdrawals) {
          totalWithdrawals.textContent = `₹${parseFloat(withdrawals).toFixed(2)}`;
          console.log('Total withdrawals updated:', withdrawals);
        }

        // Fill personal info form - add null checks for all form elements
        const fullNameInput = document.getElementById('fullNameInput');
        const phoneNumberInput = document.getElementById('phoneNumberInput');
        const emailInput = document.getElementById('emailInput');
        const cityInput = document.getElementById('cityInput');

        if (fullNameInput) fullNameInput.value = userData.fullName || '';
        if (phoneNumberInput) phoneNumberInput.value = userData.phoneNumber || '';
        if (emailInput) emailInput.value = userData.email || '';
        if (cityInput) cityInput.value = userData.city || '';

        // Safely handle bank details (consistent path)
        // First try to get the bankDetails object
        let bank = userData.bankDetails || userData.bank || userData.data?.bankDetails || {};

        // If bank is empty or missing required fields, check if fields might be at root level
        if (!bank.bank && !bank.holder && !bank.account && !bank.ifsc) {
          console.log('Bank details object is empty, checking for fields at root level');
          // Try to construct bank details from root level fields
          bank = {
            bank: userData.bankName || '',
            holder: userData.accountHolder || userData.holderName || '',
            account: userData.accountNumber || '',
            ifsc: userData.ifscCode || ''
          };
        }

        // Log bank details for debugging
        console.log('Bank details from user data:', bank);
        console.log('Root level bank fields:', {
          bankName: userData.bankName,
          accountHolder: userData.accountHolder,
          accountNumber: userData.accountNumber,
          ifscCode: userData.ifscCode
        });

        // Fill bank details form - use the correct IDs from the HTML form
        const bankNameInput = document.getElementById('edit-bank');
        const accountHolderInput = document.getElementById('edit-holder');
        const accountNumberInput = document.getElementById('edit-account');
        const ifscCodeInput = document.getElementById('edit-ifsc');

        // Log form elements to verify they exist
        console.log('Bank form elements found in updateProfileUI:', {
          bankNameInput: !!bankNameInput,
          accountHolderInput: !!accountHolderInput,
          accountNumberInput: !!accountNumberInput,
          ifscCodeInput: !!ifscCodeInput
        });

        // Fill form fields with values from any available source
        if (bankNameInput) bankNameInput.value = bank.bank || userData.bankName || '';
        if (accountHolderInput) accountHolderInput.value = bank.holder || userData.accountHolder || userData.holderName || '';
        if (accountNumberInput) accountNumberInput.value = bank.account || userData.accountNumber || '';
        if (ifscCodeInput) ifscCodeInput.value = bank.ifsc || userData.ifscCode || '';

        // Also update the display section
        if (displayBankName) displayBankName.textContent = bank.bank || userData.bankName || '-';
        if (displayAccountHolder) displayAccountHolder.textContent = bank.holder || userData.accountHolder || userData.holderName || '-';
        if (displayAccountNumber) displayAccountNumber.textContent = bank.account || userData.accountNumber || '-';
        if (displayIfscCode) displayIfscCode.textContent = bank.ifsc || userData.ifscCode || '-';
      } catch (error) {
        console.error('Error updating profile UI:', error);
      }
    }

    // Function to refresh user data in the background
    async function refreshUserData(userId) {
      try {
        console.log('Refreshing user data in background');
        const userRef = doc(db, "users", userId);
        const docSnap = await getDoc(userRef);

        if (docSnap.exists()) {
          const freshData = docSnap.data();

          // Check if createdAt field is missing and add it if needed
          if (!freshData.createdAt) {
            console.log('createdAt field missing in refreshed data, adding it now');

            // Try to use user metadata for creation time
            const currentUser = auth.currentUser;
            if (currentUser && currentUser.metadata && currentUser.metadata.creationTime) {
              const creationTime = new Date(currentUser.metadata.creationTime);

              // Update the user document with the createdAt field
              try {
                await setDoc(userRef, {
                  createdAt: serverTimestamp(),
                  joinDate: creationTime
                }, { merge: true });

                // Add to local data object
                freshData.createdAt = serverTimestamp();
                freshData.joinDate = creationTime;

                console.log('Added createdAt and joinDate fields to user document during refresh');
              } catch (error) {
                console.error('Error updating user document with createdAt during refresh:', error);
              }
            }
          }

          // Cache the fresh data
          cacheUserData(userId, freshData);

          // Only update UI if data has changed
          const cachedData = getCachedUserData(userId);
          if (JSON.stringify(cachedData) !== JSON.stringify(freshData)) {
            console.log('User data changed, updating UI');
            updateProfileUI(freshData, auth.currentUser);
          }
        }
      } catch (error) {
        console.error('Error refreshing user data:', error);
      }
    }

    // Safely handle bank details (consistent path)
    // First try to get the bankDetails object
    let bank = data.bankDetails || data.bank || data.data?.bankDetails || {};

    // If bank is empty or missing required fields, check if fields might be at root level
    if (!bank.bank && !bank.holder && !bank.account && !bank.ifsc) {
      console.log('Bank details object is empty, checking for fields at root level');
      // Try to construct bank details from root level fields
      bank = {
        bank: data.bankName || '',
        holder: data.accountHolder || data.holderName || '',
        account: data.accountNumber || '',
        ifsc: data.ifscCode || ''
      };
    }

    // Log bank details for debugging
    console.log('Bank details from user data (outside updateProfileUI):', bank);
    console.log('Root level bank fields (outside updateProfileUI):', {
      bankName: data.bankName,
      accountHolder: data.accountHolder,
      accountNumber: data.accountNumber,
      ifscCode: data.ifscCode
    });

    // Fill the edit form fields - add null checks
    const editBank = document.getElementById('edit-bank');
    const editHolder = document.getElementById('edit-holder');
    const editAccount = document.getElementById('edit-account');
    const editIfsc = document.getElementById('edit-ifsc');

    // Log form elements to verify they exist
    console.log('Bank form elements found (outside updateProfileUI):', {
      editBank: !!editBank,
      editHolder: !!editHolder,
      editAccount: !!editAccount,
      editIfsc: !!editIfsc
    });

    // Fill form fields with values from any available source
    if (editBank) editBank.value = bank.bank || data.bankName || '';
    if (editHolder) editHolder.value = bank.holder || data.accountHolder || data.holderName || '';
    if (editAccount) editAccount.value = bank.account || data.accountNumber || '';
    if (editIfsc) editIfsc.value = bank.ifsc || data.ifscCode || '';

    // Preview section update - add null checks
    if (displayBankName) displayBankName.textContent = bank.bank || data.bankName || '-';
    if (displayAccountHolder) displayAccountHolder.textContent = bank.holder || data.accountHolder || data.holderName || '-';
    if (displayAccountNumber) displayAccountNumber.textContent = bank.account || data.accountNumber || '-';
    if (displayIfscCode) displayIfscCode.textContent = bank.ifsc || data.ifscCode || '-';

    // Toggle Bank Details Display
    const toggleBankDetailsBtn = document.getElementById('toggleBankDetails');
    const bankDetailsDisplay = document.getElementById('bankDetailsDisplay');

    if (toggleBankDetailsBtn && bankDetailsDisplay) {
      toggleBankDetailsBtn.addEventListener('click', () => {
        if (bankDetailsDisplay.style.display === 'none') {
          bankDetailsDisplay.style.display = 'block';
          toggleBankDetailsBtn.textContent = 'Hide Bank Details';
        } else {
          bankDetailsDisplay.style.display = 'none';
          toggleBankDetailsBtn.textContent = 'View Bank Details';
        }
      });

      // If bank details are filled, show them by default
      if (bank.bank && bank.holder && bank.account && bank.ifsc) {
        // Only show if all fields are filled
        bankDetailsDisplay.style.display = 'block';
        toggleBankDetailsBtn.textContent = 'Hide Bank Details';
      }
    }

    // Personal Info Update
    if (personalInfoForm) {
      personalInfoForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const name = document.getElementById('fullNameInput').value.trim();
        const phone = document.getElementById('phoneNumberInput').value.trim();
        const email = document.getElementById('emailInput').value.trim();
        const city = document.getElementById('cityInput').value.trim();

        try {
          await updateProfile(user, { displayName: name });
          if (email !== user.email) await updateEmail(user, email);

          const updateData = {
            name, phone, email, city,
            updatedAt: serverTimestamp()
          };

          await setDoc(userRef, updateData, { merge: true });

          // Update the cached user data
          const cachedData = getCachedUserData(user.uid);
          if (cachedData) {
            const updatedData = {
              ...cachedData,
              fullName: name,
              phoneNumber: phone,
              email: email,
              city: city,
              updatedAt: new Date()
            };
            cacheUserData(user.uid, updatedData);
          }

          showAlert("Profile updated!");
        } catch (err) {
          showAlert(err.message, false);
        }
      });
    }

    // Bank Details Update - Enhanced with better error handling
    if (bankDetailsForm) {
      console.log('Bank details form found, setting up enhanced event listener');

      // Remove any existing event listeners to prevent duplicates
      const newBankDetailsForm = bankDetailsForm.cloneNode(true);
      bankDetailsForm.parentNode.replaceChild(newBankDetailsForm, bankDetailsForm);

      // Update the reference to the new form
      const updatedBankDetailsForm = document.getElementById('bankDetailsForm');

      if (updatedBankDetailsForm) {
        updatedBankDetailsForm.addEventListener('submit', async (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Bank details form submitted via profile.js');

          // Validate form fields first
          const bankInput = document.getElementById('edit-bank');
          const holderInput = document.getElementById('edit-holder');
          const accountInput = document.getElementById('edit-account');
          const ifscInput = document.getElementById('edit-ifsc');

          // Check if all required fields exist
          if (!bankInput || !holderInput || !accountInput || !ifscInput) {
            console.error('One or more form fields not found');
            Swal.fire({
              icon: 'error',
              title: 'Form Error',
              text: 'Form fields not found. Please refresh the page and try again.',
              confirmButtonColor: '#ef4444'
            });
            return;
          }

          // Get values and validate
          const bank = bankInput.value.trim();
          const holder = holderInput.value.trim();
          const account = accountInput.value.trim();
          const ifsc = ifscInput.value.trim();

          // Validate required fields
          if (!bank || !holder || !account || !ifsc) {
            Swal.fire({
              icon: 'warning',
              title: 'Missing Information',
              text: 'Please fill in all bank details fields.',
              confirmButtonColor: '#f59e0b'
            });
            return;
          }

          // Show loading indicator
          Swal.fire({
            title: 'Saving...',
            text: 'Updating your bank details',
            allowOutsideClick: false,
            didOpen: () => {
              Swal.showLoading();
            }
          });

          console.log('Bank details being saved:', { bank, holder, account, ifsc });

          try {
            // Check if user is still authenticated
            if (!user || !user.uid) {
              throw new Error('User not authenticated. Please log in again.');
            }

            // Double-check authentication with Firebase Auth
            const currentUser = auth.currentUser;
            if (!currentUser || currentUser.uid !== user.uid) {
              throw new Error('Authentication session expired. Please log in again.');
            }

            const userRef = doc(db, "users", user.uid);
            console.log('User reference created for ID:', user.uid);

            const bankDetails = {
              bank, holder, account, ifsc
            };

            console.log('Saving bank details to Firebase:', bankDetails);

            // Use a single comprehensive update to avoid conflicts
            const updateData = {
              // Primary storage as bankDetails object
              bankDetails: bankDetails,
              // Also store at root level for compatibility
              bankName: bank,
              accountHolder: holder,
              accountNumber: account,
              ifscCode: ifsc,
              // Alternative storage as bank object
              bank: bankDetails,
              // Update timestamp
              updatedAt: serverTimestamp()
            };

            await setDoc(userRef, updateData, { merge: true });
            console.log('Bank details saved to Firebase successfully');

            // Verify the data was saved by reading it back
            const updatedDoc = await getDoc(userRef);
            if (updatedDoc.exists()) {
              const updatedData = updatedDoc.data();
              console.log('Verification: Bank details saved successfully');
              console.log('Saved bankDetails:', updatedData.bankDetails);
            }

            // Update the cached user data
            const cachedData = getCachedUserData(user.uid);
            if (cachedData) {
              console.log('Updating cached user data with new bank details');
              const updatedCachedData = {
                ...cachedData,
                bankDetails: bankDetails,
                bank: bankDetails,
                bankName: bank,
                accountHolder: holder,
                accountNumber: account,
                ifscCode: ifsc,
                updatedAt: new Date()
              };
              cacheUserData(user.uid, updatedCachedData);
            } else {
              // If no cached data, fetch fresh data from Firebase
              refreshUserData(user.uid);
            }

            // Update display elements
            if (displayBankName) displayBankName.textContent = bank;
            if (displayAccountHolder) displayAccountHolder.textContent = holder;
            if (displayAccountNumber) displayAccountNumber.textContent = account;
            if (displayIfscCode) displayIfscCode.textContent = ifsc;

            // Show the bank details display
            const bankDetailsDisplay = document.getElementById('bankDetailsDisplay');
            if (bankDetailsDisplay) {
              bankDetailsDisplay.style.display = 'block';

              // Update the toggle button text
              const toggleBtn = document.getElementById('toggleBankDetails');
              if (toggleBtn) {
                toggleBtn.textContent = 'Hide Bank Details';
              }
            }

            // Show success message
            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: 'Bank details updated successfully',
              confirmButtonColor: '#8b5cf6',
              timer: 3000
            });

          } catch (err) {
            console.error('Error saving bank details:', err);
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: err.message || 'Failed to update bank details. Please try again.',
              confirmButtonColor: '#ef4444'
            });
          }
        });

        // Also add click handler for the save button as backup
        const saveBankDetailsBtn = document.getElementById('saveBankDetailsBtn');
        if (saveBankDetailsBtn) {
          saveBankDetailsBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Save bank details button clicked');

            // Trigger form submission
            const form = document.getElementById('bankDetailsForm');
            if (form) {
              form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
            }
          });
        }
      }
    } else {
      console.warn('Bank details form not found in the DOM');
    }

    // Set up debug functionality for bank details
    const showDebugBtn = document.getElementById('showDebugBtn');
    const bankDetailsDebug = document.getElementById('bankDetailsDebug');
    const bankDetailsDebugInfo = document.getElementById('bankDetailsDebugInfo');

    if (showDebugBtn && bankDetailsDebug) {
      showDebugBtn.addEventListener('click', () => {
        bankDetailsDebug.style.display = 'block';

        // Update debug info
        if (bankDetailsDebugInfo) {
          if (user) {
            bankDetailsDebugInfo.textContent = `User ID: ${user.uid}, Email: ${user.email}`;

            // Try to get form values
            const bankName = document.getElementById('edit-bank')?.value || 'Not found';
            const accountHolder = document.getElementById('edit-holder')?.value || 'Not found';
            const accountNumber = document.getElementById('edit-account')?.value || 'Not found';
            const ifscCode = document.getElementById('edit-ifsc')?.value || 'Not found';

            bankDetailsDebugInfo.textContent += `, Form values: Bank=${bankName}, Holder=${accountHolder}, Account=${accountNumber}, IFSC=${ifscCode}`;
          } else {
            bankDetailsDebugInfo.textContent = 'No user is logged in';
          }
        }
      });
    }

    // Password Update
      // We already have auth from the top of the file
      // const auth = getAuth(); // Removed duplicate declaration

// Add null check for passwordForm
if (passwordForm) {
  passwordForm.addEventListener('submit', async (e) => {
  e.preventDefault();

  const currentPass = passwordForm.querySelector('input[name="currentPassword"]').value.trim();
  const newPass = passwordForm.querySelector('input[name="newPassword"]').value.trim();
  const confirmPass = passwordForm.querySelector('input[name="confirmPassword"]').value.trim();

  if (newPass.length < 6) {
    return showAlert("New password must be at least 6 characters!", false);
  }

  if (newPass !== confirmPass) {
    return showAlert("New passwords do not match!", false);
  }

  try {
    const user = auth.currentUser;
    if (!user) throw new Error("No user is logged in.");

    const credential = EmailAuthProvider.credential(user.email, currentPass);
    await reauthenticateWithCredential(user, credential);  // Secure re-login

    await updatePassword(user, newPass);

    showAlert("Password updated successfully!", true);
    passwordForm.reset();

  } catch (err) {
    showAlert(err.message || "Something went wrong!", false);
  }
  });
}

// Password Visibility Toggle
window.togglePassword = function(el) {
  const input = el.previousElementSibling;
  if (input && input.type === "password") {
    input.type = "text";
    el.querySelector('i').classList.remove('fa-eye');
    el.querySelector('i').classList.add('fa-eye-slash');
  } else if (input) {
    input.type = "password";
    el.querySelector('i').classList.remove('fa-eye-slash');
    el.querySelector('i').classList.add('fa-eye');
  }
}

// Function to ensure wallet data exists in Firebase
async function ensureWalletData(userId, userData) {
  try {
    console.log('Ensuring wallet data exists for user:', userId);
    const userRef = doc(db, "users", userId);

    // Create default wallet structure if it doesn't exist
    const walletData = {
      wallet: {
        balance: 0,
        bonus: 0,
        earning: 0,
        withdrawals: 0
      }
    };

    // If there are existing values, use them
    if (userData.totalEarnings) {
      walletData.wallet.earning = parseFloat(userData.totalEarnings);
    }

    if (userData.totalWithdrawals) {
      walletData.wallet.withdrawals = parseFloat(userData.totalWithdrawals);
    }

    // Update Firebase
    await setDoc(userRef, walletData, { merge: true });
    console.log('Wallet data created successfully');

    // Update local cache
    const cachedData = getCachedUserData(userId);
    if (cachedData) {
      const updatedData = {
        ...cachedData,
        wallet: walletData.wallet
      };
      cacheUserData(userId, updatedData);
    }

    // Refresh the page to show the updated data
    setTimeout(() => {
      refreshUserData(userId);
    }, 1000);

  } catch (error) {
    console.error('Error ensuring wallet data:', error);
  }
}

// Simple Alert function
function showAlert(message, success = true) {
  const div = document.createElement('div');
  div.textContent = message;
  div.className = success ? 'alert success' : 'alert error';
  document.body.appendChild(div);
  setTimeout(() => div.remove(), 4000);
}

    // Logout
    if (logoutButton) {
      logoutButton.addEventListener('click', async () => {
        try {
          await signOut(auth);
          window.location.href = 'index.html';
        } catch (err) {
          showAlert("Logout failed: " + err.message, false);
        }
      });
    }
  });
});
