<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyTube Admin - Users</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/admin-simple.css">
    <link rel="stylesheet" href="css/youtube-theme.css">
    <link rel="stylesheet" href="css/admin-theme.css">
    <meta name="theme-color" content="#FF0000">
</head>
<body class="admin-body">
    <div class="animated-bg"></div>
    <div class="admin-container">
        <!-- Sidebar (hidden on mobile) -->
        <aside class="admin-sidebar">
            <div class="admin-sidebar-header">
                <img src="img/mytube-logo.svg" alt="MyTube Logo">
                <h2>MyTube <span>Admin</span></h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li>
                        <a href="admin-dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="admin-users.html"><i class="fas fa-users"></i> Users</a>
                    </li>
                    <li>
                        <a href="admin-withdrawals.html"><i class="fas fa-money-bill-wave"></i> Withdrawals</a>
                    </li>
                    <li>
                        <a href="admin-transactions.html"><i class="fas fa-exchange-alt"></i> Transactions</a>
                    </li>

                    <li>
                        <a href="admin-settings.html"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                </ul>
            </nav>
            <div class="admin-sidebar-footer">
                <button id="admin-logout-btn" class="admin-logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Top Bar -->
            <header class="admin-topbar">
                <button class="admin-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="admin-search">
                    <input type="text" id="user-search" placeholder="Search users...">
                    <button><i class="fas fa-search"></i></button>
                </div>
                <div class="admin-user-info">
                    <span id="admin-name">Admin</span>
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                </div>
            </header>

            <!-- Users Content -->
            <div class="admin-content">
                <div class="admin-page-header">
                    <h1><i class="fas fa-users"></i> Users</h1>
                    <p>Manage all users</p>
                </div>

                <!-- Stats header removed - only shown on dashboard -->

                <!-- Filters and Actions -->
                <div class="admin-filters">
                    <div class="admin-filter-group">
                        <label for="plan-filter"><i class="fas fa-crown"></i> Filter by Plan:</label>
                        <select id="plan-filter">
                            <option value="all">All Plans</option>
                            <!-- Plans will be loaded dynamically from Firebase -->
                        </select>
                    </div>
                    <div class="admin-filter-group">
                        <label for="status-filter"><i class="fas fa-toggle-on"></i> Filter by Status:</label>
                        <select id="status-filter">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                    <button class="admin-btn" id="export-users-btn">
                        <i class="fas fa-file-export"></i> Export
                    </button>
                    <button class="admin-btn admin-btn-secondary" id="create-index-btn" style="display: none;">
                        <i class="fas fa-database"></i> Create Index
                    </button>
                </div>

                <!-- Users Table -->
                <div class="admin-table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Actions</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Mobile</th>
                                <th>Referral Code</th>
                                <th>Referred By</th>
                                <th>Refers Done</th>
                                <th>Plan</th>
                                <th>Videos</th>
                                <th>Today's Videos</th>
                                <th>Active Days</th>
                                <th>Earnings</th>
                                <th>Earning Wallet</th>
                                <th>Bonus Wallet</th>
                                <th>Main Wallet</th>
                                <th>Joined</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="users-table">
                            <tr>
                                <td colspan="17" class="admin-loading-cell">Loading users...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="admin-pagination">
                    <button id="prev-page" class="admin-pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span id="page-info">Page 1 of 1</span>
                    <button id="next-page" class="admin-pagination-btn" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="admin-bottom-nav">
        <a href="admin-dashboard.html">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="admin-users.html" class="active">
            <i class="fas fa-users"></i>
            <span>Users</span>
        </a>
        <a href="admin-withdrawals.html">
            <i class="fas fa-money-bill-wave"></i>
            <span>Withdrawals</span>
        </a>
        <a href="admin-transactions.html">
            <i class="fas fa-exchange-alt"></i>
            <span>Transactions</span>
        </a>

        <a href="admin-settings.html">
            <i class="fas fa-cog"></i>
            <span>Settings</span>
        </a>
    </nav>

    <!-- User Details Modal -->
    <div id="user-modal" class="admin-modal">
        <div class="admin-modal-content">
            <div class="admin-modal-header">
                <h2><i class="fas fa-user"></i> User Details</h2>
                <button class="admin-modal-close">&times;</button>
            </div>
            <div class="admin-modal-body">
                <!-- Edit User Form (Hidden by default) -->
                <div id="edit-user-form" style="display: none;">
                    <div class="admin-modal-header">
                        <h2><i class="fas fa-user-edit"></i> Edit User</h2>
                    </div>
                    <div class="edit-user-form-grid">
                        <div class="admin-form-group">
                            <label for="edit-user-name"><i class="fas fa-user"></i> Name</label>
                            <input type="text" id="edit-user-name" class="admin-input" placeholder="User Name">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-email"><i class="fas fa-envelope"></i> Email</label>
                            <input type="email" id="edit-user-email" class="admin-input" placeholder="Email Address">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-password"><i class="fas fa-key"></i> Password</label>
                            <input type="text" id="edit-user-password" class="admin-input" placeholder="Set new password">
                            <small class="admin-form-help">Leave empty to keep current password</small>
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-mobile"><i class="fas fa-mobile-alt"></i> Phone Number</label>
                            <input type="text" id="edit-user-mobile" class="admin-input" placeholder="Phone Number">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-referral"><i class="fas fa-qrcode"></i> Referral Code</label>
                            <input type="text" id="edit-user-referral" class="admin-input" placeholder="Referral Code">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-plan"><i class="fas fa-crown"></i> User Plan</label>
                            <select id="edit-user-plan" class="admin-input">
                                <!-- Plans will be loaded dynamically from Firebase -->
                                <option value="">Loading plans...</option>
                            </select>
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-plan-duration"><i class="fas fa-calendar-day"></i> Plan Duration (days)</label>
                            <input type="number" id="edit-user-plan-duration" class="admin-input" value="30" min="1">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-video-duration"><i class="fas fa-video"></i> Custom Video Duration</label>
                            <select id="edit-user-video-duration" class="admin-input">
                                <option value="">Use Default (5 mins)</option>
                                <option value="1">1 Second</option>
                                <option value="30">30 Seconds</option>
                                <option value="60">1 Minute</option>
                                <option value="120">2 Minutes</option>
                                <option value="300">5 Minutes</option>
                            </select>
                            <small class="admin-form-help">Set custom video duration for this user. Default is 5 minutes.</small>
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-wallet"><i class="fas fa-wallet"></i> Main Wallet Balance</label>
                            <input type="number" id="edit-wallet" class="admin-input" placeholder="Main Wallet Balance" step="0.01">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-earning-wallet"><i class="fas fa-coins"></i> Earning Wallet</label>
                            <input type="number" id="edit-earning-wallet" class="admin-input" placeholder="Earning Wallet Balance" step="0.01">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-bonus-wallet"><i class="fas fa-gift"></i> Bonus Wallet</label>
                            <input type="number" id="edit-bonus-wallet" class="admin-input" placeholder="Bonus Wallet Balance" step="0.01">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-earnings"><i class="fas fa-coins"></i> Total Earnings</label>
                            <input type="number" id="edit-earnings" class="admin-input" placeholder="Total Earnings" step="0.01">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-videos"><i class="fas fa-play-circle"></i> Total Videos</label>
                            <input type="number" id="edit-videos" class="admin-input" placeholder="Total Videos">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-today-videos"><i class="fas fa-calendar-day"></i> Today's Videos</label>
                            <input type="number" id="edit-today-videos" class="admin-input" placeholder="Today's Videos">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-active-days"><i class="fas fa-calendar-check"></i> Active Days</label>
                            <input type="number" id="edit-active-days" class="admin-input" placeholder="Active Days">
                        </div>
                        <div class="admin-form-group">
                            <label for="edit-user-referred-by"><i class="fas fa-user-plus"></i> Referred By</label>
                            <input type="text" id="edit-user-referred-by" class="admin-input" placeholder="Referred By Code">
                        </div>
                    </div>
                    <div class="admin-form-group">
                        <label for="edit-user-earnings-per-batch"><i class="fas fa-coins"></i> Earnings Per 100 Videos</label>
                        <input type="number" id="edit-user-earnings-per-batch" class="admin-input" placeholder="Earnings per 100 videos" readonly>
                        <small class="admin-form-help">This value is set based on the selected plan</small>
                    </div>
                    <div class="admin-form-group">
                        <label><i class="fas fa-clock"></i> Short Video Advantage</label>
                        <div class="admin-toggle-container">
                            <input type="checkbox" id="edit-short-video" class="admin-toggle-input">
                            <label for="edit-short-video" class="admin-toggle-label"></label>
                            <span class="admin-toggle-text">Enable 30-second videos for this user</span>
                        </div>
                        <div class="admin-form-group" id="short-video-duration-container" style="display: none;">
                            <label for="short-video-duration-days"><i class="fas fa-calendar-day"></i> Duration (days)</label>
                            <input type="number" id="short-video-duration-days" class="admin-input" value="7" min="1" max="30">
                            <small class="admin-form-help">Short video advantage will be automatically disabled after this many days</small>
                        </div>
                        <div class="admin-info-box">
                            <p><strong>Admin Override:</strong> Settings here will override automatic advantages.</p>
                            <p><i class="fas fa-info-circle"></i> By default, users get short video advantage based on their plan:</p>
                            <ul>
                                <li>Trial: Always (30-second videos)</li>
                                <li>Starter: 7 days of 30-second videos, then 5 minutes</li>
                                <li>Premium: 7 days of 30-second videos, then 5 minutes</li>
                            </ul>
                            <p>Users with 3 successful referrals get permanent 30-second video advantage.</p>
                            <p>Your settings here will take priority over the automatic system.</p>
                        </div>
                    </div>
                    <div class="admin-form-actions">
                        <button id="update-user-btn" class="admin-btn">Update User</button>
                        <button id="cancel-edit-btn" class="admin-btn admin-btn-secondary">Cancel</button>
                    </div>
                </div>

                <!-- Activate Plan Form (Hidden by default) -->
                <div id="activate-plan-form" style="display: none;">
                    <div class="admin-modal-header">
                        <h2><i class="fas fa-crown"></i> Activate Plan</h2>
                    </div>
                    <div class="edit-user-form-grid">
                        <div class="admin-form-group">
                            <label for="plan-select"><i class="fas fa-layer-group"></i> Select Plan</label>
                            <select id="plan-select" class="admin-select">
                                <!-- Plans will be loaded dynamically from Firebase -->
                                <option value="">Loading plans...</option>
                            </select>
                        </div>
                        <div class="admin-form-group">
                            <label for="plan-duration"><i class="fas fa-calendar-day"></i> Duration (days)</label>
                            <input type="number" id="plan-duration" class="admin-input" value="30" min="1">
                        </div>
                    </div>
                    <div class="admin-form-group">
                        <label for="plan-earnings-per-batch"><i class="fas fa-coins"></i> Earnings Per 50 Videos</label>
                        <input type="number" id="plan-earnings-per-batch" class="admin-input" placeholder="Earnings per 50 videos" readonly>
                        <small class="admin-form-help">This value is set based on the selected plan</small>
                    </div>
                    <div class="admin-form-actions">
                        <button id="confirm-plan-btn" class="admin-btn">Activate Plan</button>
                        <button id="cancel-plan-btn" class="admin-btn admin-btn-secondary">Cancel</button>
                    </div>
                </div>
                <div class="admin-user-profile">
                    <div class="admin-user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="admin-user-info-large">
                        <h3 id="modal-user-name">User Name</h3>
                        <p><i class="fas fa-envelope"></i> <span id="modal-user-email"><EMAIL></span></p>
                        <span id="modal-user-status" class="admin-badge">Active</span>
                    </div>
                </div>

                <div class="admin-tabs">
                    <button class="admin-tab-btn active" data-tab="details"><i class="fas fa-info-circle"></i> Details</button>
                    <button class="admin-tab-btn" data-tab="transactions"><i class="fas fa-exchange-alt"></i> Transactions</button>
                    <button class="admin-tab-btn" data-tab="withdrawals"><i class="fas fa-money-bill-wave"></i> Withdrawals</button>
                </div>

                <div id="details-tab" class="admin-tab-content active">
                    <div class="admin-detail-grid">
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-fingerprint"></i> User ID</span>
                            <span id="modal-user-id" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-mobile-alt"></i> Phone Number</span>
                            <span id="modal-user-mobile" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-crown"></i> Plan</span>
                            <span id="modal-user-plan" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-hourglass-end"></i> Plan Expiry</span>
                            <span id="modal-plan-expiry" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-qrcode"></i> Referral Code</span>
                            <span id="modal-user-referral-code" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-user-plus"></i> Referred By</span>
                            <span id="modal-user-referred-by" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-users"></i> Refers Done</span>
                            <span id="modal-user-refers-done" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-play-circle"></i> Total Videos</span>
                            <span id="modal-user-videos" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-calendar-day"></i> Today's Videos</span>
                            <span id="modal-user-today-videos" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-calendar-check"></i> Active Days</span>
                            <span id="modal-user-active-days" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-coins"></i> Total Earnings</span>
                            <span id="modal-user-earnings" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-coins"></i> Earning Wallet</span>
                            <span id="modal-user-earning-wallet" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-gift"></i> Bonus Wallet</span>
                            <span id="modal-user-bonus-wallet" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-wallet"></i> Main Wallet</span>
                            <span id="modal-user-wallet-balance" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-calendar-alt"></i> Joined Date</span>
                            <span id="modal-user-joined" class="admin-detail-value">Loading...</span>
                        </div>
                        <div class="admin-detail-item">
                            <span class="admin-detail-label"><i class="fas fa-sign-in-alt"></i> Last Login</span>
                            <span id="modal-user-last-login" class="admin-detail-value">Loading...</span>
                        </div>
                    </div>

                    <div class="admin-user-actions">
                        <button id="edit-user-btn" class="admin-btn">
                            <i class="fas fa-edit"></i> Edit User
                        </button>
                        <button id="activate-plan-btn" class="admin-btn admin-btn-secondary">
                            <i class="fas fa-crown"></i> Activate Plan
                        </button>
                        <button id="process-referral-btn" class="admin-btn admin-btn-success">
                            <i class="fas fa-user-plus"></i> Process Referral Bonus
                        </button>
                        <button id="block-user-btn" class="admin-btn admin-btn-warning">
                            <i class="fas fa-ban"></i> Block User
                        </button>
                        <button id="unblock-user-btn" class="admin-btn admin-btn-success" style="display: none;">
                            <i class="fas fa-check"></i> Unblock User
                        </button>
                    </div>
                </div>

                <div id="transactions-tab" class="admin-tab-content">
                    <div class="admin-table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="user-transactions-table">
                                <tr>
                                    <td colspan="4" class="admin-loading-cell">Loading transactions...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="withdrawals-tab" class="admin-tab-content">
                    <div class="admin-table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Bank</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="user-withdrawals-table">
                                <tr>
                                    <td colspan="4" class="admin-loading-cell">Loading withdrawals...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="admin-loading-overlay">
        <div class="admin-spinner"></div>
        <p>Loading users data...</p>
    </div>

    <!-- Firebase Scripts -->
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js"></script>

    <!-- SweetAlert2 for nice alerts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- SheetJS for Excel Export -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

    <!-- Export Diagnostic Script -->
    <script src="js/export-diagnostic.js"></script>

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Admin Scripts -->
    <script type="module" src="js/admin-auth.js"></script>
    <script type="module" src="js/admin-users.js"></script>
    <script type="module" src="js/admin-mobile.js"></script>

    <!-- Mobile Meta Tags -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#FF0000">

    <!-- Theme Management -->
    <script type="module">
        import { applyTheme } from './js/admin-theme.js';

        // Apply YouTube theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            applyTheme('youtube');
        });
    </script>
</body>
</html>
