<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sync Plans</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #ff0000;
        }
        button {
            background-color: #ff0000;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 20px;
        }
        .plan-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .plan-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Sync Plans with Website</h1>
    <p>This tool will ensure the plans in Firebase match the website by creating or updating the Trial, Starter, and Premium plans.</p>
    
    <div id="plans-container">Loading plans...</div>
    
    <button id="sync-button">Sync Plans</button>
    <div id="result"></div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
        import { getFirestore, collection, getDocs, doc, setDoc, query, where } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

        // Your Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyAMMabAMVSrNzKdTEVOcn4Ffh7oDVxD2EM",
            authDomain: "mytube-earnings.firebaseapp.com",
            projectId: "mytube-earnings",
            storageBucket: "mytube-earnings.firebasestorage.app",
            messagingSenderId: "116772605182",
            appId: "1:116772605182:web:a5e9875d48867cca03e9af",
            measurementId: "G-MR5HFN8J4V"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        
        // Standard plan definitions from the website
        const standardPlans = [
            {
                name: 'Trial',
                price: 0,
                duration: 2,
                videoQuality: 'Standard',
                earningsPerBatch: 10,
                referralBonus: 0,
                referralVideosCount: 0,
                referralWalletBonus: 0,
                shortVideoDays: 2,
                description: 'Free trial plan for new users',
                active: true
            },
            {
                name: 'Starter',
                price: 499,
                duration: 30,
                videoQuality: 'HD',
                earningsPerBatch: 25,
                referralBonus: 50,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7,
                description: 'Basic plan for new video watchers',
                active: true
            },
            {
                name: 'Premium',
                price: 2999,
                duration: 30,
                videoQuality: 'Full HD',
                earningsPerBatch: 150,
                referralBonus: 300,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7,
                description: 'Premium plan with advanced features',
                active: true
            },
            {
                name: 'Elite',
                price: 4999,
                duration: 30,
                videoQuality: 'Ultra HD',
                earningsPerBatch: 250,
                referralBonus: 500,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7,
                description: 'Elite plan with high volume video watching',
                active: true
            },
            {
                name: 'Ultimate',
                price: 9999,
                duration: 30,
                videoQuality: 'Ultra HD+',
                earningsPerBatch: 500,
                referralBonus: 1000,
                referralVideosCount: 50,
                referralWalletBonus: 0,
                shortVideoDays: 7,
                description: 'Ultimate plan with maximum earnings',
                active: true
            }
        ];

        // Get plans from Firestore
        async function getPlans() {
            const plansContainer = document.getElementById('plans-container');
            
            try {
                const plansCollection = collection(db, 'plans');
                const plansSnapshot = await getDocs(plansCollection);

                if (plansSnapshot.empty) {
                    plansContainer.innerHTML = '<p>No plans found in Firebase.</p>';
                    return [];
                }

                let plansHTML = '';
                const plans = [];

                plansSnapshot.forEach((doc) => {
                    const plan = { id: doc.id, ...doc.data() };
                    plans.push(plan);
                    
                    // Create HTML for each plan
                    plansHTML += `
                        <div class="plan-card">
                            <div class="plan-title">${plan.name || 'Unnamed Plan'}</div>
                            <p><strong>Price:</strong> ₹${plan.price || 0}</p>
                            <p><strong>Duration:</strong> ${plan.duration || 0} days</p>
                            <p><strong>Earnings per Batch:</strong> ₹${plan.earningsPerBatch || 0}</p>
                            <p><strong>Video Quality:</strong> ${plan.videoQuality || 'Not set'}</p>
                            <p><strong>Referral Bonus:</strong> ₹${plan.referralBonus || 0}</p>
                            <p><strong>Referral Videos Count:</strong> ${plan.referralVideosCount || 0}</p>
                            <p><strong>Referral Wallet Bonus:</strong> ₹${plan.referralWalletBonus || 0}</p>
                            <p><strong>Short Video Days:</strong> ${plan.shortVideoDays || 0}</p>
                        </div>
                    `;
                });

                plansContainer.innerHTML = plansHTML;
                return plans;
            } catch (error) {
                console.error('Error getting plans:', error);
                plansContainer.innerHTML = `<p class="error">Error getting plans: ${error.message}</p>`;
                return [];
            }
        }

        // Sync plans with standard definitions
        async function syncPlans() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Syncing plans...</p>';
            
            try {
                let createdCount = 0;
                let updatedCount = 0;
                
                for (const standardPlan of standardPlans) {
                    // Check if plan exists
                    const planQuery = query(collection(db, 'plans'), where('name', '==', standardPlan.name));
                    const planSnapshot = await getDocs(planQuery);
                    
                    if (planSnapshot.empty) {
                        // Create new plan
                        const newPlanRef = doc(collection(db, 'plans'));
                        await setDoc(newPlanRef, {
                            ...standardPlan,
                            createdAt: new Date(),
                        });
                        createdCount++;
                    } else {
                        // Update existing plan
                        const planDoc = planSnapshot.docs[0];
                        await setDoc(doc(db, 'plans', planDoc.id), {
                            ...standardPlan,
                            updatedAt: new Date(),
                        }, { merge: true });
                        updatedCount++;
                    }
                }
                
                resultDiv.innerHTML = `<p class="success">Successfully synced plans: ${createdCount} created, ${updatedCount} updated. Please refresh the page to see the changes.</p>`;
                
                // Add refresh button
                const refreshButton = document.createElement('button');
                refreshButton.textContent = 'Refresh Page';
                refreshButton.onclick = () => location.reload();
                resultDiv.appendChild(refreshButton);
                
            } catch (error) {
                console.error('Error syncing plans:', error);
                resultDiv.innerHTML = `<p class="error">Error syncing plans: ${error.message}</p>`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            await getPlans();
            
            // Add event listener to sync button
            document.getElementById('sync-button').addEventListener('click', async () => {
                await syncPlans();
            });
        });
    </script>
</body>
</html>
