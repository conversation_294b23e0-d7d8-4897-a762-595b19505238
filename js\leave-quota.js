// User Leave Quota System
// This module handles user leave applications and quota management

import {
  getFirestore,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  addDoc,
  query,
  where,
  orderBy,
  getDocs,
  serverTimestamp
} from "https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js";

import { getAuth } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
import { db, auth } from "./firebase-config.js";
import { isLeaveDay } from "./leave-system.js";

// Constants for leave quotas by plan type
const LEAVE_QUOTAS = {
  'Trial': 0,     // Trial users cannot apply for leave
  'Starter': 4,   // Starter users get 4 days of leave
  'Premium': 4,   // Premium users get 4 days of leave
  'Elite': 4,     // Elite users get 4 days of leave
  'Ultimate': 4   // Ultimate users get 4 days of leave
};

// Cache for user leave data
const userLeaveCache = new Map();

// Constants for localStorage cache
const LEAVE_QUOTA_CACHE_PREFIX = 'leave_quota_';
const LEAVE_QUOTA_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Save leave quota data to localStorage
 * @param {string} userId - The user's ID
 * @param {Object} quotaData - The quota data to save
 */
function saveLeaveQuotaToCache(userId, quotaData) {
  try {
    const cacheKey = `${LEAVE_QUOTA_CACHE_PREFIX}${userId}`;
    const cacheData = {
      data: quotaData,
      timestamp: Date.now(),
      date: new Date().toISOString().split('T')[0] // Current date in YYYY-MM-DD format
    };

    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    console.log('Saved leave quota to localStorage cache:', quotaData);

    // Also update in-memory cache
    userLeaveCache.set(userId, quotaData);
  } catch (error) {
    console.error('Error saving leave quota to localStorage:', error);
  }
}

/**
 * Get leave quota data from localStorage
 * @param {string} userId - The user's ID
 * @returns {Object|null} - The cached quota data or null if not found or expired
 */
function getLeaveQuotaFromCache(userId) {
  try {
    const cacheKey = `${LEAVE_QUOTA_CACHE_PREFIX}${userId}`;
    const cachedDataStr = localStorage.getItem(cacheKey);

    if (!cachedDataStr) {
      console.log('No cached leave quota found in localStorage');
      return null;
    }

    const cachedData = JSON.parse(cachedDataStr);

    // Check if cache is expired
    const cacheAge = Date.now() - cachedData.timestamp;
    if (cacheAge > LEAVE_QUOTA_CACHE_TTL) {
      console.log('Cached leave quota is expired, invalidating');
      localStorage.removeItem(cacheKey);
      return null;
    }

    console.log('Using cached leave quota from localStorage:', cachedData.data);

    // Update in-memory cache
    userLeaveCache.set(userId, cachedData.data);

    return cachedData.data;
  } catch (error) {
    console.error('Error getting leave quota from localStorage:', error);
    return null;
  }
}

/**
 * Invalidate leave quota cache
 * @param {string} userId - The user's ID
 */
function invalidateLeaveQuotaCache(userId) {
  try {
    const cacheKey = `${LEAVE_QUOTA_CACHE_PREFIX}${userId}`;
    localStorage.removeItem(cacheKey);
    userLeaveCache.delete(userId);
    console.log('Invalidated leave quota cache for user:', userId);
  } catch (error) {
    console.error('Error invalidating leave quota cache:', error);
  }
}

/**
 * Get user's leave quota based on their plan
 * @param {string} planType - The user's plan type (Trial, Starter, Premium)
 * @returns {number} - Number of leave days allowed
 */
function getLeaveQuotaForPlan(planType) {
  return LEAVE_QUOTAS[planType] || 0;
}

/**
 * Get user's current leave quota and usage
 * @param {string} userId - The user's ID
 * @returns {Promise<Object>} - Object containing quota, used, and remaining leave days
 */
async function getUserLeaveQuota(userId) {
  try {
    // Check in-memory cache first (fastest)
    if (userLeaveCache.has(userId)) {
      console.log('Using in-memory cached leave quota');
      return userLeaveCache.get(userId);
    }

    // Check localStorage cache next
    const cachedQuota = getLeaveQuotaFromCache(userId);
    if (cachedQuota) {
      console.log('Using localStorage cached leave quota');
      return cachedQuota;
    }

    console.log('No cached leave quota found, fetching from Firebase');

    // Get user document to check plan type
    const userDoc = await getDoc(doc(db, "users", userId));
    if (!userDoc.exists()) {
      throw new Error("User not found");
    }

    const userData = userDoc.data();
    const planType = userData.planType || 'Trial';

    // Get total quota based on plan
    const totalQuota = getLeaveQuotaForPlan(planType);

    // Get leave records to calculate used quota
    const leaveRef = collection(db, "userLeaves"); // Changed from "userLeave" to "userLeaves" to match user-leave.js
    const leaveQuery = query(
      leaveRef,
      where("userId", "==", userId),
      where("status", "==", "approved"),
      where("leaveDate", ">=", userData.planStartDate || new Date(0))
    );

    const leaveSnapshot = await getDocs(leaveQuery);

    // Use a Map to track unique leave dates to prevent counting duplicates
    const uniqueLeaveDatesMap = new Map();

    leaveSnapshot.forEach(doc => {
      const leave = doc.data();
      // Store only one entry per date to avoid counting duplicates
      uniqueLeaveDatesMap.set(leave.leaveDate, doc.id);
    });

    // Count unique leave dates instead of total documents
    const usedLeave = uniqueLeaveDatesMap.size;

    console.log(`Leave quota calculation: Total documents=${leaveSnapshot.size}, Unique dates=${usedLeave}`);
    if (leaveSnapshot.size > usedLeave) {
      console.log('Detected duplicate leave entries that were properly deduplicated');
    }

    // Calculate remaining quota
    const remainingQuota = Math.max(0, totalQuota - usedLeave);

    // Create result object
    const result = {
      totalQuota,
      usedLeave,
      remainingQuota,
      planType
    };

    // Cache the result in both in-memory and localStorage
    userLeaveCache.set(userId, result);
    saveLeaveQuotaToCache(userId, result);

    console.log('Cached leave quota data:', result);

    return result;
  } catch (error) {
    console.error("Error getting user leave quota:", error);
    throw error;
  }
}

/**
 * Apply for leave on a specific date
 * @param {string} userId - The user's ID
 * @param {Date} leaveDate - The date for leave application
 * @param {string} reason - Reason for leave (optional)
 * @returns {Promise<Object>} - Result of leave application
 */
async function applyForLeave(userId, leaveDate, reason = "") {
  try {
    // Format date to YYYY-MM-DD for consistent storage
    const formattedDate = leaveDate.toISOString().split('T')[0];

    // Check if the date is already a system leave day
    const isSystemLeave = await isLeaveDay(new Date(formattedDate));
    if (isSystemLeave) {
      return {
        success: false,
        message: "This date is already a system leave day. You don't need to apply for leave."
      };
    }

    // Check if user already has leave for this date
    const leaveRef = collection(db, "userLeaves"); // Changed from "userLeave" to "userLeaves" to match user-leave.js
    const existingLeaveQuery = query(
      leaveRef,
      where("userId", "==", userId),
      where("leaveDate", "==", formattedDate) // Changed from "formattedDate" to "leaveDate" to match user-leave.js
    );

    const existingLeaveSnapshot = await getDocs(existingLeaveQuery);
    if (!existingLeaveSnapshot.empty) {
      console.log(`User already has ${existingLeaveSnapshot.size} leave application(s) for date ${formattedDate}`);

      // Check if there are multiple leave applications for this date
      if (existingLeaveSnapshot.size > 1) {
        console.warn(`Found ${existingLeaveSnapshot.size} leave applications for the same date: ${formattedDate}`);

        // Collect all leave applications for this date
        const existingLeaves = [];
        existingLeaveSnapshot.forEach(doc => {
          existingLeaves.push({
            id: doc.id,
            ...doc.data()
          });
        });

        // Sort by status priority (approved > pending > rejected > others)
        existingLeaves.sort((a, b) => {
          const statusPriority = {
            'approved': 3,
            'pending': 2,
            'rejected': 1
          };

          const aPriority = statusPriority[a.status] || 0;
          const bPriority = statusPriority[b.status] || 0;

          return bPriority - aPriority;
        });

        // Keep the first one (highest priority) and clean up the rest
        const keepLeave = existingLeaves[0];
        console.log(`Keeping leave application with status ${keepLeave.status} for date ${formattedDate}, ID: ${keepLeave.id}`);

        // Delete the duplicate leave applications
        for (let i = 1; i < existingLeaves.length; i++) {
          try {
            const duplicateLeaveRef = doc(db, "userLeaves", existingLeaves[i].id);
            await deleteDoc(duplicateLeaveRef);
            console.log(`Deleted duplicate leave application with ID: ${existingLeaves[i].id}`);
          } catch (deleteError) {
            console.error(`Error deleting duplicate leave application: ${deleteError}`);
          }
        }
      }

      return {
        success: false,
        message: "You already have leave applied for this date."
      };
    }

    // Get user's leave quota
    const quotaInfo = await getUserLeaveQuota(userId);

    // Check if user has remaining quota
    if (quotaInfo.remainingQuota <= 0) {
      return {
        success: false,
        message: `You have used all your leave quota (${quotaInfo.totalQuota} days) for your ${quotaInfo.planType} plan.`
      };
    }

    // Auto-approve the leave since user has quota
    const leaveData = {
      userId,
      leaveDate: formattedDate, // Store as string to match user-leave.js
      reason,
      status: "approved",
      appliedAt: serverTimestamp(),
      isAdminCreated: false,
      // Get user details if available
      userName: "User", // This would ideally be fetched from user profile
      mobileNumber: "", // This would ideally be fetched from user profile
      planType: quotaInfo.planType || "Unknown"
    };

    // Add to userLeaves collection
    await addDoc(collection(db, "userLeaves"), leaveData);

    // Invalidate both in-memory and localStorage cache
    userLeaveCache.delete(userId);
    invalidateLeaveQuotaCache(userId);

    return {
      success: true,
      message: "Leave approved automatically. You have " + (quotaInfo.remainingQuota - 1) + " leave days remaining."
    };
  } catch (error) {
    console.error("Error applying for leave:", error);
    return {
      success: false,
      message: "An error occurred while applying for leave: " + error.message
    };
  }
}

/**
 * Check if user is on leave for a specific date
 * @param {string} userId - The user's ID
 * @param {Date} date - The date to check (defaults to today)
 * @returns {Promise<boolean>} - True if user is on leave
 */
async function isUserOnLeave(userId, date = new Date()) {
  try {
    // Format date to YYYY-MM-DD for consistent comparison
    const formattedDate = date.toISOString().split('T')[0];

    // Query userLeaves collection
    const leaveRef = collection(db, "userLeaves");
    const leaveQuery = query(
      leaveRef,
      where("userId", "==", userId),
      where("leaveDate", "==", formattedDate),
      where("status", "==", "approved")
    );

    const leaveSnapshot = await getDocs(leaveQuery);

    // Check if there are any approved leave applications
    if (leaveSnapshot.empty) {
      return false;
    }

    // Check if there are multiple approved leave applications for this date
    if (leaveSnapshot.size > 1) {
      console.warn(`Found ${leaveSnapshot.size} approved leave applications for the same date: ${formattedDate}`);

      // This is a data inconsistency that should be cleaned up
      // For now, we'll just return true since there is at least one approved leave
      return true;
    }

    return true;
  } catch (error) {
    console.error("Error checking if user is on leave:", error);
    return false;
  }
}

/**
 * Get user's leave history
 * @param {string} userId - The user's ID
 * @returns {Promise<Array>} - Array of leave records
 */
async function getUserLeaveHistory(userId) {
  try {
    const leaveRef = collection(db, "userLeaves");
    const leaveQuery = query(
      leaveRef,
      where("userId", "==", userId),
      orderBy("leaveDate", "desc")
    );

    const leaveSnapshot = await getDocs(leaveQuery);

    // Use a Map to deduplicate leave applications by date
    // This ensures we only return one leave application per date
    const leavesByDate = new Map();

    leaveSnapshot.forEach(doc => {
      const leave = {
        id: doc.id,
        ...doc.data()
      };

      // If we already have a leave for this date, only replace it if the new one has higher priority
      if (leavesByDate.has(leave.leaveDate)) {
        const existingLeave = leavesByDate.get(leave.leaveDate);

        // Determine status priority (approved > pending > rejected > others)
        const statusPriority = {
          'approved': 3,
          'pending': 2,
          'rejected': 1
        };

        const existingPriority = statusPriority[existingLeave.status] || 0;
        const newPriority = statusPriority[leave.status] || 0;

        // Only replace if the new leave has higher priority
        if (newPriority > existingPriority) {
          leavesByDate.set(leave.leaveDate, leave);
        }
      } else {
        // No existing leave for this date, add it
        leavesByDate.set(leave.leaveDate, leave);
      }
    });

    // Convert the Map values to an array
    const leaveHistory = Array.from(leavesByDate.values());

    // Sort by leave date (newest first)
    leaveHistory.sort((a, b) => {
      return new Date(b.leaveDate) - new Date(a.leaveDate);
    });

    // Log if we found and deduplicated any entries
    if (leaveSnapshot.size > leaveHistory.length) {
      console.log(`Deduplicated leave history: ${leaveSnapshot.size} total entries, ${leaveHistory.length} unique dates`);
    }

    return leaveHistory;
  } catch (error) {
    console.error("Error getting user leave history:", error);
    return [];
  }
}

/**
 * Cancel a previously approved leave
 * @param {string} userId - The user's ID
 * @param {string} leaveId - The leave document ID
 * @returns {Promise<Object>} - Result of cancellation
 */
async function cancelLeave(userId, leaveId) {
  try {
    // Get the leave document
    const leaveDoc = await getDoc(doc(db, "userLeaves", leaveId));

    if (!leaveDoc.exists()) {
      return {
        success: false,
        message: "Leave record not found."
      };
    }

    const leaveData = leaveDoc.data();

    // Check if this leave belongs to the user
    if (leaveData.userId !== userId) {
      return {
        success: false,
        message: "You don't have permission to cancel this leave."
      };
    }

    // Check if leave date is in the future
    const leaveDate = leaveData.leaveDate.toDate();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (leaveDate < today) {
      return {
        success: false,
        message: "Cannot cancel past leave."
      };
    }

    // Update leave status to cancelled
    await updateDoc(doc(db, "userLeaves", leaveId), {
      status: "cancelled",
      cancelledAt: serverTimestamp()
    });

    // Invalidate both in-memory and localStorage cache
    userLeaveCache.delete(userId);
    invalidateLeaveQuotaCache(userId);

    return {
      success: true,
      message: "Leave cancelled successfully. The day has been added back to your quota."
    };
  } catch (error) {
    console.error("Error cancelling leave:", error);
    return {
      success: false,
      message: "An error occurred while cancelling leave: " + error.message
    };
  }
}

/**
 * Clean up duplicate leave entries for a user
 * @param {string} userId - The user's ID
 * @returns {Promise<Object>} - Result of cleanup operation
 */
async function cleanupDuplicateLeaves(userId) {
  try {
    console.log(`Starting cleanup of duplicate leave entries for user: ${userId}`);

    // Query all leave applications for the user
    const leavesRef = collection(db, "userLeaves");
    const q = query(
      leavesRef,
      where("userId", "==", userId)
    );

    const querySnapshot = await getDocs(q);
    console.log(`Found ${querySnapshot.size} total leave applications`);

    // Group leaves by date
    const leavesByDate = {};

    querySnapshot.forEach(doc => {
      const leave = {
        id: doc.id,
        ...doc.data()
      };

      if (!leavesByDate[leave.leaveDate]) {
        leavesByDate[leave.leaveDate] = [];
      }

      leavesByDate[leave.leaveDate].push(leave);
    });

    // Find dates with multiple leave applications
    const duplicatesToRemove = [];
    const keptLeaves = [];

    for (const [date, leaves] of Object.entries(leavesByDate)) {
      if (leaves.length > 1) {
        console.warn(`Found ${leaves.length} leave applications for date ${date}`);

        // Sort by status priority (approved > pending > rejected > others)
        leaves.sort((a, b) => {
          const statusPriority = {
            'approved': 3,
            'pending': 2,
            'rejected': 1
          };

          const aPriority = statusPriority[a.status] || 0;
          const bPriority = statusPriority[b.status] || 0;

          return bPriority - aPriority;
        });

        // Keep the first one (highest priority) and mark others as duplicates
        const keepLeave = leaves[0];
        keptLeaves.push(keepLeave);
        console.log(`Keeping leave application with status ${keepLeave.status} for date ${date}, ID: ${keepLeave.id}`);

        // Add others to duplicates list for removal
        for (let i = 1; i < leaves.length; i++) {
          duplicatesToRemove.push(leaves[i]);
          console.log(`Marking as duplicate: leave for date ${date}, ID: ${leaves[i].id}, status: ${leaves[i].status}`);
        }
      }
    }

    // Delete duplicate leave entries
    let deletedCount = 0;
    for (const duplicate of duplicatesToRemove) {
      try {
        const duplicateRef = doc(db, "userLeaves", duplicate.id);
        await deleteDoc(duplicateRef);
        deletedCount++;
        console.log(`Deleted duplicate leave with ID: ${duplicate.id}`);
      } catch (error) {
        console.error(`Error deleting duplicate leave: ${error}`);
      }
    }

    // Invalidate both in-memory and localStorage cache
    userLeaveCache.delete(userId);
    invalidateLeaveQuotaCache(userId);

    return {
      success: true,
      message: `Cleaned up ${deletedCount} duplicate leave entries`,
      deletedCount,
      keptCount: keptLeaves.length
    };
  } catch (error) {
    console.error("Error cleaning up duplicate leaves:", error);
    return {
      success: false,
      message: "Failed to clean up duplicate leaves: " + error.message
    };
  }
}

// Export functions
export {
  getUserLeaveQuota,
  applyForLeave,
  isUserOnLeave,
  getUserLeaveHistory,
  cancelLeave,
  getLeaveQuotaForPlan,
  cleanupDuplicateLeaves,
  // Cache functions
  saveLeaveQuotaToCache,
  getLeaveQuotaFromCache,
  invalidateLeaveQuotaCache
};
