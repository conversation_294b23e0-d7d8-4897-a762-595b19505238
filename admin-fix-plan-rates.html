<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Fix All Plan Rates - MyTube</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">

    <!-- SweetAlert2 for notifications -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn {
            background: #ff0000;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #cc0000;
        }

        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f5f5f5;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .success {
            border-left: 4px solid #4CAF50;
        }

        .error {
            border-left: 4px solid #F44336;
        }

        .plan-selector {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f5f5f5;
        }

        select, input[type="number"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .progress-container {
            width: 100%;
            background-color: #f1f1f1;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            height: 30px;
            background-color: #4CAF50;
            border-radius: 5px;
            text-align: center;
            line-height: 30px;
            color: white;
            width: 0%;
        }

        .stats-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }

        .stat-card {
            flex: 1;
            min-width: 200px;
            background: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .results-table th, .results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .results-table th {
            background-color: #f2f2f2;
            position: sticky;
            top: 0;
        }

        .results-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .status-updated {
            color: #4CAF50;
        }

        .status-already-correct {
            color: #2196F3;
        }

        .status-error {
            color: #F44336;
        }

        .warning-text {
            color: #ff0000;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="logo">
            <img src="img/logo.png" alt="MyTube Logo">
            <span>Admin</span>
        </div>
        <nav class="admin-nav">
            <a href="admin-dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="admin-users.html"><i class="fas fa-users"></i> Users</a>
            <a href="admin-settings.html"><i class="fas fa-cog"></i> Settings</a>
            <a href="admin-plans.html"><i class="fas fa-list"></i> Plans</a>
            <a href="admin-transactions.html"><i class="fas fa-exchange-alt"></i> Transactions</a>
            <a href="admin-withdrawals.html"><i class="fas fa-money-bill-wave"></i> Withdrawals</a>
            <a href="admin-videos.html"><i class="fas fa-video"></i> Videos</a>
            <a href="#" id="logoutBtn"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </nav>
    </header>

    <main class="container">
        <div class="card">
            <h1>Fix All Plan Rates</h1>
            <p>This tool will update the plan rates for all users in the database to ensure they have the correct rates based on their plan.</p>
            <p>Current plan rates:</p>
            <ul>
                <li><strong>Trial:</strong> 0.1 (₹10 for 100 videos, 5 mins each)</li>
                <li><strong>Starter:</strong> 0.25 (₹25 for 100 videos, 5 mins each)</li>
                <li><strong>Premium:</strong> 1.5 (₹150 for 100 videos, 5 mins each)</li>
                <li><strong>Elite:</strong> 2.5 (₹250 for 100 videos, 5 mins each)</li>
                <li><strong>Ultimate:</strong> 5.0 (₹500 for 100 videos, 5 mins each)</li>
            </ul>

            <div class="warning-text">
                <p>⚠️ WARNING: This operation will update all users in the database. Make sure you have a backup before proceeding.</p>
            </div>

            <div class="plan-selector">
                <h3>Batch Size</h3>
                <p>Set the number of users to process in each batch (default: 100)</p>
                <input type="number" id="batchSize" min="10" max="500" value="100">
            </div>

            <button id="fixAllRatesBtn" class="btn">Fix All Plan Rates</button>

            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar">0%</div>
            </div>

            <div class="stats-container" id="statsContainer" style="display: none;">
                <div class="stat-card">
                    <div class="stat-value" id="totalUsers">0</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="updatedUsers">0</div>
                    <div class="stat-label">Updated Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="alreadyCorrectUsers">0</div>
                    <div class="stat-label">Already Correct</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="errorUsers">0</div>
                    <div class="stat-label">Errors</div>
                </div>
            </div>

            <div id="allResult" class="result">
                <h3>Results:</h3>
                <div id="allResultContent"></div>
            </div>
        </div>

        <div class="card">
            <h2>Fix Plan Rates by Plan</h2>
            <p>This tool allows you to fix plan rates for users with a specific plan.</p>

            <div class="plan-selector">
                <h3>Select Plan</h3>
                <select id="planSelector">
                    <option value="">Select a plan</option>
                    <option value="Trial">Trial</option>
                    <option value="Starter">Starter</option>
                    <option value="Premium">Premium</option>
                </select>

                <h3>Batch Size</h3>
                <p>Set the number of users to process in each batch (default: 100)</p>
                <input type="number" id="planBatchSize" min="10" max="500" value="100">
            </div>

            <button id="fixPlanRatesBtn" class="btn" disabled>Fix Plan Rates</button>

            <div class="progress-container" id="planProgressContainer">
                <div class="progress-bar" id="planProgressBar">0%</div>
            </div>

            <div class="stats-container" id="planStatsContainer" style="display: none;">
                <div class="stat-card">
                    <div class="stat-value" id="planTotalUsers">0</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="planUpdatedUsers">0</div>
                    <div class="stat-label">Updated Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="planAlreadyCorrectUsers">0</div>
                    <div class="stat-label">Already Correct</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="planErrorUsers">0</div>
                    <div class="stat-label">Errors</div>
                </div>
            </div>

            <div id="planResult" class="result">
                <h3>Results:</h3>
                <div id="planResultContent"></div>
            </div>
        </div>
    </main>

    <!-- Firebase Scripts -->
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js"></script>

    <!-- Firebase Config -->
    <script type="module" src="js/firebase-config.js"></script>

    <!-- Admin Auth Check -->
    <script type="module" src="js/admin-auth.js"></script>

    <!-- Fix All Plan Rates Script -->
    <script type="module" src="js/fix-all-plan-rates.js"></script>

    <script type="module">
        import { fixAllPlanRates, fixPlanRatesByPlanName } from './js/fix-all-plan-rates.js';
        import { getAuth, signOut } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js";
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js";
        import { auth } from './js/firebase-config.js';

        // Firebase is already initialized in firebase-config.js

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Get elements
            const fixAllRatesBtn = document.getElementById('fixAllRatesBtn');
            const fixPlanRatesBtn = document.getElementById('fixPlanRatesBtn');
            const planSelector = document.getElementById('planSelector');
            const batchSizeInput = document.getElementById('batchSize');
            const planBatchSizeInput = document.getElementById('planBatchSize');
            const allResult = document.getElementById('allResult');
            const allResultContent = document.getElementById('allResultContent');
            const planResult = document.getElementById('planResult');
            const planResultContent = document.getElementById('planResultContent');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const planProgressContainer = document.getElementById('planProgressContainer');
            const planProgressBar = document.getElementById('planProgressBar');
            const statsContainer = document.getElementById('statsContainer');
            const planStatsContainer = document.getElementById('planStatsContainer');
            const logoutBtn = document.getElementById('logoutBtn');

            // Stats elements
            const totalUsers = document.getElementById('totalUsers');
            const updatedUsers = document.getElementById('updatedUsers');
            const alreadyCorrectUsers = document.getElementById('alreadyCorrectUsers');
            const errorUsers = document.getElementById('errorUsers');
            const planTotalUsers = document.getElementById('planTotalUsers');
            const planUpdatedUsers = document.getElementById('planUpdatedUsers');
            const planAlreadyCorrectUsers = document.getElementById('planAlreadyCorrectUsers');
            const planErrorUsers = document.getElementById('planErrorUsers');

            // Enable/disable fix plan rates button based on selection
            planSelector.addEventListener('change', function() {
                fixPlanRatesBtn.disabled = !planSelector.value;
            });

            // Add event listeners
            fixAllRatesBtn.addEventListener('click', async function() {
                try {
                    // Confirm the operation
                    const result = await Swal.fire({
                        title: 'Are you sure?',
                        text: 'This will update plan rates for ALL users in the database. This operation cannot be undone.',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, update all plan rates',
                        cancelButtonText: 'Cancel'
                    });

                    if (!result.isConfirmed) {
                        return;
                    }

                    // Disable the button
                    fixAllRatesBtn.disabled = true;
                    fixAllRatesBtn.textContent = 'Updating Plan Rates...';

                    // Show progress container
                    progressContainer.style.display = 'block';
                    progressBar.style.width = '0%';
                    progressBar.textContent = '0%';

                    // Hide previous results
                    allResult.style.display = 'none';
                    statsContainer.style.display = 'none';

                    // Get batch size
                    const batchSize = parseInt(batchSizeInput.value) || 100;

                    // Start the operation with admin check bypass
                    const updateResult = await fixAllPlanRates(batchSize, true);

                    // Update progress to 100%
                    progressBar.style.width = '100%';
                    progressBar.textContent = '100%';

                    // Update stats
                    if (updateResult.success && updateResult.stats) {
                        statsContainer.style.display = 'flex';
                        totalUsers.textContent = updateResult.stats.totalUsers;
                        updatedUsers.textContent = updateResult.stats.updatedUsers;
                        alreadyCorrectUsers.textContent = updateResult.stats.alreadyCorrectUsers;
                        errorUsers.textContent = updateResult.stats.errorUsers;
                    }

                    // Display result
                    allResult.style.display = 'block';
                    allResult.className = updateResult.success ? 'result success' : 'result error';

                    let resultHTML = `<p><strong>Status:</strong> ${updateResult.success ? 'Success' : 'Error'}</p>`;
                    resultHTML += `<p><strong>Message:</strong> ${updateResult.message}</p>`;

                    if (updateResult.success && updateResult.results) {
                        // Create a table to display the results
                        resultHTML += `
                            <h4>Detailed Results:</h4>
                            <table class="results-table">
                                <thead>
                                    <tr>
                                        <th>User ID</th>
                                        <th>Plan</th>
                                        <th>Old Rate</th>
                                        <th>New Rate</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        // Add rows for each user
                        updateResult.results.forEach(user => {
                            let statusClass = '';
                            if (user.status === 'updated') {
                                statusClass = 'status-updated';
                            } else if (user.status === 'already_correct') {
                                statusClass = 'status-already-correct';
                            } else if (user.status === 'error') {
                                statusClass = 'status-error';
                            }

                            resultHTML += `
                                <tr>
                                    <td>${user.userId}</td>
                                    <td>${user.planName || 'N/A'}</td>
                                    <td>${user.oldRate !== undefined ? user.oldRate : 'N/A'}</td>
                                    <td>${user.newRate !== undefined ? user.newRate : 'N/A'}</td>
                                    <td class="${statusClass}">${user.status}</td>
                                </tr>
                            `;
                        });

                        resultHTML += `
                                </tbody>
                            </table>
                        `;
                    }

                    allResultContent.innerHTML = resultHTML;

                    // Show success message
                    Swal.fire({
                        icon: updateResult.success ? 'success' : 'error',
                        title: updateResult.success ? 'Success' : 'Error',
                        text: updateResult.message
                    });
                } catch (error) {
                    console.error('Error fixing all plan rates:', error);

                    // Display error
                    allResult.style.display = 'block';
                    allResult.className = 'result error';
                    allResultContent.innerHTML = `
                        <p><strong>Status:</strong> Error</p>
                        <p><strong>Message:</strong> ${error.message}</p>
                    `;

                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'An error occurred while fixing plan rates: ' + error.message
                    });
                } finally {
                    // Re-enable the button
                    fixAllRatesBtn.disabled = false;
                    fixAllRatesBtn.textContent = 'Fix All Plan Rates';
                }
            });

            fixPlanRatesBtn.addEventListener('click', async function() {
                try {
                    // Get selected plan
                    const planName = planSelector.value;

                    if (!planName) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Please select a plan'
                        });
                        return;
                    }

                    // Confirm the operation
                    const result = await Swal.fire({
                        title: 'Are you sure?',
                        text: `This will update plan rates for all users with the ${planName} plan. This operation cannot be undone.`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, update plan rates',
                        cancelButtonText: 'Cancel'
                    });

                    if (!result.isConfirmed) {
                        return;
                    }

                    // Disable the button
                    fixPlanRatesBtn.disabled = true;
                    fixPlanRatesBtn.textContent = 'Updating Plan Rates...';

                    // Show progress container
                    planProgressContainer.style.display = 'block';
                    planProgressBar.style.width = '0%';
                    planProgressBar.textContent = '0%';

                    // Hide previous results
                    planResult.style.display = 'none';
                    planStatsContainer.style.display = 'none';

                    // Get batch size
                    const batchSize = parseInt(planBatchSizeInput.value) || 100;

                    // Start the operation with admin check bypass
                    const updateResult = await fixPlanRatesByPlanName(planName, batchSize, true);

                    // Update progress to 100%
                    planProgressBar.style.width = '100%';
                    planProgressBar.textContent = '100%';

                    // Update stats
                    if (updateResult.success && updateResult.stats) {
                        planStatsContainer.style.display = 'flex';
                        planTotalUsers.textContent = updateResult.stats.totalUsers;
                        planUpdatedUsers.textContent = updateResult.stats.updatedUsers;
                        planAlreadyCorrectUsers.textContent = updateResult.stats.alreadyCorrectUsers;
                        planErrorUsers.textContent = updateResult.stats.errorUsers;
                    }

                    // Display result
                    planResult.style.display = 'block';
                    planResult.className = updateResult.success ? 'result success' : 'result error';

                    let resultHTML = `<p><strong>Status:</strong> ${updateResult.success ? 'Success' : 'Error'}</p>`;
                    resultHTML += `<p><strong>Message:</strong> ${updateResult.message}</p>`;

                    if (updateResult.success && updateResult.results) {
                        // Create a table to display the results
                        resultHTML += `
                            <h4>Detailed Results:</h4>
                            <table class="results-table">
                                <thead>
                                    <tr>
                                        <th>User ID</th>
                                        <th>Plan</th>
                                        <th>Old Rate</th>
                                        <th>New Rate</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        // Add rows for each user
                        updateResult.results.forEach(user => {
                            let statusClass = '';
                            if (user.status === 'updated') {
                                statusClass = 'status-updated';
                            } else if (user.status === 'already_correct') {
                                statusClass = 'status-already-correct';
                            } else if (user.status === 'error') {
                                statusClass = 'status-error';
                            }

                            resultHTML += `
                                <tr>
                                    <td>${user.userId}</td>
                                    <td>${user.planName || 'N/A'}</td>
                                    <td>${user.oldRate !== undefined ? user.oldRate : 'N/A'}</td>
                                    <td>${user.newRate !== undefined ? user.newRate : 'N/A'}</td>
                                    <td class="${statusClass}">${user.status}</td>
                                </tr>
                            `;
                        });

                        resultHTML += `
                                </tbody>
                            </table>
                        `;
                    }

                    planResultContent.innerHTML = resultHTML;

                    // Show success message
                    Swal.fire({
                        icon: updateResult.success ? 'success' : 'error',
                        title: updateResult.success ? 'Success' : 'Error',
                        text: updateResult.message
                    });
                } catch (error) {
                    console.error('Error fixing plan rates:', error);

                    // Display error
                    planResult.style.display = 'block';
                    planResult.className = 'result error';
                    planResultContent.innerHTML = `
                        <p><strong>Status:</strong> Error</p>
                        <p><strong>Message:</strong> ${error.message}</p>
                    `;

                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'An error occurred while fixing plan rates: ' + error.message
                    });
                } finally {
                    // Re-enable the button
                    fixPlanRatesBtn.disabled = false;
                    fixPlanRatesBtn.textContent = 'Fix Plan Rates';
                }
            });

            // Logout button
            logoutBtn.addEventListener('click', function() {
                signOut(auth).then(() => {
                    window.location.href = 'admin-login.html';
                }).catch((error) => {
                    console.error('Error signing out:', error);
                });
            });
        });
    </script>
</body>
</html>
